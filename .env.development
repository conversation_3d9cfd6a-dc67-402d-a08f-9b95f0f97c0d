# Development Environment Configuration
# AI Service Development Settings

# Application Settings
APP_ENV=development
APP_VERSION=1.0.0-dev
APP_NAME=AI Service (Dev)
DEBUG=true
TESTING=false

# Server Settings
HOST=127.0.0.1
PORT=8000

# Logging
LOG_LEVEL=DEBUG

# Security Settings (Development - Less Restrictive)
SECRET_KEY=dev-secret-key-not-for-production
API_KEY_HEADER=X-API-Key
ENABLE_RATE_LIMITING=false
MAX_REQUESTS_PER_MINUTE=1000
MAX_REQUESTS_PER_HOUR=10000

# CORS Settings (Allow all for development)
CORS_ORIGINS=["*"]

# Monitoring Settings
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=60

# Performance Settings
MAX_WORKERS=4
REQUEST_TIMEOUT=300
MAX_REQUEST_SIZE=10485760

# Cache Settings
ENABLE_CACHING=true
CACHE_TTL=1800
MAX_CACHE_SIZE=1000

# AI Model Settings
DEFAULT_MODEL=gemini-2.0-flash
DEFAULT_EMBEDDING_MODEL=gemini-embedding-exp-03-07

# Google AI Studio API Key (REQUIRED)
GOOGLE_API_KEY=your-google-ai-studio-api-key

# File Storage
TEMP_FILE_DIR=temp_files
