version: '3.8'

services:
  ai-service:
    build:
      context: .
      dockerfile: Dockerfile.production
    ports:
      - "8000:8000"
      - "9090:9090"  # Metrics port
    environment:
      - APP_ENV=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      - ENABLE_METRICS=true
      - ENABLE_RATE_LIMITING=true
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
      - /tmp/ai_service:/tmp/ai_service
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - ai-service-network

  # Optional: Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-service-network
    depends_on:
      - ai-service

  # Optional: Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-service-network
    depends_on:
      - prometheus

volumes:
  prometheus_data:
  grafana_data:

networks:
  ai-service-network:
    driver: bridge
