# 🚀 Production Deployment Guide

## Phase 5: Production Readiness & Monitoring

This guide covers deploying the AI Service with comprehensive production readiness features including health checks, monitoring, security, and observability.

---

## 📋 Table of Contents

1. [Production Features Overview](#production-features-overview)
2. [Prerequisites](#prerequisites)
3. [Environment Configuration](#environment-configuration)
4. [Deployment Options](#deployment-options)
5. [Monitoring & Observability](#monitoring--observability)
6. [Security Configuration](#security-configuration)
7. [Health Checks](#health-checks)
8. [Performance Tuning](#performance-tuning)
9. [Troubleshooting](#troubleshooting)

---

## 🎯 Production Features Overview

### ✅ Implemented Features

**Health Checks & Monitoring:**
- Comprehensive health check system with multiple checks
- Kubernetes-compatible liveness and readiness probes
- Prometheus metrics integration
- Real-time system resource monitoring
- Request performance tracking

**Security Enhancements:**
- Rate limiting with configurable thresholds
- Input validation and sanitization
- API key management and validation
- IP reputation checking
- Security event logging and audit trails

**Observability:**
- Structured JSON logging with correlation IDs
- Request tracing and performance metrics
- Error tracking and alerting
- Business metrics and analytics

**Configuration Management:**
- Environment-specific configurations
- Feature flags for gradual rollouts
- Secrets management integration
- Dynamic configuration updates

---

## 🔧 Prerequisites

### System Requirements
- **Python**: 3.12+
- **Memory**: Minimum 2GB RAM (4GB recommended)
- **CPU**: 2+ cores recommended
- **Disk**: 10GB+ free space
- **Network**: Stable internet connection for AI API calls

### Dependencies
```bash
# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y curl gcc g++ python3-dev

# Install Python dependencies
pip install -r requirements.txt
```

### Required API Keys
- **Google AI Studio API Key**: Required for AI operations
- **Secret Key**: 32+ character secure key for production

---

## ⚙️ Environment Configuration

### Production Environment (.env.production)

```bash
# Application Settings
APP_ENV=production
APP_VERSION=1.0.0
DEBUG=false

# Security (CHANGE THESE!)
SECRET_KEY=your-super-secure-secret-key-32-chars-min
GOOGLE_API_KEY=your-google-ai-studio-api-key

# Performance
MAX_WORKERS=8
MAX_REQUESTS_PER_MINUTE=100
MAX_REQUESTS_PER_HOUR=2000

# Monitoring
ENABLE_METRICS=true
HEALTH_CHECK_INTERVAL=30

# CORS (Specify your domains)
CORS_ORIGINS=["https://yourdomain.com"]
```

### Development Environment (.env.development)

```bash
# Application Settings
APP_ENV=development
DEBUG=true
ENABLE_RATE_LIMITING=false
CORS_ORIGINS=["*"]
```

---

## 🚀 Deployment Options

### Option 1: Docker Deployment (Recommended)

**1. Build Production Image:**
```bash
docker build -f Dockerfile.production -t ai-service:latest .
```

**2. Run with Docker Compose:**
```bash
# Copy and configure environment
cp .env.production .env

# Start services
docker-compose -f docker-compose.production.yml up -d
```

**3. Verify Deployment:**
```bash
# Check health
curl http://localhost:8000/api/v1/health/live

# Check metrics
curl http://localhost:9090/metrics
```

### Option 2: Direct Python Deployment

**1. Install Dependencies:**
```bash
pip install -r requirements.txt
```

**2. Configure Environment:**
```bash
export APP_ENV=production
export GOOGLE_API_KEY=your-api-key
export SECRET_KEY=your-secure-secret-key
```

**3. Run Application:**
```bash
python -m uvicorn ai_service.main:app \
  --host 0.0.0.0 \
  --port 8000 \
  --workers 4
```

### Option 3: Kubernetes Deployment

**1. Create ConfigMap:**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-service-config
data:
  APP_ENV: "production"
  ENABLE_METRICS: "true"
  LOG_LEVEL: "INFO"
```

**2. Create Secret:**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ai-service-secrets
type: Opaque
stringData:
  GOOGLE_API_KEY: "your-api-key"
  SECRET_KEY: "your-secret-key"
```

**3. Deploy Service:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-service
  template:
    metadata:
      labels:
        app: ai-service
    spec:
      containers:
      - name: ai-service
        image: ai-service:latest
        ports:
        - containerPort: 8000
        - containerPort: 9090
        envFrom:
        - configMapRef:
            name: ai-service-config
        - secretRef:
            name: ai-service-secrets
        livenessProbe:
          httpGet:
            path: /api/v1/health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

---

## 📊 Monitoring & Observability

### Health Check Endpoints

| Endpoint | Purpose | Expected Response |
|----------|---------|-------------------|
| `/api/v1/health/live` | Liveness probe | 200 if alive |
| `/api/v1/health/ready` | Readiness probe | 200 if ready |
| `/api/v1/health/` | Comprehensive health | 200/503 with details |
| `/api/v1/health/status` | Quick status | Service status summary |
| `/api/v1/health/metrics` | Prometheus metrics | Metrics in Prometheus format |

### Metrics Collection

**System Metrics:**
- CPU usage percentage
- Memory usage and availability
- Disk space utilization
- Network I/O statistics

**Application Metrics:**
- Request count and rate
- Response time percentiles
- Error rate and types
- Active request count

**Business Metrics:**
- API usage by endpoint
- Model usage statistics
- Token consumption
- Feature adoption rates

### Logging Structure

**Log Format (JSON):**
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "logger": "ai_service.main",
  "message": "Request completed",
  "request_id": "req-123-456",
  "correlation_id": "corr-789",
  "event_type": "request_completed",
  "status_code": 200,
  "duration_ms": 150.5,
  "endpoint": "/api/v1/ai/generate"
}
```

---

## 🔒 Security Configuration

### Rate Limiting

**Configuration:**
```bash
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=100
MAX_REQUESTS_PER_HOUR=2000
```

**Per-Client Limits:**
- 100 requests per minute per IP
- 2000 requests per hour per IP
- Burst limit of 10 requests

### Input Validation

**Automatic Protection Against:**
- XSS attacks (script injection)
- Code injection attempts
- Oversized requests (>10MB default)
- Malformed JSON/data

### API Key Management

**Format:** `sk-your-api-key-here`

**Headers:**
```bash
X-API-Key: sk-your-api-key-here
```

### CORS Configuration

**Production (Restrictive):**
```bash
CORS_ORIGINS=["https://yourdomain.com","https://api.yourdomain.com"]
```

**Development (Permissive):**
```bash
CORS_ORIGINS=["*"]
```

---

## ⚡ Performance Tuning

### Worker Configuration

**Production Settings:**
```bash
MAX_WORKERS=8  # 2x CPU cores
REQUEST_TIMEOUT=300  # 5 minutes
MAX_REQUEST_SIZE=10485760  # 10MB
```

### Caching Configuration

```bash
ENABLE_CACHING=true
CACHE_TTL=3600  # 1 hour
MAX_CACHE_SIZE=10000  # 10k items
```

### Resource Limits

**Docker Limits:**
```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
    reservations:
      memory: 1G
      cpus: '0.5'
```

---

## 🔍 Troubleshooting

### Common Issues

**1. Health Checks Failing**
```bash
# Check logs
docker logs ai-service

# Test individual health checks
curl http://localhost:8000/api/v1/health/check/gemini_api
```

**2. High Memory Usage**
```bash
# Check memory metrics
curl http://localhost:8000/api/v1/health/metrics/summary

# Restart service if needed
docker-compose restart ai-service
```

**3. Rate Limiting Issues**
```bash
# Check rate limit status
curl -H "X-API-Key: your-key" http://localhost:8000/api/v1/health/status

# Adjust limits in environment
export MAX_REQUESTS_PER_MINUTE=200
```

### Log Analysis

**Find Errors:**
```bash
# Docker logs
docker logs ai-service | grep ERROR

# File logs (if using file logging)
grep ERROR /app/logs/ai_service.log
```

**Performance Analysis:**
```bash
# Response time analysis
grep "duration_ms" /app/logs/ai_service.log | jq '.duration_ms'

# Error rate calculation
grep "status_code" /app/logs/ai_service.log | jq '.status_code' | sort | uniq -c
```

---

## 📈 Monitoring Dashboard

### Grafana Dashboard

**Key Metrics to Monitor:**
- Request rate (requests/second)
- Response time (p50, p95, p99)
- Error rate (4xx, 5xx)
- System resources (CPU, Memory, Disk)
- Health check status

### Alerting Rules

**Critical Alerts:**
- Service down (health check fails)
- High error rate (>5% for 5 minutes)
- High response time (>2s p95 for 5 minutes)
- High memory usage (>90% for 10 minutes)

**Warning Alerts:**
- Moderate error rate (>1% for 10 minutes)
- Elevated response time (>1s p95 for 10 minutes)
- High CPU usage (>80% for 15 minutes)

---

## ✅ Production Checklist

**Before Deployment:**
- [ ] Set secure SECRET_KEY (32+ characters)
- [ ] Configure GOOGLE_API_KEY
- [ ] Set restrictive CORS_ORIGINS
- [ ] Enable rate limiting
- [ ] Configure monitoring
- [ ] Set up log aggregation
- [ ] Test health checks
- [ ] Verify security settings

**After Deployment:**
- [ ] Verify all health checks pass
- [ ] Test API endpoints
- [ ] Check metrics collection
- [ ] Verify logging output
- [ ] Test rate limiting
- [ ] Monitor resource usage
- [ ] Set up alerting
- [ ] Document runbooks

---

## 🎯 Next Steps

1. **Phase 6**: Performance Optimization & Scalability
2. **Phase 7**: Advanced Streaming & Real-time Features
3. **Phase 8**: Workflow Orchestration & Automation

For detailed implementation guides, see the respective phase documentation.
