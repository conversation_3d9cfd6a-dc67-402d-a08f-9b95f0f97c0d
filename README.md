# AI Service - Complete Multimodal AI Platform

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Google Gemini](https://img.shields.io/badge/Google-Gemini%20API-orange.svg)](https://ai.google.dev)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![Production](https://img.shields.io/badge/Production-Ready-brightgreen.svg)](#)

**AI Service** là một **complete multimodal AI platform** được xây dựng với Google Gemini API, cung cấp đầy đủ các tính năng AI tiên tiến cho ứng dụng production.

## 🚀 Quick Start

### **Development Setup**
```bash
# Clone repository
git clone <repository-url>
cd ai_service

# Install dependencies
pip install -r requirements.txt

# Configure environment
export GOOGLE_API_KEY="your-gemini-api-key"

# Run development server
./run_app.sh

# API sẽ available tại: http://localhost:8000
# API Documentation: http://localhost:8000/docs
```

### **Production Deployment**
```bash
# Production deployment với Docker
docker-compose -f docker-compose.production.yml up -d

# Health check
curl http://localhost:8000/api/v1/ai/health
```

## ✨ Key Features

### **🤖 Complete AI Capabilities**
- **Text Generation**: Advanced text generation với streaming
- **Chat Completion**: Multi-turn conversations
- **Multimodal AI**: Text + Image processing
- **Image Generation**: AI-powered image creation
- **Text-to-Speech**: Multi-speaker voice synthesis
- **Function Calling**: Tool integration và automation
- **Embeddings**: Semantic search và similarity

### **🏗️ Production-Grade Features**
- **Real-time Streaming**: WebSocket và SSE support
- **Context Caching**: Performance optimization
- **Health Monitoring**: Comprehensive health checks
- **Error Recovery**: Robust error handling
- **Resource Management**: Efficient resource utilization
- **Schema Validation**: Input/output validation

### **🔧 Developer Experience**
- **REST API**: 25+ comprehensive endpoints
- **OpenAPI Docs**: Interactive API documentation
- **Docker Support**: Containerized deployment
- **Testing Suite**: Automated testing
- **Structured Logging**: Comprehensive logging
- **Configuration**: Flexible environment management

## 📊 API Overview

### **Core AI Endpoints**
```bash
# Text Generation
POST /api/v1/ai/generate/text

# Chat Completion  
POST /api/v1/ai/chat/completions

# Multimodal Generation
POST /api/v1/ai/generate/multimodal

# Image Generation
POST /api/v1/ai/generate/image

# Text-to-Speech
POST /api/v1/ai/tts

# Function Calling
POST /api/v1/ai/function-calling

# Embeddings
POST /api/v1/ai/embeddings
```

### **Advanced Features**
```bash
# Structured Output
POST /api/v1/ai/generate/structured

# Schema Validation
POST /api/v1/ai/validate/schema

# Template Rendering
POST /api/v1/ai/render/template

# Context Caching
POST /api/v1/ai/cache/create
```

### **Monitoring & Health**
```bash
# Health Check
GET /api/v1/ai/health

# Metrics (Prometheus)
GET /api/v1/ai/metrics

# Performance Status
GET /api/v1/ai/status/performance
```

## 🎯 Use Cases

### **Content Creation**
- **Article Writing**: AI-powered content generation
- **Product Descriptions**: E-commerce content
- **Social Media**: Automated social content
- **Documentation**: Technical documentation

### **Visual Content**
- **Image Generation**: Creative image creation
- **Image Editing**: AI-powered image modification
- **Visual Storytelling**: Multimodal content
- **Brand Assets**: Marketing materials

### **Conversational AI**
- **Chatbots**: Customer service automation
- **Virtual Assistants**: Personal AI assistants
- **Educational Tools**: Interactive learning
- **Support Systems**: Technical support

### **Business Intelligence**
- **Data Analysis**: Automated insights
- **Report Generation**: Business reports
- **Document Processing**: Information extraction
- **Decision Support**: AI-driven recommendations

## 📈 Performance

### **Response Times**
- **Text Generation**: <2s average
- **Image Generation**: <10s average
- **Streaming**: <100ms latency
- **API Calls**: <500ms average

### **Scalability**
- **Concurrent Users**: 100+ simultaneous
- **Throughput**: 1000+ requests/minute
- **Uptime**: 99.9% availability target
- **Cache Hit Rate**: >80% for repeated requests

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   AI Service    │    │  Google Gemini  │
│   REST API      │◄──►│   Core Logic    │◄──►│      API        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Monitoring    │    │     Caching     │    │   Streaming     │
│   & Health      │    │   & Storage     │    │   & WebSocket   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Core Components**
- **API Layer**: FastAPI với OpenAPI documentation
- **Service Layer**: Business logic và AI integration
- **Core Layer**: Caching, streaming, monitoring
- **Models Layer**: Pydantic schemas và validation

## 🔧 Configuration

### **Environment Variables**
```bash
# Required
GOOGLE_API_KEY=your-gemini-api-key

# Optional
LOG_LEVEL=INFO
CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=100
ENABLE_STREAMING=true
```

### **Docker Configuration**
```yaml
# docker-compose.yml
version: '3.8'
services:
  ai-service:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    volumes:
      - ./logs:/app/logs
```

## 📚 Documentation

- **[Project Overview](docs/PROJECT_OVERVIEW.md)**: Complete platform overview
- **[Phase 5 Roadmap](docs/PHASE5_ROADMAP.md)**: Next development phase
- **[Project Status](docs/PROJECT_STATUS.md)**: Current completion status
- **[Image Generation](docs/IMAGE_GENERATION.md)**: Image generation features
- **[Stability Guide](docs/PHASE3_STABILITY_GUIDE.md)**: Production stability
- **[Advanced Features](docs/PHASE4_ADVANCED_FEATURES_GUIDE.md)**: Advanced capabilities

## 🧪 Testing

```bash
# Run all tests
./run_tests.sh

# Run specific test suite
pytest tests/test_phase3_stability.py -v

# Run với coverage
pytest --cov=ai_service tests/
```

## 🚀 Deployment

### **Development**
```bash
# Local development
uvicorn ai_service.main:app --reload --host 0.0.0.0 --port 8000
```

### **Production**
```bash
# Docker production deployment
docker-compose -f docker-compose.production.yml up -d

# Kubernetes deployment
kubectl apply -f k8s/
```

### **Health Monitoring**
```bash
# Basic health check
curl http://localhost:8000/api/v1/ai/health

# Detailed health check
curl http://localhost:8000/api/v1/ai/health/detailed

# Prometheus metrics
curl http://localhost:8000/api/v1/ai/metrics
```

## 📊 Project Status

### **Completed Phases**
- ✅ **Phase 1**: Basic AI Features (Text, Chat, Multimodal, Functions)
- ✅ **Phase 2**: Image Generation (Gemini Native + Imagen 3)
- ✅ **Phase 3**: Stability & Production Readiness
- ✅ **Phase 4**: Advanced Features (Structured Output, Templates)

### **Current Phase**
- 🚧 **Phase 5**: Performance Optimization & Enterprise Features

### **Success Metrics**
- **API Endpoints**: 25+ comprehensive endpoints
- **AI Modalities**: 6 different AI capabilities
- **Production Ready**: Health checks, monitoring, error handling
- **Developer Experience**: Complete documentation và testing

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google Gemini API**: Powerful AI capabilities
- **FastAPI**: Modern Python web framework
- **Pydantic**: Data validation và serialization
- **Docker**: Containerization platform

---

**AI Service** - Complete Multimodal AI Platform for Production Applications 🚀

For support và questions, please refer to the [documentation](docs/) or open an issue.
