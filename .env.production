# Production Environment Configuration
# AI Service Production Settings

# Application Settings
APP_ENV=production
APP_VERSION=1.0.0
APP_NAME=AI Service
DEBUG=false
TESTING=false

# Server Settings
HOST=0.0.0.0
PORT=8000

# Logging
LOG_LEVEL=INFO

# Security Settings (CHANGE THESE IN PRODUCTION!)
SECRET_KEY=your-super-secure-secret-key-change-this-in-production
API_KEY_HEADER=X-API-Key
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=100
MAX_REQUESTS_PER_HOUR=2000

# CORS Settings (Specify your actual domains)
CORS_ORIGINS=["https://yourdomain.com","https://api.yourdomain.com"]

# Monitoring Settings
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Performance Settings
MAX_WORKERS=8
REQUEST_TIMEOUT=300
MAX_REQUEST_SIZE=10485760

# Cache Settings
ENABLE_CACHING=true
CACHE_TTL=3600
MAX_CACHE_SIZE=10000

# AI Model Settings
DEFAULT_MODEL=gemini-2.0-flash
DEFAULT_EMBEDDING_MODEL=gemini-embedding-exp-03-07

# Google AI Studio API Key (REQUIRED)
GOOGLE_API_KEY=your-google-ai-studio-api-key

# File Storage
TEMP_FILE_DIR=/tmp/ai_service
