"""
Anthropic Claude Provider Implementation for Multi-Provider System.
Supports Claude models for text generation and chat completion.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
import anthropic
from anthropic import AsyncAnthropic

from ai_service.core.multi_provider_manager import (
    BaseAIProvider, AIProvider, ModelCapability, ModelInfo, ProviderConfig, RequestMetrics
)

logger = logging.getLogger(__name__)


class AnthropicProvider(BaseAIProvider):
    """Anthropic Claude provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        super().__init__(config)
        self.client: Optional[AsyncAnthropic] = None
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize available Anthropic models."""
        self.models = {
            "claude-3-5-sonnet-20241022": ModelInfo(
                provider=AIProvider.ANTHROPIC,
                model_id="claude-3-5-sonnet-20241022",
                name="Claude 3.5 Sonnet",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.IMAGE_ANALYSIS,
                    ModelCapability.MULTIMODAL
                ],
                max_tokens=8192,
                cost_per_1k_tokens=0.003,
                quality_score=0.95,
                speed_score=0.85,
                reliability_score=0.94
            ),
            "claude-3-opus-20240229": ModelInfo(
                provider=AIProvider.ANTHROPIC,
                model_id="claude-3-opus-20240229",
                name="Claude 3 Opus",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.IMAGE_ANALYSIS,
                    ModelCapability.MULTIMODAL
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.015,
                quality_score=0.98,
                speed_score=0.75,
                reliability_score=0.96
            ),
            "claude-3-sonnet-20240229": ModelInfo(
                provider=AIProvider.ANTHROPIC,
                model_id="claude-3-sonnet-20240229",
                name="Claude 3 Sonnet",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.IMAGE_ANALYSIS,
                    ModelCapability.MULTIMODAL
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.003,
                quality_score=0.90,
                speed_score=0.85,
                reliability_score=0.93
            ),
            "claude-3-haiku-20240307": ModelInfo(
                provider=AIProvider.ANTHROPIC,
                model_id="claude-3-haiku-20240307",
                name="Claude 3 Haiku",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.00025,
                quality_score=0.82,
                speed_score=0.95,
                reliability_score=0.90
            )
        }
    
    async def initialize(self) -> bool:
        """Initialize the Anthropic provider."""
        try:
            # Initialize Anthropic client
            self.client = AsyncAnthropic(
                api_key=self.config.api_key,
                timeout=self.config.timeout,
                max_retries=self.config.max_retries
            )
            
            # Test connection
            health_ok = await self.health_check()
            if not health_ok:
                logger.error("Anthropic provider health check failed")
                return False
            
            logger.info("Anthropic provider initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic provider: {e}")
            return False
    
    async def generate_text(
        self,
        prompt: str,
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text using Claude."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Convert to messages format for Claude
            messages = [{"role": "user", "content": prompt}]
            
            response = await self.client.messages.create(
                model=model_id,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", 1000),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 1.0),
                stream=kwargs.get("stream", False)
            )
            
            if kwargs.get("stream", False):
                return {"stream": response}
            else:
                return {
                    "text": response.content[0].text if response.content else "",
                    "model": response.model,
                    "usage": {
                        "prompt_tokens": response.usage.input_tokens,
                        "completion_tokens": response.usage.output_tokens,
                        "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                    },
                    "provider": AIProvider.ANTHROPIC.value,
                    "finish_reason": response.stop_reason
                }
            
        except Exception as e:
            logger.error(f"Anthropic text generation failed: {e}")
            raise
    
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate chat completion using Claude."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Claude requires system message to be separate
            system_message = None
            claude_messages = []
            
            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    claude_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
            
            # Prepare request parameters
            request_params = {
                "model": model_id,
                "messages": claude_messages,
                "max_tokens": kwargs.get("max_tokens", 1000),
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 1.0),
                "stream": kwargs.get("stream", False)
            }
            
            if system_message:
                request_params["system"] = system_message
            
            response = await self.client.messages.create(**request_params)
            
            if kwargs.get("stream", False):
                return {"stream": response}
            else:
                return {
                    "message": {
                        "role": "assistant",
                        "content": response.content[0].text if response.content else ""
                    },
                    "model": response.model,
                    "usage": {
                        "prompt_tokens": response.usage.input_tokens,
                        "completion_tokens": response.usage.output_tokens,
                        "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                    },
                    "provider": AIProvider.ANTHROPIC.value,
                    "finish_reason": response.stop_reason
                }
            
        except Exception as e:
            logger.error(f"Anthropic chat generation failed: {e}")
            raise
    
    async def generate_embeddings(
        self,
        texts: List[str],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate embeddings using Claude (not supported)."""
        raise NotImplementedError("Anthropic does not provide embedding models")
    
    async def analyze_image(
        self,
        image_data: str,
        prompt: str,
        model_id: str = "claude-3-5-sonnet-20241022",
        **kwargs
    ) -> Dict[str, Any]:
        """Analyze image using Claude vision capabilities."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Prepare message with image
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/jpeg",  # Assume JPEG, could be detected
                                "data": image_data
                            }
                        }
                    ]
                }
            ]
            
            response = await self.client.messages.create(
                model=model_id,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", 1000),
                temperature=kwargs.get("temperature", 0.7)
            )
            
            return {
                "text": response.content[0].text if response.content else "",
                "model": response.model,
                "usage": {
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                },
                "provider": AIProvider.ANTHROPIC.value,
                "finish_reason": response.stop_reason
            }
            
        except Exception as e:
            logger.error(f"Anthropic image analysis failed: {e}")
            raise
    
    async def function_calling(
        self,
        prompt: str,
        functions: List[Dict[str, Any]],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute function calling using Claude (limited support)."""
        # Claude doesn't have native function calling like OpenAI
        # This would need to be implemented using structured prompting
        raise NotImplementedError("Native function calling not supported by Anthropic")
    
    async def health_check(self) -> bool:
        """Check Anthropic provider health."""
        try:
            if not self.client:
                return False
            
            # Simple test request
            response = await self.client.messages.create(
                model="claude-3-haiku-20240307",
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )
            
            return bool(response and response.content)
            
        except Exception as e:
            logger.error(f"Anthropic health check failed: {e}")
            return False
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.models.get(model_id)
    
    def supports_capability(self, model_id: str, capability: ModelCapability) -> bool:
        """Check if model supports specific capability."""
        model = self.models.get(model_id)
        return model and capability in model.capabilities if model else False
    
    async def get_model_pricing(self, model_id: str) -> Dict[str, float]:
        """Get pricing information for a model."""
        model = self.models.get(model_id)
        if not model:
            return {}
        
        return {
            "cost_per_1k_tokens": model.cost_per_1k_tokens,
            "currency": "USD"
        }
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information."""
        return {
            "provider": self.provider.value,
            "name": "Anthropic Claude",
            "models_count": len(self.models),
            "capabilities": list(set(
                cap.value for model in self.models.values() 
                for cap in model.capabilities
            )),
            "is_enabled": self.config.is_enabled,
            "priority": self.config.priority,
            "rate_limit": self.config.rate_limit_per_minute
        }
    
    async def generate_multimodal(
        self,
        prompt: str,
        images: Optional[List[str]] = None,
        model_id: str = "claude-3-5-sonnet-20241022",
        **kwargs
    ) -> Dict[str, Any]:
        """Generate multimodal content using Claude."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Prepare content with text and images
            content = [{"type": "text", "text": prompt}]
            
            if images:
                for image_data in images:
                    content.append({
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": "image/jpeg",
                            "data": image_data
                        }
                    })
            
            messages = [{"role": "user", "content": content}]
            
            response = await self.client.messages.create(
                model=model_id,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", 1000),
                temperature=kwargs.get("temperature", 0.7),
                stream=kwargs.get("stream", False)
            )
            
            if kwargs.get("stream", False):
                return {"stream": response}
            else:
                return {
                    "text": response.content[0].text if response.content else "",
                    "model": response.model,
                    "usage": {
                        "prompt_tokens": response.usage.input_tokens,
                        "completion_tokens": response.usage.output_tokens,
                        "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                    },
                    "provider": AIProvider.ANTHROPIC.value,
                    "finish_reason": response.stop_reason
                }
            
        except Exception as e:
            logger.error(f"Anthropic multimodal generation failed: {e}")
            raise
