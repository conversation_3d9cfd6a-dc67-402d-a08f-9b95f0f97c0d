"""
OpenAI Provider Implementation for Multi-Provider System.
Supports GPT models, DALL-E, and OpenAI embeddings.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
import openai
from openai import AsyncOpenAI

from ai_service.core.multi_provider_manager import (
    BaseAIProvider, AIProvider, ModelCapability, ModelInfo, ProviderConfig, RequestMetrics
)

logger = logging.getLogger(__name__)


class OpenAIProvider(BaseAIProvider):
    """OpenAI provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        super().__init__(config)
        self.client: Optional[AsyncOpenAI] = None
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize available OpenAI models."""
        self.models = {
            "gpt-4o": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="gpt-4o",
                name="GPT-4o",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.IMAGE_ANALYSIS
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.005,
                quality_score=0.95,
                speed_score=0.85,
                reliability_score=0.95
            ),
            "gpt-4o-mini": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="gpt-4o-mini",
                name="GPT-4o Mini",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE_GENERATION
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.00015,
                quality_score=0.85,
                speed_score=0.95,
                reliability_score=0.92
            ),
            "gpt-4-turbo": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="gpt-4-turbo",
                name="GPT-4 Turbo",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE_GENERATION
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.01,
                quality_score=0.92,
                speed_score=0.80,
                reliability_score=0.94
            ),
            "gpt-3.5-turbo": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="gpt-3.5-turbo",
                name="GPT-3.5 Turbo",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.FUNCTION_CALLING
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.0005,
                quality_score=0.80,
                speed_score=0.90,
                reliability_score=0.90
            ),
            "text-embedding-3-large": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="text-embedding-3-large",
                name="Text Embedding 3 Large",
                capabilities=[ModelCapability.EMBEDDINGS],
                max_tokens=8191,
                cost_per_1k_tokens=0.00013,
                quality_score=0.95,
                speed_score=0.85,
                reliability_score=0.95
            ),
            "text-embedding-3-small": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="text-embedding-3-small",
                name="Text Embedding 3 Small",
                capabilities=[ModelCapability.EMBEDDINGS],
                max_tokens=8191,
                cost_per_1k_tokens=0.00002,
                quality_score=0.88,
                speed_score=0.95,
                reliability_score=0.93
            ),
            "dall-e-3": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="dall-e-3",
                name="DALL-E 3",
                capabilities=[ModelCapability.IMAGE_GENERATION],
                max_tokens=0,  # Not applicable for image generation
                cost_per_1k_tokens=0.04,  # Per image cost
                quality_score=0.95,
                speed_score=0.70,
                reliability_score=0.90
            ),
            "dall-e-2": ModelInfo(
                provider=AIProvider.OPENAI,
                model_id="dall-e-2",
                name="DALL-E 2",
                capabilities=[ModelCapability.IMAGE_GENERATION],
                max_tokens=0,
                cost_per_1k_tokens=0.02,  # Per image cost
                quality_score=0.85,
                speed_score=0.80,
                reliability_score=0.88
            )
        }
    
    async def initialize(self) -> bool:
        """Initialize the OpenAI provider."""
        try:
            # Initialize OpenAI client
            self.client = AsyncOpenAI(
                api_key=self.config.api_key,
                organization=self.config.organization,
                timeout=self.config.timeout,
                max_retries=self.config.max_retries
            )
            
            # Test connection
            health_ok = await self.health_check()
            if not health_ok:
                logger.error("OpenAI provider health check failed")
                return False
            
            logger.info("OpenAI provider initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {e}")
            return False
    
    async def generate_text(
        self,
        prompt: str,
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text using OpenAI."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Convert to chat format for consistency
            messages = [{"role": "user", "content": prompt}]
            
            response = await self.client.chat.completions.create(
                model=model_id,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", 1000),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 1.0),
                stream=kwargs.get("stream", False)
            )
            
            if kwargs.get("stream", False):
                # Handle streaming response
                return {"stream": response}
            else:
                return {
                    "text": response.choices[0].message.content,
                    "model": response.model,
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    },
                    "provider": AIProvider.OPENAI.value,
                    "finish_reason": response.choices[0].finish_reason
                }
            
        except Exception as e:
            logger.error(f"OpenAI text generation failed: {e}")
            raise
    
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate chat completion using OpenAI."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Handle function calling if provided
            functions = kwargs.get("functions")
            tools = None
            if functions:
                tools = [{"type": "function", "function": func} for func in functions]
            
            response = await self.client.chat.completions.create(
                model=model_id,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", 1000),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 1.0),
                tools=tools,
                stream=kwargs.get("stream", False)
            )
            
            if kwargs.get("stream", False):
                return {"stream": response}
            else:
                message = response.choices[0].message
                result = {
                    "message": {
                        "role": message.role,
                        "content": message.content
                    },
                    "model": response.model,
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    },
                    "provider": AIProvider.OPENAI.value,
                    "finish_reason": response.choices[0].finish_reason
                }
                
                # Add function calls if present
                if message.tool_calls:
                    result["function_calls"] = [
                        {
                            "name": call.function.name,
                            "arguments": call.function.arguments
                        }
                        for call in message.tool_calls
                    ]
                
                return result
            
        except Exception as e:
            logger.error(f"OpenAI chat generation failed: {e}")
            raise
    
    async def generate_embeddings(
        self,
        texts: List[str],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate embeddings using OpenAI."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            response = await self.client.embeddings.create(
                model=model_id,
                input=texts,
                dimensions=kwargs.get("dimensions")  # Optional for some models
            )
            
            return {
                "embeddings": [data.embedding for data in response.data],
                "model": response.model,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "provider": AIProvider.OPENAI.value
            }
            
        except Exception as e:
            logger.error(f"OpenAI embeddings generation failed: {e}")
            raise
    
    async def generate_image(
        self,
        prompt: str,
        model_id: str = "dall-e-3",
        **kwargs
    ) -> Dict[str, Any]:
        """Generate image using DALL-E."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            size = kwargs.get("size", "1024x1024")
            quality = kwargs.get("quality", "standard")
            n = kwargs.get("n", 1)
            
            # DALL-E 3 specific parameters
            if model_id == "dall-e-3":
                response = await self.client.images.generate(
                    model=model_id,
                    prompt=prompt,
                    size=size,
                    quality=quality,
                    n=n
                )
            else:
                # DALL-E 2 parameters
                response = await self.client.images.generate(
                    model=model_id,
                    prompt=prompt,
                    size=size,
                    n=n
                )
            
            return {
                "images": [
                    {
                        "url": image.url,
                        "revised_prompt": getattr(image, "revised_prompt", None)
                    }
                    for image in response.data
                ],
                "model": model_id,
                "usage": {"images_generated": len(response.data)},
                "provider": AIProvider.OPENAI.value
            }
            
        except Exception as e:
            logger.error(f"OpenAI image generation failed: {e}")
            raise
    
    async def analyze_image(
        self,
        image_url: str,
        prompt: str,
        model_id: str = "gpt-4o",
        **kwargs
    ) -> Dict[str, Any]:
        """Analyze image using OpenAI vision models."""
        if not self.client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": image_url}}
                    ]
                }
            ]
            
            response = await self.client.chat.completions.create(
                model=model_id,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", 1000),
                temperature=kwargs.get("temperature", 0.7)
            )
            
            return {
                "text": response.choices[0].message.content,
                "model": response.model,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "provider": AIProvider.OPENAI.value,
                "finish_reason": response.choices[0].finish_reason
            }
            
        except Exception as e:
            logger.error(f"OpenAI image analysis failed: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check OpenAI provider health."""
        try:
            if not self.client:
                return False
            
            # Simple test request
            response = await self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )
            
            return bool(response and response.choices)
            
        except Exception as e:
            logger.error(f"OpenAI health check failed: {e}")
            return False
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.models.get(model_id)
    
    def supports_capability(self, model_id: str, capability: ModelCapability) -> bool:
        """Check if model supports specific capability."""
        model = self.models.get(model_id)
        return model and capability in model.capabilities if model else False
    
    async def get_model_pricing(self, model_id: str) -> Dict[str, float]:
        """Get pricing information for a model."""
        model = self.models.get(model_id)
        if not model:
            return {}
        
        return {
            "cost_per_1k_tokens": model.cost_per_1k_tokens,
            "currency": "USD"
        }
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information."""
        return {
            "provider": self.provider.value,
            "name": "OpenAI",
            "models_count": len(self.models),
            "capabilities": list(set(
                cap.value for model in self.models.values() 
                for cap in model.capabilities
            )),
            "is_enabled": self.config.is_enabled,
            "priority": self.config.priority,
            "rate_limit": self.config.rate_limit_per_minute
        }
