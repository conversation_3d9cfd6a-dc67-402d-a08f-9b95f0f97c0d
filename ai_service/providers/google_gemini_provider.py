"""
Google Gemini Provider Implementation for Multi-Provider System.
Integrates existing Gemini functionality into the new provider architecture.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
import google.generativeai as genai

from ai_service.core.multi_provider_manager import (
    BaseAIProvider, AIProvider, ModelCapability, ModelInfo, ProviderConfig, RequestMetrics
)
from ai_service.core.ai_client import AIClient

logger = logging.getLogger(__name__)


class GoogleGeminiProvider(BaseAIProvider):
    """Google Gemini provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        super().__init__(config)
        self.ai_client: Optional[AIClient] = None
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize available Gemini models."""
        self.models = {
            "gemini-1.5-flash": ModelInfo(
                provider=AIProvider.GOOGLE_GEMINI,
                model_id="gemini-1.5-flash",
                name="Gemini 1.5 Flash",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE_GENERATION
                ],
                max_tokens=8192,
                cost_per_1k_tokens=0.0015,
                quality_score=0.85,
                speed_score=0.95,
                reliability_score=0.92
            ),
            "gemini-1.5-pro": ModelInfo(
                provider=AIProvider.GOOGLE_GEMINI,
                model_id="gemini-1.5-pro",
                name="Gemini 1.5 Pro",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.IMAGE_ANALYSIS
                ],
                max_tokens=8192,
                cost_per_1k_tokens=0.0035,
                quality_score=0.95,
                speed_score=0.80,
                reliability_score=0.94
            ),
            "gemini-1.0-pro": ModelInfo(
                provider=AIProvider.GOOGLE_GEMINI,
                model_id="gemini-1.0-pro",
                name="Gemini 1.0 Pro",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.FUNCTION_CALLING
                ],
                max_tokens=4096,
                cost_per_1k_tokens=0.0025,
                quality_score=0.80,
                speed_score=0.85,
                reliability_score=0.90
            ),
            "text-embedding-004": ModelInfo(
                provider=AIProvider.GOOGLE_GEMINI,
                model_id="text-embedding-004",
                name="Text Embedding 004",
                capabilities=[ModelCapability.EMBEDDINGS],
                max_tokens=2048,
                cost_per_1k_tokens=0.0001,
                quality_score=0.88,
                speed_score=0.90,
                reliability_score=0.95
            )
        }
    
    async def initialize(self) -> bool:
        """Initialize the Google Gemini provider."""
        try:
            # Configure Gemini API
            genai.configure(api_key=self.config.api_key)
            
            # Initialize AI client
            self.ai_client = AIClient()
            
            # Test connection
            health_ok = await self.health_check()
            if not health_ok:
                logger.error("Gemini provider health check failed")
                return False
            
            logger.info("Google Gemini provider initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Google Gemini provider: {e}")
            return False
    
    async def generate_text(
        self,
        prompt: str,
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text using Gemini."""
        if not self.ai_client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Use existing AI client functionality
            result = await self.ai_client.generate_text_async(
                prompt=prompt,
                model=model_id,
                max_tokens=kwargs.get("max_tokens"),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 0.95),
                top_k=kwargs.get("top_k", 40),
                stream=kwargs.get("stream", False)
            )
            
            return {
                "text": result["text"],
                "model": result["model"],
                "usage": result.get("usage", {}),
                "provider": AIProvider.GOOGLE_GEMINI.value,
                "finish_reason": result.get("finish_reason", "stop")
            }
            
        except Exception as e:
            logger.error(f"Gemini text generation failed: {e}")
            raise
    
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate chat completion using Gemini."""
        if not self.ai_client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Convert messages to Gemini format
            conversation_history = []
            for msg in messages[:-1]:  # All but last message
                conversation_history.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            current_message = messages[-1]["content"] if messages else ""
            
            # Use existing AI client functionality
            result = await self.ai_client.generate_chat_async(
                message=current_message,
                conversation_history=conversation_history,
                model=model_id,
                max_tokens=kwargs.get("max_tokens"),
                temperature=kwargs.get("temperature", 0.7),
                stream=kwargs.get("stream", False)
            )
            
            return {
                "message": {
                    "role": "assistant",
                    "content": result["response"]
                },
                "model": result["model"],
                "usage": result.get("usage", {}),
                "provider": AIProvider.GOOGLE_GEMINI.value,
                "finish_reason": result.get("finish_reason", "stop")
            }
            
        except Exception as e:
            logger.error(f"Gemini chat generation failed: {e}")
            raise
    
    async def generate_embeddings(
        self,
        texts: List[str],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate embeddings using Gemini."""
        if not self.ai_client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Generate embeddings for each text
            embeddings = []
            total_tokens = 0
            
            for text in texts:
                result = await self.ai_client.generate_embeddings_async(
                    text=text,
                    model=model_id
                )
                embeddings.append(result["embedding"])
                total_tokens += result.get("usage", {}).get("total_tokens", 0)
            
            return {
                "embeddings": embeddings,
                "model": model_id,
                "usage": {
                    "prompt_tokens": total_tokens,
                    "total_tokens": total_tokens
                },
                "provider": AIProvider.GOOGLE_GEMINI.value
            }
            
        except Exception as e:
            logger.error(f"Gemini embeddings generation failed: {e}")
            raise
    
    async def generate_multimodal(
        self,
        prompt: str,
        images: Optional[List[str]] = None,
        model_id: str = "gemini-1.5-pro",
        **kwargs
    ) -> Dict[str, Any]:
        """Generate multimodal content using Gemini."""
        if not self.ai_client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Use existing multimodal functionality
            result = await self.ai_client.generate_multimodal_async(
                prompt=prompt,
                images=images or [],
                model=model_id,
                max_tokens=kwargs.get("max_tokens"),
                temperature=kwargs.get("temperature", 0.7),
                stream=kwargs.get("stream", False)
            )
            
            return {
                "text": result["text"],
                "model": result["model"],
                "usage": result.get("usage", {}),
                "provider": AIProvider.GOOGLE_GEMINI.value,
                "finish_reason": result.get("finish_reason", "stop")
            }
            
        except Exception as e:
            logger.error(f"Gemini multimodal generation failed: {e}")
            raise
    
    async def generate_image(
        self,
        prompt: str,
        model_id: str = "imagen-3.0-generate-001",
        **kwargs
    ) -> Dict[str, Any]:
        """Generate image using Gemini/Imagen."""
        if not self.ai_client:
            raise Exception("Provider not initialized")
        
        try:
            # Use existing image generation functionality
            result = await self.ai_client.generate_image_async(
                prompt=prompt,
                model=model_id,
                num_images=kwargs.get("num_images", 1),
                aspect_ratio=kwargs.get("aspect_ratio", "1:1"),
                safety_filter_level=kwargs.get("safety_filter_level", "block_some")
            )
            
            return {
                "images": result["images"],
                "model": result["model"],
                "usage": result.get("usage", {}),
                "provider": AIProvider.GOOGLE_GEMINI.value
            }
            
        except Exception as e:
            logger.error(f"Gemini image generation failed: {e}")
            raise
    
    async def function_calling(
        self,
        prompt: str,
        functions: List[Dict[str, Any]],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute function calling using Gemini."""
        if not self.ai_client:
            raise Exception("Provider not initialized")
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not available")
        
        try:
            # Use existing function calling functionality
            result = await self.ai_client.function_calling_async(
                prompt=prompt,
                functions=functions,
                model=model_id,
                max_tokens=kwargs.get("max_tokens"),
                temperature=kwargs.get("temperature", 0.7)
            )
            
            return {
                "function_calls": result.get("function_calls", []),
                "text": result.get("text", ""),
                "model": result["model"],
                "usage": result.get("usage", {}),
                "provider": AIProvider.GOOGLE_GEMINI.value
            }
            
        except Exception as e:
            logger.error(f"Gemini function calling failed: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check Gemini provider health."""
        try:
            if not self.ai_client:
                return False
            
            # Simple test request
            result = await self.ai_client.generate_text_async(
                prompt="Hello",
                model="gemini-1.5-flash",
                max_tokens=10
            )
            
            return bool(result and result.get("text"))
            
        except Exception as e:
            logger.error(f"Gemini health check failed: {e}")
            return False
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.models.get(model_id)
    
    def supports_capability(self, model_id: str, capability: ModelCapability) -> bool:
        """Check if model supports specific capability."""
        model = self.models.get(model_id)
        return model and capability in model.capabilities if model else False
    
    async def get_model_pricing(self, model_id: str) -> Dict[str, float]:
        """Get pricing information for a model."""
        model = self.models.get(model_id)
        if not model:
            return {}
        
        return {
            "cost_per_1k_tokens": model.cost_per_1k_tokens,
            "currency": "USD"
        }
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information."""
        return {
            "provider": self.provider.value,
            "name": "Google Gemini",
            "models_count": len(self.models),
            "capabilities": list(set(
                cap.value for model in self.models.values() 
                for cap in model.capabilities
            )),
            "is_enabled": self.config.is_enabled,
            "priority": self.config.priority,
            "rate_limit": self.config.rate_limit_per_minute
        }
