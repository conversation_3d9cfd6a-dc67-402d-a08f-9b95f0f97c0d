"""
Service layer for AI operations.
"""
import json
import logging
import asyncio
import time
from typing import Dict, List, Optional, Any, AsyncIterator, <PERSON>, Callable

from fastapi import WebSocket
from fastapi.responses import StreamingResponse

from ai_service.core.ai_client import AIClient
from ai_service.core.resource_manager import resource_tracker, managed_stream
from ai_service.core.json_mode import json_mode_handler
from ai_service.core.context_cache import context_cache_manager
from ai_service.core.enhanced_function_executor import enhanced_function_executor
from ai_service.core.cache_manager import cache_manager
from ai_service.core.batch_processor import batch_processor
from ai_service.core.performance_monitor import performance_monitor
from ai_service.core.streaming_manager import streaming_manager, StreamType, collaboration_manager
from ai_service.core.websocket_manager import websocket_manager
from ai_service.core.sse_manager import sse_manager
from ai_service.models.schemas import (
    TextGenerationRequest,
    TextGenerationResponse,
    StreamChunk,
    ChatRequest,
    ChatResponse,
    ChatMessage,
    MultimodalGenerationRequest,
    FunctionCallingRequest,
    FunctionCallingResponse,
    FunctionCall,
    FunctionResponse,
    FunctionExecutionResult,
    EmbeddingRequest,
    EmbeddingResponse,
    TokenCountRequest,
    TokenCountResponse,
    TTSRequest,
    TTSResponse,
    UsageInfo,
    # Context Caching schemas
    CacheCreateRequest,
    CacheUpdateRequest,
    CacheResponse,
    CacheListResponse,
    CachedGenerationRequest,
    # Enhanced Function Calling schemas
    EnhancedFunctionCallingRequest,
    EnhancedFunctionCallingResponse,
    FunctionExecutionConfig,
    FunctionChain,
    FunctionExecutionResult,
    FunctionChainResult
)

logger = logging.getLogger(__name__)


class AIService:
    """Service for AI operations."""

    def __init__(self, ai_client: Optional[AIClient] = None):
        """Initialize the AI service.

        Args:
            ai_client: AI client instance. If not provided, creates a new one.
        """
        # Try to use global AI client first (with registered functions)
        if ai_client is None:
            try:
                from ai_service.main import global_ai_client
                if global_ai_client is not None:
                    self.ai_client = global_ai_client
                    logger.info("AI service initialized with global AI client (with registered functions)")
                else:
                    self.ai_client = AIClient()
                    logger.info("AI service initialized with new AI client (no registered functions)")
            except ImportError:
                self.ai_client = AIClient()
                logger.info("AI service initialized with new AI client (no registered functions)")
        else:
            self.ai_client = ai_client
            logger.info("AI service initialized with provided AI client")

    async def generate_text(
        self,
        request: TextGenerationRequest,
        stream: bool = False
    ) -> Union[TextGenerationResponse, AsyncIterator[StreamChunk]]:
        """Generate text using the AI model.

        Args:
            request: Text generation request
            stream: Whether to stream the response

        Returns:
            Text generation response or streaming response
        """
        logger.info(f"Generating text with prompt: {request.prompt[:50]}...")

        # Convert safety settings if provided
        safety_settings = None
        if request.safety_settings:
            safety_settings = [
                {
                    "category": setting.category.value,
                    "threshold": setting.threshold.value
                }
                for setting in request.safety_settings
            ]

        if stream or request.stream:
            # Return a streaming response
            async def response_generator():
                async_gen = await self.ai_client.generate_text_async(
                    prompt=request.prompt,
                    model=request.model,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p,
                    top_k=request.top_k,
                    system_instruction=request.system_instruction,
                    safety_settings=safety_settings,
                    stream=True,
                    json_mode=getattr(request, 'json_mode', False),
                    json_schema=getattr(request, 'json_schema', None)
                )

                async for chunk in async_gen:
                    yield StreamChunk(
                        text=chunk["text"],
                        is_final=chunk["is_final"]
                    ).json() + "\n"

            return response_generator()
        else:
            # Return a complete response
            result = await self.ai_client.generate_text_async(
                prompt=request.prompt,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                system_instruction=request.system_instruction,
                safety_settings=safety_settings,
                stream=False,
                json_mode=getattr(request, 'json_mode', False),
                json_schema=getattr(request, 'json_schema', None)
            )

            return TextGenerationResponse(
                text=result["text"],
                model=result["model"],
                usage=UsageInfo(
                    prompt_tokens=result["usage"].get("prompt_tokens", 0),
                    completion_tokens=result["usage"].get("completion_tokens", 0),
                    total_tokens=result["usage"].get("total_tokens", 0)
                ),
                finish_reason=result.get("finish_reason")
            )

    async def chat_completion(
        self,
        request: ChatRequest,
        stream: bool = False
    ) -> Union[ChatResponse, AsyncIterator[StreamChunk]]:
        """Generate chat completion using the AI model.

        Args:
            request: Chat completion request
            stream: Whether to stream the response

        Returns:
            Chat completion response or streaming response
        """
        logger.info("Generating chat completion")

        # Convert Pydantic ChatMessage objects to dictionaries
        messages = [
            {"role": msg.role, "content": msg.content}
            for msg in request.messages
        ]

        # Convert safety settings if provided
        safety_settings = None
        if request.safety_settings:
            safety_settings = [
                {
                    "category": setting.category.value,
                    "threshold": setting.threshold.value
                }
                for setting in request.safety_settings
            ]

        if stream or request.stream:
            # Return a streaming response
            async def response_generator():
                async_gen = await self.ai_client.chat_completion_async(
                    messages=messages,
                    model=request.model,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p,
                    top_k=request.top_k,
                    system_instruction=request.system_instruction,
                    safety_settings=safety_settings,
                    stream=True
                )

                async for chunk in async_gen:
                    yield StreamChunk(
                        text=chunk["message"]["content"],
                        is_final=chunk["is_final"]
                    ).json() + "\n"

            return response_generator()
        else:
            # Return a complete response
            result = await self.ai_client.chat_completion_async(
                messages=messages,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                system_instruction=request.system_instruction,
                safety_settings=safety_settings,
                stream=False
            )

            return ChatResponse(
                message=ChatMessage(
                    role=result["message"]["role"],
                    content=result["message"]["content"]
                ),
                model=result["model"],
                usage=UsageInfo(
                    prompt_tokens=result["usage"].get("prompt_tokens", 0),
                    completion_tokens=result["usage"].get("completion_tokens", 0),
                    total_tokens=result["usage"].get("total_tokens", 0)
                ),
                finish_reason=result.get("finish_reason")
            )

    async def multimodal_generation(
        self,
        request: MultimodalGenerationRequest,
        image_parts: List[Union[str, bytes, Dict[str, Any]]] = None,
        stream: bool = False
    ) -> Union[TextGenerationResponse, AsyncIterator[StreamChunk]]:
        """Generate content using multimodal inputs.

        Args:
            request: Multimodal generation request
            image_parts: Processed image parts (optional, will process from request if not provided)
            stream: Whether to stream the response

        Returns:
            Text generation response or streaming response
        """
        logger.info("Generating multimodal content")

        # Use provided image_parts or process from request
        if image_parts is None:
            image_parts = []
            for img in request.image_parts:
                if img.type == "url":
                    # It's a URL
                    image_parts.append(img.data)
                elif img.type == "file_uri":
                    # It's a file URI
                    image_parts.append({
                        "file_uri": img.data,
                        "mime_type": img.mime_type
                    })
                elif img.type == "base64":
                    # It's base64-encoded data
                    image_parts.append({
                        "type": "base64",
                        "data": img.data,
                        "mime_type": img.mime_type
                    })

        # Convert safety settings if provided
        safety_settings = None
        if request.safety_settings:
            safety_settings = [
                {
                    "category": setting.category.value,
                    "threshold": setting.threshold.value
                }
                for setting in request.safety_settings
            ]

        if stream or request.stream:
            # Return a streaming response with enhanced resource management
            async def response_generator():
                async_gen = None
                stream_id = None
                try:
                    # Create stream session for tracking
                    from ai_service.core.streaming_manager import streaming_manager, StreamType
                    stream_id = await streaming_manager.create_stream(
                        stream_type=StreamType.MULTIMODAL,
                        client_id=f"multimodal_{id(request)}",
                        metadata={"model": request.model, "has_images": len(image_parts) > 0}
                    )

                    # Get the async generator directly
                    async_gen = await self.ai_client.multimodal_generation_async(
                        text_prompt=request.text,
                        image_parts=image_parts,
                        model=request.model,
                        max_tokens=request.max_tokens,
                        temperature=request.temperature,
                        top_p=request.top_p,
                        top_k=request.top_k,
                        system_instruction=request.system_instruction,
                        safety_settings=safety_settings,
                        stream=True
                    )

                    # Register the stream for resource tracking
                    resource_tracker.register_stream(async_gen)
                    await streaming_manager.start_stream(stream_id)

                    chunk_count = 0
                    total_content_length = 0

                    try:
                        # Process each chunk from the async generator
                        async for chunk in async_gen:
                            chunk_count += 1
                            chunk_text = chunk.get("text", "")
                            total_content_length += len(chunk_text)

                            chunk_data = {
                                "text": chunk_text,
                                "is_final": chunk.get("is_final", False),
                                "chunk_id": chunk_count,
                                "finish_reason": chunk.get("finish_reason"),
                                "model": chunk.get("model", request.model),
                                "progress": chunk.get("progress", 0.0),
                                "stream_id": stream_id
                            }

                            # Add total info for final chunk
                            if chunk.get("is_final"):
                                chunk_data.update({
                                    "total_chunks": chunk_count,
                                    "total_content_length": total_content_length
                                })

                                # Complete the stream
                                await streaming_manager.complete_stream(stream_id)

                            # Send chunk to stream manager
                            await streaming_manager.send_chunk(
                                stream_id=stream_id,
                                chunk_type="multimodal_response",
                                data=chunk_data,
                                is_final=chunk.get("is_final", False)
                            )

                            yield json.dumps(chunk_data) + "\n"

                    except asyncio.CancelledError:
                        logger.info(f"Multimodal stream {stream_id} cancelled by client")
                        if stream_id:
                            await streaming_manager.cancel_stream(stream_id)
                        raise
                    except Exception as e:
                        logger.error(f"Error processing multimodal chunks: {str(e)}", exc_info=True)
                        if stream_id:
                            await streaming_manager.error_stream(stream_id, str(e))
                        raise
                    finally:
                        # Unregister the stream
                        if async_gen:
                            resource_tracker.unregister_stream(async_gen)

                except Exception as e:
                    logger.error(f"Error in multimodal streaming: {str(e)}", exc_info=True)

                    # Ensure stream is marked as error
                    if stream_id:
                        await streaming_manager.error_stream(stream_id, str(e))

                    # Send error chunk
                    error_chunk = {
                        "text": "",
                        "is_final": True,
                        "chunk_id": 1,
                        "finish_reason": "error",
                        "model": request.model,
                        "error": str(e),
                        "progress": 100.0,
                        "stream_id": stream_id
                    }
                    yield json.dumps(error_chunk) + "\n"

            return response_generator()
        else:
            # Return a complete response
            result = await self.ai_client.multimodal_generation_async(
                text_prompt=request.text,
                image_parts=image_parts,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                system_instruction=request.system_instruction,
                safety_settings=safety_settings,
                stream=False
            )

            return TextGenerationResponse(
                text=result["text"],
                model=result["model"],
                usage=UsageInfo(
                    prompt_tokens=result["usage"].get("prompt_tokens", 0),
                    completion_tokens=result["usage"].get("completion_tokens", 0),
                    total_tokens=result["usage"].get("total_tokens", 0)
                ),
                finish_reason=result.get("finish_reason")
            )

    async def function_calling(
        self,
        request: FunctionCallingRequest
    ) -> FunctionCallingResponse:
        """Generate content with function calling capabilities.

        Args:
            request: Function calling request

        Returns:
            Function calling response
        """
        logger.info("Generating content with function calling")

        # Convert function declarations to the format expected by the AI client
        functions = []
        for func in request.functions:
            # Convert parameters to JSON Schema format
            parameters = {
                "type": "object",
                "properties": {},
                "required": []
            }

            for param in func.parameters:
                parameters["properties"][param.name] = {
                    "type": param.type,
                    "description": param.description or ""
                }

                if param.required:
                    parameters["required"].append(param.name)

            functions.append({
                "name": func.name,
                "description": func.description,
                "parameters": parameters
            })

        # Convert safety settings if provided
        safety_settings = None
        if request.safety_settings:
            safety_settings = [
                {
                    "category": setting.category.value,
                    "threshold": setting.threshold.value
                }
                for setting in request.safety_settings
            ]

        try:
            result = await self.ai_client.function_calling_async(
                prompt=request.prompt,
                functions=functions,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                system_instruction=request.system_instruction,
                safety_settings=safety_settings
            )

            # If there's a function call, execute it
            if result.get("function_call"):
                function_call = result["function_call"]
                logger.info(f"Executing function call: {function_call['name']}")

                # Execute the function
                execution_result = await self.ai_client.execute_function_async(function_call)

                # Add execution result to response
                result["function_execution"] = execution_result

        except Exception as e:
            logger.error(f"Error in function calling: {str(e)}")
            raise

        # Create response
        function_call = None
        function_execution = None

        if result.get("function_call"):
            function_call = FunctionCall(
                name=result["function_call"]["name"],
                arguments=result["function_call"]["args"]
            )

        if result.get("function_execution"):
            exec_result = result["function_execution"]
            function_execution = FunctionExecutionResult(
                success=exec_result.get("success", False),
                result=exec_result.get("result"),
                error=exec_result.get("error"),
                execution_time=exec_result.get("execution_time"),
                function_name=exec_result.get("function_name", "")
            )

        return FunctionCallingResponse(
            text=result.get("text"),
            function_call=function_call,
            function_execution=function_execution,
            model=result["model"],
            usage=UsageInfo(
                prompt_tokens=result["usage"].get("prompt_tokens", 0),
                completion_tokens=result["usage"].get("completion_tokens", 0),
                total_tokens=result["usage"].get("total_tokens", 0)
            )
        )

    async def function_response(
        self,
        function_call: FunctionCall,
        function_response: FunctionResponse,
        chat_history: List[Dict[str, Any]]
    ) -> TextGenerationResponse:
        """Process function response and continue the conversation.

        Args:
            function_call: Function call information
            function_response: Function response data
            chat_history: Previous chat history

        Returns:
            Text generation response
        """
        logger.info(f"Processing function response for {function_call.name}")

        # Convert chat history to the format expected by the AI client
        messages = []
        for msg in chat_history:
            if msg["role"] == "function":
                messages.append({
                    "role": "function",
                    "name": msg["name"],
                    "content": msg["content"]
                })
            else:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

        result = self.ai_client.function_response(
            function_call={
                "name": function_call.name,
                "args": function_call.arguments
            },
            function_response=function_response.response,
            chat_history=messages
        )

        return TextGenerationResponse(
            text=result["text"],
            model=result["model"],
            usage=UsageInfo(
                prompt_tokens=result["usage"].get("prompt_tokens", 0),
                completion_tokens=result["usage"].get("completion_tokens", 0),
                total_tokens=result["usage"].get("total_tokens", 0)
            )
        )

    async def generate_embeddings(
        self,
        request: EmbeddingRequest
    ) -> EmbeddingResponse:
        """Generate embeddings for text.

        Args:
            request: Embedding request

        Returns:
            Embedding response
        """
        logger.info("Generating embeddings")

        result = await self.ai_client.generate_embeddings_async(
            text=request.text,
            model=request.model,
            task_type=request.task_type
        )

        return EmbeddingResponse(
            embedding=result["embedding"],
            model=result["model"]
        )

    async def count_tokens(
        self,
        request: TokenCountRequest
    ) -> TokenCountResponse:
        """Count tokens in text.

        Args:
            request: Token count request

        Returns:
            Token count response
        """
        logger.info("Counting tokens")

        result = self.ai_client.count_tokens(
            text=request.text,
            model=request.model
        )

        return TokenCountResponse(
            token_count=result["token_count"],
            model=result["model"]
        )

    async def upload_file(
        self,
        file_path: str,
        mime_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Upload a file to the API.

        Args:
            file_path: Path to the file to upload
            mime_type: MIME type of the file

        Returns:
            Dictionary containing file information
        """
        logger.info(f"Uploading file {file_path}")

        result = self.ai_client.upload_file(
            file_path=file_path,
            mime_type=mime_type
        )

        return result

    async def get_file(
        self,
        file_id: str
    ) -> Dict[str, Any]:
        """Get information about a file.

        Args:
            file_id: ID of the file to get information about

        Returns:
            Dictionary containing file information
        """
        logger.info(f"Getting file information for {file_id}")

        result = self.ai_client.get_file(
            file_id=file_id
        )

        return result

    async def list_files(self) -> Dict[str, Any]:
        """List all files.

        Returns:
            Dictionary containing list of files
        """
        logger.info("Listing files")

        result = self.ai_client.list_files()

        return result

    async def delete_file(
        self,
        file_id: str
    ) -> Dict[str, Any]:
        """Delete a file.

        Args:
            file_id: ID of the file to delete

        Returns:
            Dictionary containing deletion status
        """
        logger.info(f"Deleting file {file_id}")

        result = self.ai_client.delete_file(
            file_id=file_id
        )

        return result

    async def text_to_speech(
        self,
        request: TTSRequest
    ) -> TTSResponse:
        """Convert text to speech with advanced voice characteristics.

        Args:
            request: TTS request with voice characteristics support

        Returns:
            TTS response with audio content
        """
        logger.info(f"Converting text to speech: {request.text[:50]}...")

        # Store voice characteristics and narrative style in client for enhanced voice selection
        if hasattr(request, 'voice_characteristics') and request.voice_characteristics:
            self.ai_client._current_voice_characteristics = request.voice_characteristics.dict() if hasattr(request.voice_characteristics, 'dict') else request.voice_characteristics

        if hasattr(request, 'emotional_tone') and request.emotional_tone:
            self.ai_client._current_emotional_tone = request.emotional_tone

        if hasattr(request, 'narrative_style') and request.narrative_style:
            self.ai_client._current_narrative_style = request.narrative_style

        # Store multi-speaker configuration for Gemini Native TTS
        if hasattr(request, 'speakers') and request.speakers:
            # Convert Pydantic models to dict for easier processing
            speakers_data = []
            for speaker in request.speakers:
                speaker_dict = speaker.dict() if hasattr(speaker, 'dict') else speaker
                speakers_data.append(speaker_dict)
            self.ai_client._current_speakers = speakers_data
        else:
            self.ai_client._current_speakers = None

        try:
            result = await self.ai_client.text_to_speech_async(
                text=request.text,
                voice=request.voice,
                language_code=request.language_code,
                audio_format=request.audio_format,
                speaking_rate=request.speaking_rate,
                pitch=request.pitch,
                volume_gain_db=request.volume_gain_db,
                sample_rate=request.sample_rate,
                effects_profile_id=request.effects_profile_id
            )

            return TTSResponse(
                audio_content=result["audio_content"],
                audio_format=result["audio_format"],
                sample_rate=result["sample_rate"],
                duration_seconds=result.get("duration_seconds"),
                voice_used=result["voice_used"],
                language_code=result["language_code"],
                text_length=result["text_length"]
            )

        except Exception as e:
            logger.error(f"Error in text-to-speech conversion: {str(e)}")
            raise

    # Context Caching Methods
    async def create_cache(self, request: CacheCreateRequest) -> CacheResponse:
        """Create a new context cache.

        Args:
            request: Cache creation request

        Returns:
            Cache response with created cache info
        """
        logger.info(f"Creating context cache for model {request.model}")

        try:
            return await context_cache_manager.create_cache(request)
        except Exception as e:
            logger.error(f"Error creating cache: {str(e)}")
            raise

    async def get_cache(self, cache_name: str) -> CacheResponse:
        """Get cache information.

        Args:
            cache_name: Name of the cache

        Returns:
            Cache response with cache info
        """
        logger.info(f"Getting cache: {cache_name}")

        try:
            return await context_cache_manager.get_cache(cache_name)
        except Exception as e:
            logger.error(f"Error getting cache: {str(e)}")
            raise

    async def list_caches(self, page_size: int = 10, page_token: Optional[str] = None) -> CacheListResponse:
        """List all context caches.

        Args:
            page_size: Number of caches per page
            page_token: Token for pagination

        Returns:
            List of caches
        """
        logger.info("Listing context caches")

        try:
            return await context_cache_manager.list_caches(page_size, page_token)
        except Exception as e:
            logger.error(f"Error listing caches: {str(e)}")
            raise

    async def update_cache(self, cache_name: str, request: CacheUpdateRequest) -> CacheResponse:
        """Update an existing context cache.

        Args:
            cache_name: Name of the cache to update
            request: Cache update request

        Returns:
            Cache response with updated cache info
        """
        logger.info(f"Updating cache: {cache_name}")

        try:
            return await context_cache_manager.update_cache(cache_name, request)
        except Exception as e:
            logger.error(f"Error updating cache: {str(e)}")
            raise

    async def delete_cache(self, cache_name: str) -> Dict[str, Any]:
        """Delete a context cache.

        Args:
            cache_name: Name of the cache to delete

        Returns:
            Deletion result
        """
        logger.info(f"Deleting cache: {cache_name}")

        try:
            return await context_cache_manager.delete_cache(cache_name)
        except Exception as e:
            logger.error(f"Error deleting cache: {str(e)}")
            raise

    async def generate_with_cache(
        self,
        request: CachedGenerationRequest,
        stream: bool = False
    ) -> Union[TextGenerationResponse, AsyncIterator[StreamChunk]]:
        """Generate content using cached context.

        Args:
            request: Cached generation request
            stream: Whether to stream the response

        Returns:
            Text generation response or streaming response
        """
        logger.info(f"Generating content with cache: {request.cache_name}")

        # Convert safety settings if provided
        safety_settings = None
        if request.safety_settings:
            safety_settings = [
                {
                    "category": setting.category.value,
                    "threshold": setting.threshold.value
                }
                for setting in request.safety_settings
            ]

        if stream or request.stream:
            # Return a streaming response
            async def response_generator():
                async_gen = await self.ai_client.generate_with_cache_async(
                    cache_name=request.cache_name,
                    prompt=request.prompt,
                    model=request.model,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p,
                    top_k=request.top_k,
                    safety_settings=safety_settings,
                    stream=True
                )

                async for chunk in async_gen:
                    yield StreamChunk(
                        text=chunk["text"],
                        is_final=chunk["is_final"]
                    ).json() + "\n"

            return response_generator()
        else:
            # Return a complete response
            result = await self.ai_client.generate_with_cache_async(
                cache_name=request.cache_name,
                prompt=request.prompt,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                safety_settings=safety_settings,
                stream=False
            )

            return TextGenerationResponse(
                text=result["text"],
                model=result["model"],
                usage=UsageInfo(
                    prompt_tokens=result["usage"].get("prompt_tokens", 0),
                    completion_tokens=result["usage"].get("completion_tokens", 0),
                    total_tokens=result["usage"].get("total_tokens", 0)
                ),
                finish_reason=result.get("finish_reason")
            )

    # Enhanced Function Calling Methods
    async def enhanced_function_calling(
        self,
        request: EnhancedFunctionCallingRequest
    ) -> EnhancedFunctionCallingResponse:
        """Enhanced function calling with chaining and parallel execution.

        Args:
            request: Enhanced function calling request

        Returns:
            Enhanced function calling response
        """
        logger.info("Processing enhanced function calling request")

        # Convert safety settings if provided
        safety_settings = None
        if request.safety_settings:
            safety_settings = [
                {
                    "category": setting.category.value,
                    "threshold": setting.threshold.value
                }
                for setting in request.safety_settings
            ]

        # Convert functions to the format expected by the AI client
        functions = []
        for func in request.functions:
            # Convert parameters to JSON Schema format
            parameters = {
                "type": "object",
                "properties": {},
                "required": []
            }

            for param in func.parameters:
                parameters["properties"][param.name] = {
                    "type": param.type,
                    "description": param.description or ""
                }

                if param.required:
                    parameters["required"].append(param.name)

            functions.append({
                "name": func.name,
                "description": func.description,
                "parameters": parameters
            })

        # Convert function chains if provided
        function_chains = None
        if request.function_chains:
            function_chains = []
            for chain in request.function_chains:
                chain_data = {
                    "name": chain.name,
                    "description": chain.description,
                    "steps": []
                }

                for step in chain.steps:
                    step_data = {
                        "function_name": step.function_name,
                        "arguments": step.arguments,
                        "depends_on": step.depends_on,
                        "condition": step.condition
                    }
                    if step.retry_config:
                        step_data["retry_config"] = step.retry_config.dict()

                    chain_data["steps"].append(step_data)

                if chain.execution_config:
                    chain_data["execution_config"] = chain.execution_config.dict()

                function_chains.append(chain_data)

        # Convert execution config
        execution_config = None
        if request.execution_config:
            execution_config = request.execution_config.dict()

        try:
            result = await self.ai_client.enhanced_function_calling_async(
                prompt=request.prompt,
                functions=functions,
                function_chains=function_chains,
                execution_config=execution_config,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                system_instruction=request.system_instruction,
                safety_settings=safety_settings,
                json_mode=getattr(request, 'json_mode', False),
                json_schema=getattr(request, 'json_schema', None)
            )

            # Convert function results
            function_results = []
            if result.get("function_results"):
                for func_result in result["function_results"]:
                    function_results.append(FunctionExecutionResult(**func_result))

            # Convert chain results
            chain_results = []
            if result.get("chain_results"):
                for chain_result in result["chain_results"]:
                    # Convert step results
                    step_results = []
                    for step_result in chain_result.get("step_results", []):
                        step_results.append(FunctionExecutionResult(**step_result))

                    chain_result["step_results"] = step_results
                    chain_results.append(FunctionChainResult(**chain_result))

            return EnhancedFunctionCallingResponse(
                text=result["text"],
                model=result["model"],
                function_calls=result.get("function_calls"),
                function_results=function_results,
                chain_results=chain_results,
                usage=UsageInfo(
                    prompt_tokens=result["usage"].get("prompt_tokens", 0),
                    completion_tokens=result["usage"].get("completion_tokens", 0),
                    total_tokens=result["usage"].get("total_tokens", 0)
                ) if result.get("usage") else None,
                finish_reason=result.get("finish_reason"),
                execution_summary=result.get("execution_summary"),
                json_mode=result.get("json_mode", False),
                json_data=result.get("json_data")
            )

        except Exception as e:
            logger.error(f"Error in enhanced function calling: {str(e)}")
            raise

    async def register_function(
        self,
        name: str,
        func: Callable,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Register a function for enhanced function calling.

        Args:
            name: Function name
            func: Function to register
            metadata: Function metadata

        Returns:
            Registration result
        """
        try:
            # Convert metadata if provided
            enhanced_metadata = None
            if metadata:
                from ai_service.models.schemas import EnhancedFunctionDeclaration, FunctionParameter

                parameters = []
                if "parameters" in metadata:
                    for param_data in metadata["parameters"]:
                        parameters.append(FunctionParameter(**param_data))

                enhanced_metadata = EnhancedFunctionDeclaration(
                    name=name,
                    description=metadata.get("description", f"Function {name}"),
                    parameters=parameters,
                    category=metadata.get("category"),
                    tags=metadata.get("tags"),
                    version=metadata.get("version", "1.0"),
                    execution_config=FunctionExecutionConfig(**metadata["execution_config"]) if metadata.get("execution_config") else None,
                    examples=metadata.get("examples")
                )

            enhanced_function_executor.register_function(name, func, enhanced_metadata)

            logger.info(f"Function '{name}' registered successfully")

            return {
                "success": True,
                "message": f"Function '{name}' registered successfully",
                "function_name": name,
                "registered_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error registering function '{name}': {str(e)}")
            raise

    async def unregister_function(self, name: str) -> Dict[str, Any]:
        """Unregister a function.

        Args:
            name: Function name to unregister

        Returns:
            Unregistration result
        """
        try:
            enhanced_function_executor.unregister_function(name)

            logger.info(f"Function '{name}' unregistered successfully")

            return {
                "success": True,
                "message": f"Function '{name}' unregistered successfully",
                "function_name": name,
                "unregistered_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error unregistering function '{name}': {str(e)}")
            raise

    async def list_registered_functions(self) -> Dict[str, Any]:
        """List all registered functions.

        Returns:
            List of registered functions
        """
        try:
            functions = enhanced_function_executor.list_functions()

            return {
                "functions": [func.dict() for func in functions],
                "total_count": len(functions),
                "listed_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error listing functions: {str(e)}")
            raise

    # Performance Optimization Methods
    async def generate_with_cache(
        self,
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """Generate text with caching optimization."""
        logger.info("Processing cached text generation request")

        # Convert safety settings if provided
        safety_settings = None
        if request.safety_settings:
            safety_settings = [
                {
                    "category": setting.category.value,
                    "threshold": setting.threshold.value
                }
                for setting in request.safety_settings
            ]

        try:
            result = await self.ai_client.generate_with_cache(
                prompt=request.prompt,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                system_instruction=request.system_instruction,
                safety_settings=safety_settings,
                cache_ttl=getattr(request, 'cache_ttl', None)
            )

            return TextGenerationResponse(
                text=result["text"],
                model=result["model"],
                usage=UsageInfo(
                    prompt_tokens=result["usage"].get("prompt_tokens", 0),
                    completion_tokens=result["usage"].get("completion_tokens", 0),
                    total_tokens=result["usage"].get("total_tokens", 0)
                ) if result.get("usage") else None,
                finish_reason=result.get("finish_reason"),
                cached=result.get("cached", False)
            )

        except Exception as e:
            logger.error(f"Error in cached text generation: {str(e)}")
            raise

    async def embeddings_with_cache(
        self,
        request: EmbeddingRequest
    ) -> EmbeddingResponse:
        """Generate embeddings with caching optimization."""
        logger.info("Processing cached embedding request")

        try:
            result = await self.ai_client.embeddings_with_cache(
                texts=request.texts,
                model=request.model,
                cache_ttl=getattr(request, 'cache_ttl', None)
            )

            return EmbeddingResponse(
                embeddings=result["embeddings"],
                model=result["model"],
                usage=UsageInfo(
                    prompt_tokens=len(request.texts),
                    completion_tokens=0,
                    total_tokens=len(request.texts)
                ),
                cached_count=result.get("cached_count", 0),
                new_count=result.get("new_count", 0)
            )

        except Exception as e:
            logger.error(f"Error in cached embeddings: {str(e)}")
            raise

    async def batch_generate(
        self,
        prompts: List[str],
        model: Optional[str] = None,
        batch_config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Process multiple text generation requests in batch."""
        logger.info(f"Processing batch generation for {len(prompts)} prompts")

        try:
            batch_id = await self.ai_client.batch_generate(
                prompts=prompts,
                model=model,
                **kwargs
            )

            return {
                "batch_id": batch_id,
                "total_prompts": len(prompts),
                "status": "submitted",
                "submitted_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error in batch generation: {str(e)}")
            raise

    async def batch_embeddings(
        self,
        texts: List[str],
        model: Optional[str] = None,
        batch_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process multiple embedding requests in batch."""
        logger.info(f"Processing batch embeddings for {len(texts)} texts")

        try:
            batch_id = await self.ai_client.batch_embeddings(
                texts=texts,
                model=model
            )

            return {
                "batch_id": batch_id,
                "total_texts": len(texts),
                "status": "submitted",
                "submitted_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error in batch embeddings: {str(e)}")
            raise

    async def get_batch_status(self, batch_id: str) -> Dict[str, Any]:
        """Get status of a batch job."""
        try:
            job = await batch_processor.get_job_status(batch_id)

            if not job:
                return {
                    "batch_id": batch_id,
                    "status": "not_found",
                    "error": "Batch job not found"
                }

            return {
                "batch_id": batch_id,
                "status": job.status.value,
                "progress": job.progress,
                "total_items": job.total_items,
                "processed_items": job.processed_items,
                "failed_items": job.failed_items,
                "created_at": job.created_at,
                "started_at": job.started_at,
                "completed_at": job.completed_at
            }

        except Exception as e:
            logger.error(f"Error getting batch status: {str(e)}")
            raise

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            stats = cache_manager.get_cache_stats()

            return {
                "cache_stats": {
                    cache_type: {
                        "hits": cache_stats.hits,
                        "misses": cache_stats.misses,
                        "hit_rate": cache_stats.hit_rate,
                        "total_entries": cache_stats.total_entries,
                        "total_size_bytes": cache_stats.total_size_bytes,
                        "evictions": cache_stats.evictions
                    }
                    for cache_type, cache_stats in stats.items()
                },
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Error getting cache stats: {str(e)}")
            raise

    async def clear_cache(self, cache_type: Optional[str] = None) -> Dict[str, Any]:
        """Clear cache entries."""
        try:
            if cache_type:
                # Clear specific cache type
                cache = cache_manager._get_cache_by_type(cache_type)
                cache.clear()
                message = f"Cleared {cache_type} cache"
            else:
                # Clear all caches
                cache_manager.clear_all_caches()
                message = "Cleared all caches"

            logger.info(message)

            return {
                "success": True,
                "message": message,
                "cleared_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error clearing cache: {str(e)}")
            raise

    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        try:
            # Get performance monitor stats
            perf_summary = performance_monitor.get_performance_summary()

            # Get batch processor stats
            batch_stats = batch_processor.get_statistics()

            # Get cache stats
            cache_stats = await self.get_cache_stats()

            return {
                "performance": perf_summary,
                "batch_processing": batch_stats,
                "caching": cache_stats["cache_stats"],
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Error getting performance stats: {str(e)}")
            raise

    # Advanced Streaming Methods
    async def create_universal_stream(
        self,
        stream_type: str,
        client_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create universal stream for any AI operation."""
        logger.info(f"Creating universal stream of type {stream_type}")

        try:
            # Validate stream type
            try:
                stream_type_enum = StreamType(stream_type)
            except ValueError:
                raise ValueError(f"Invalid stream type: {stream_type}")

            # Create stream via AI client
            stream_id = await self.ai_client.create_universal_stream(
                stream_type=stream_type_enum,
                client_id=client_id,
                metadata=metadata
            )

            return {
                "stream_id": stream_id,
                "stream_type": stream_type,
                "client_id": client_id,
                "status": "created",
                "created_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error creating universal stream: {str(e)}")
            raise

    async def get_stream_status(self, stream_id: str) -> Dict[str, Any]:
        """Get stream status information."""
        try:
            status = await self.ai_client.get_stream_status(stream_id)

            if not status:
                return {
                    "stream_id": stream_id,
                    "status": "not_found",
                    "error": "Stream not found"
                }

            return status

        except Exception as e:
            logger.error(f"Error getting stream status: {str(e)}")
            raise

    async def cancel_stream(self, stream_id: str) -> Dict[str, Any]:
        """Cancel an active stream."""
        try:
            success = await self.ai_client.cancel_stream(stream_id)

            return {
                "stream_id": stream_id,
                "status": "cancelled" if success else "failed",
                "cancelled_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error cancelling stream: {str(e)}")
            raise

    async def pause_stream(self, stream_id: str) -> Dict[str, Any]:
        """Pause an active stream."""
        try:
            success = await self.ai_client.pause_stream(stream_id)

            return {
                "stream_id": stream_id,
                "status": "paused" if success else "failed",
                "paused_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error pausing stream: {str(e)}")
            raise

    async def resume_stream(self, stream_id: str) -> Dict[str, Any]:
        """Resume a paused stream."""
        try:
            success = await self.ai_client.resume_stream(stream_id)

            return {
                "stream_id": stream_id,
                "status": "resumed" if success else "failed",
                "resumed_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error resuming stream: {str(e)}")
            raise

    # Real-time Collaboration Methods
    async def create_collaboration_session(
        self,
        session_name: str,
        creator_client_id: str,
        session_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create real-time collaboration session."""
        logger.info(f"Creating collaboration session: {session_name}")

        try:
            session_id = await self.ai_client.create_collaboration_session(
                session_name=session_name,
                creator_client_id=creator_client_id,
                session_config=session_config
            )

            return {
                "session_id": session_id,
                "session_name": session_name,
                "creator": creator_client_id,
                "status": "created",
                "created_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error creating collaboration session: {str(e)}")
            raise

    async def join_collaboration_session(
        self,
        session_id: str,
        client_id: str
    ) -> Dict[str, Any]:
        """Join collaboration session."""
        logger.info(f"Client {client_id} joining collaboration session {session_id}")

        try:
            success = await self.ai_client.join_collaboration_session(
                session_id=session_id,
                client_id=client_id
            )

            return {
                "session_id": session_id,
                "client_id": client_id,
                "status": "joined" if success else "failed",
                "joined_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error joining collaboration session: {str(e)}")
            raise

    async def leave_collaboration_session(self, client_id: str) -> Dict[str, Any]:
        """Leave collaboration session."""
        logger.info(f"Client {client_id} leaving collaboration session")

        try:
            success = await self.ai_client.leave_collaboration_session(client_id)

            return {
                "client_id": client_id,
                "status": "left" if success else "failed",
                "left_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error leaving collaboration session: {str(e)}")
            raise

    async def update_shared_context(
        self,
        session_id: str,
        client_id: str,
        updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update shared context in collaboration session."""
        logger.info(f"Updating shared context in session {session_id}")

        try:
            success = await self.ai_client.update_shared_context(
                session_id=session_id,
                client_id=client_id,
                updates=updates
            )

            return {
                "session_id": session_id,
                "client_id": client_id,
                "status": "updated" if success else "failed",
                "updated_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error updating shared context: {str(e)}")
            raise

    async def get_shared_context(self, session_id: str) -> Dict[str, Any]:
        """Get shared context from collaboration session."""
        try:
            context = await self.ai_client.get_shared_context(session_id)

            if not context:
                return {
                    "session_id": session_id,
                    "status": "not_found",
                    "error": "Session not found"
                }

            return context

        except Exception as e:
            logger.error(f"Error getting shared context: {str(e)}")
            raise

    async def start_collaborative_stream(
        self,
        session_id: str,
        stream_type: str,
        initiator_client_id: str,
        stream_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Start collaborative streaming session."""
        logger.info(f"Starting collaborative stream in session {session_id}")

        try:
            # Validate stream type
            try:
                stream_type_enum = StreamType(stream_type)
            except ValueError:
                raise ValueError(f"Invalid stream type: {stream_type}")

            stream_id = await self.ai_client.start_collaborative_stream(
                session_id=session_id,
                stream_type=stream_type_enum,
                initiator_client_id=initiator_client_id,
                stream_config=stream_config
            )

            return {
                "session_id": session_id,
                "stream_id": stream_id,
                "stream_type": stream_type,
                "initiator": initiator_client_id,
                "status": "started",
                "started_at": time.time()
            }

        except Exception as e:
            logger.error(f"Error starting collaborative stream: {str(e)}")
            raise

    # Streaming Statistics Methods
    async def get_streaming_stats(self) -> Dict[str, Any]:
        """Get comprehensive streaming statistics."""
        try:
            streaming_stats = streaming_manager.get_streaming_stats()
            websocket_stats = websocket_manager.get_websocket_stats()
            sse_stats = sse_manager.get_sse_stats()
            collaboration_stats = collaboration_manager.get_collaboration_stats()

            return {
                "streaming": streaming_stats,
                "websocket": websocket_stats,
                "sse": sse_stats,
                "collaboration": collaboration_stats,
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Error getting streaming stats: {str(e)}")
            raise

    async def get_client_streams(self, client_id: str) -> Dict[str, Any]:
        """Get all streams for a client."""
        try:
            streams = streaming_manager.get_client_streams(client_id)

            return {
                "client_id": client_id,
                "streams": [
                    {
                        "stream_id": stream.id,
                        "stream_type": stream.stream_type.value,
                        "status": stream.status.value,
                        "created_at": stream.created_at.isoformat(),
                        "chunks_sent": stream.chunks_sent,
                        "bytes_sent": stream.bytes_sent
                    }
                    for stream in streams
                ],
                "total_streams": len(streams)
            }

        except Exception as e:
            logger.error(f"Error getting client streams: {str(e)}")
            raise
