"""
Configuration settings for the AI service.
"""
import os
from typing import Optional, Dict, List, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Google AI Studio API settings
    google_api_key: str = Field(..., env="GOOGLE_API_KEY")

    # Application settings
    app_env: str = Field("development", env="APP_ENV")
    app_version: str = Field("1.0.0", env="APP_VERSION")
    app_name: str = Field("AI Service", env="APP_NAME")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    port: int = Field(8000, env="PORT")
    host: str = Field("0.0.0.0", env="HOST")

    # Production settings
    debug: bool = Field(False, env="DEBUG")
    testing: bool = Field(False, env="TESTING")
    cors_origins: List[str] = Field(["*"], env="CORS_ORIGINS")

    # Security settings
    secret_key: str = Field("dev-secret-key", env="SECRET_KEY")
    api_key_header: str = Field("X-API-Key", env="API_KEY_HEADER")
    enable_rate_limiting: bool = Field(True, env="ENABLE_RATE_LIMITING")
    max_requests_per_minute: int = Field(60, env="MAX_REQUESTS_PER_MINUTE")
    max_requests_per_hour: int = Field(1000, env="MAX_REQUESTS_PER_HOUR")

    # Monitoring settings
    enable_metrics: bool = Field(True, env="ENABLE_METRICS")
    metrics_port: int = Field(9090, env="METRICS_PORT")
    health_check_interval: int = Field(60, env="HEALTH_CHECK_INTERVAL")

    # Performance settings
    max_workers: int = Field(4, env="MAX_WORKERS")
    request_timeout: int = Field(300, env="REQUEST_TIMEOUT")
    max_request_size: int = Field(10 * 1024 * 1024, env="MAX_REQUEST_SIZE")  # 10MB

    # Cache settings
    enable_caching: bool = Field(True, env="ENABLE_CACHING")
    cache_ttl: int = Field(3600, env="CACHE_TTL")  # 1 hour
    max_cache_size: int = Field(1000, env="MAX_CACHE_SIZE")

    # AI model settings
    default_model: str = Field("gemini-2.0-flash", env="DEFAULT_MODEL")
    default_embedding_model: str = Field("gemini-embedding-exp-03-07", env="DEFAULT_EMBEDDING_MODEL")

    # Temporary file storage
    temp_file_dir: str = Field("temp_files", env="TEMP_FILE_DIR")

    # Default safety settings
    default_safety_settings: List[Dict[str, str]] = [
        {
            "category": "HARM_CATEGORY_HARASSMENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_HATE_SPEECH",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]

    # Available models
    available_models: Dict[str, Dict[str, Any]] = {
        "gemini-2.0-flash": {
            "description": "Fastest model for text generation",
            "max_tokens": 8192,
            "supports_multimodal": True,
            "supports_function_calling": True
        },
        "gemini-2.0-pro": {
            "description": "Most capable model for complex tasks",
            "max_tokens": 32768,
            "supports_multimodal": True,
            "supports_function_calling": True
        },
        "gemini-embedding-exp-03-07": {
            "description": "Model for generating embeddings",
            "max_tokens": 8192,
            "supports_multimodal": False,
            "supports_function_calling": False
        }
    }

    @validator('app_env')
    def validate_app_env(cls, v):
        """Validate application environment."""
        allowed_envs = ['development', 'staging', 'production']
        if v not in allowed_envs:
            raise ValueError(f'app_env must be one of {allowed_envs}')
        return v

    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        allowed_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in allowed_levels:
            raise ValueError(f'log_level must be one of {allowed_levels}')
        return v.upper()

    @validator('secret_key')
    def validate_secret_key(cls, v, values):
        """Validate secret key for production."""
        if values.get('app_env') == 'production' and v == 'dev-secret-key':
            raise ValueError('Must set a secure SECRET_KEY for production')
        # Only enforce length requirement in production
        if values.get('app_env') == 'production' and len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters long for production')
        return v

    @property
    def is_production(self) -> bool:
        """Check if running in production."""
        return self.app_env == 'production'

    @property
    def is_development(self) -> bool:
        """Check if running in development."""
        return self.app_env == 'development'

    @property
    def is_testing(self) -> bool:
        """Check if running in testing mode."""
        return self.testing or self.app_env == 'testing'

    def get_cors_origins(self) -> List[str]:
        """Get CORS origins based on environment."""
        if self.is_production:
            # In production, be more restrictive
            return [origin for origin in self.cors_origins if origin != "*"]
        return self.cors_origins

    def get_log_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "[%(asctime)s] %(levelname)s in %(module)s: %(message)s",
                },
                "detailed": {
                    "format": "[%(asctime)s] %(levelname)s in %(module)s [%(pathname)s:%(lineno)d]: %(message)s",
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": self.log_level,
                    "formatter": "detailed" if self.debug else "default",
                    "stream": "ext://sys.stdout"
                }
            },
            "root": {
                "level": self.log_level,
                "handlers": ["console"]
            }
        }

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Create temp file directory if it doesn't exist
        if not os.path.exists(self.temp_file_dir):
            os.makedirs(self.temp_file_dir)

    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_file_encoding = "utf-8"


# Create settings instance
settings = Settings()
