"""
Enhanced logging system for AI service with structured logging and performance metrics.
"""
import logging
import json
import time
import traceback
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from contextlib import contextmanager

from ai_service.core.error_handler import E<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorSeverity


class LogLevel(Enum):
    """Enhanced log levels."""
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"
    PERFORMANCE = "PERFORMANCE"
    SECURITY = "SECURITY"


@dataclass
class LogContext:
    """Context information for structured logging."""
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    endpoint: Optional[str] = None
    model: Optional[str] = None
    stream_id: Optional[str] = None
    client_ip: Optional[str] = None
    user_agent: Optional[str] = None
    correlation_id: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceMetrics:
    """Performance metrics for logging."""
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    tokens_processed: Optional[int] = None
    bytes_processed: Optional[int] = None
    
    def complete(self):
        """Mark metrics as complete."""
        self.end_time = time.time()
        self.duration_ms = (self.end_time - self.start_time) * 1000


class StructuredLogger:
    """Enhanced structured logger with performance tracking."""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.context_stack: List[LogContext] = []
        self.performance_stack: List[PerformanceMetrics] = []
        self._local = threading.local()
    
    def _get_current_context(self) -> LogContext:
        """Get current logging context."""
        if hasattr(self._local, 'context') and self._local.context:
            return self._local.context
        elif self.context_stack:
            return self.context_stack[-1]
        else:
            return LogContext()
    
    def _format_log_entry(
        self,
        level: LogLevel,
        message: str,
        context: Optional[LogContext] = None,
        extra_data: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None,
        performance: Optional[PerformanceMetrics] = None
    ) -> Dict[str, Any]:
        """Format log entry as structured JSON."""
        ctx = context or self._get_current_context()
        
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level.value,
            "message": message,
            "logger": self.logger.name,
            "thread_id": threading.get_ident(),
        }
        
        # Add context information
        if ctx.request_id:
            log_entry["request_id"] = ctx.request_id
        if ctx.user_id:
            log_entry["user_id"] = ctx.user_id
        if ctx.session_id:
            log_entry["session_id"] = ctx.session_id
        if ctx.endpoint:
            log_entry["endpoint"] = ctx.endpoint
        if ctx.model:
            log_entry["model"] = ctx.model
        if ctx.stream_id:
            log_entry["stream_id"] = ctx.stream_id
        if ctx.client_ip:
            log_entry["client_ip"] = ctx.client_ip
        if ctx.user_agent:
            log_entry["user_agent"] = ctx.user_agent
        if ctx.correlation_id:
            log_entry["correlation_id"] = ctx.correlation_id
        
        # Add additional context data
        if ctx.additional_data:
            log_entry["context"] = ctx.additional_data
        
        # Add extra data
        if extra_data:
            log_entry["data"] = extra_data
        
        # Add error information
        if error:
            log_entry["error"] = {
                "type": type(error).__name__,
                "message": str(error),
                "traceback": traceback.format_exc()
            }
        
        # Add performance metrics
        if performance:
            log_entry["performance"] = {
                "duration_ms": performance.duration_ms,
                "memory_usage_mb": performance.memory_usage_mb,
                "cpu_usage_percent": performance.cpu_usage_percent,
                "tokens_processed": performance.tokens_processed,
                "bytes_processed": performance.bytes_processed
            }
        
        return log_entry
    
    def _log(
        self,
        level: LogLevel,
        message: str,
        context: Optional[LogContext] = None,
        extra_data: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None,
        performance: Optional[PerformanceMetrics] = None
    ):
        """Internal logging method."""
        log_entry = self._format_log_entry(level, message, context, extra_data, error, performance)
        
        # Convert to JSON string for structured logging
        log_message = json.dumps(log_entry, default=str)
        
        # Map to standard logging levels
        level_map = {
            LogLevel.TRACE: logging.DEBUG,
            LogLevel.DEBUG: logging.DEBUG,
            LogLevel.INFO: logging.INFO,
            LogLevel.WARNING: logging.WARNING,
            LogLevel.ERROR: logging.ERROR,
            LogLevel.CRITICAL: logging.CRITICAL,
            LogLevel.PERFORMANCE: logging.INFO,
            LogLevel.SECURITY: logging.WARNING,
        }
        
        self.logger.log(level_map[level], log_message)
    
    def trace(self, message: str, **kwargs):
        """Log trace message."""
        self._log(LogLevel.TRACE, message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self._log(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self._log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self._log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, error: Optional[Exception] = None, **kwargs):
        """Log error message."""
        self._log(LogLevel.ERROR, message, error=error, **kwargs)
    
    def critical(self, message: str, error: Optional[Exception] = None, **kwargs):
        """Log critical message."""
        self._log(LogLevel.CRITICAL, message, error=error, **kwargs)
    
    def performance(self, message: str, performance: PerformanceMetrics, **kwargs):
        """Log performance metrics."""
        self._log(LogLevel.PERFORMANCE, message, performance=performance, **kwargs)
    
    def security(self, message: str, **kwargs):
        """Log security-related message."""
        self._log(LogLevel.SECURITY, message, **kwargs)
    
    @contextmanager
    def context(self, context: LogContext):
        """Context manager for logging context."""
        self._local.context = context
        try:
            yield
        finally:
            self._local.context = None
    
    @contextmanager
    def performance_tracking(self, operation_name: str, **kwargs):
        """Context manager for performance tracking."""
        metrics = PerformanceMetrics(start_time=time.time())
        
        try:
            yield metrics
        finally:
            metrics.complete()
            self.performance(f"Operation completed: {operation_name}", performance=metrics, **kwargs)


class AIServiceLogger:
    """Main logger for AI service with specialized methods."""
    
    def __init__(self):
        self.logger = StructuredLogger("ai_service")
        self.request_logger = StructuredLogger("ai_service.requests")
        self.model_logger = StructuredLogger("ai_service.models")
        self.streaming_logger = StructuredLogger("ai_service.streaming")
        self.security_logger = StructuredLogger("ai_service.security")
    
    def log_request_start(
        self,
        endpoint: str,
        method: str,
        context: LogContext,
        request_data: Optional[Dict[str, Any]] = None
    ):
        """Log request start."""
        extra_data = {
            "method": method,
            "request_size": len(json.dumps(request_data, default=str)) if request_data else 0
        }
        
        self.request_logger.info(
            f"Request started: {method} {endpoint}",
            context=context,
            extra_data=extra_data
        )
    
    def log_request_end(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        context: LogContext,
        performance: PerformanceMetrics,
        response_size: Optional[int] = None
    ):
        """Log request completion."""
        extra_data = {
            "method": method,
            "status_code": status_code,
            "response_size": response_size or 0
        }
        
        self.request_logger.performance(
            f"Request completed: {method} {endpoint} - {status_code}",
            performance=performance,
            context=context,
            extra_data=extra_data
        )
    
    def log_model_call(
        self,
        model: str,
        operation: str,
        context: LogContext,
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None,
        performance: Optional[PerformanceMetrics] = None
    ):
        """Log model API call."""
        extra_data = {
            "operation": operation,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens
        }
        
        if performance:
            self.model_logger.performance(
                f"Model call: {model} - {operation}",
                performance=performance,
                context=context,
                extra_data=extra_data
            )
        else:
            self.model_logger.info(
                f"Model call: {model} - {operation}",
                context=context,
                extra_data=extra_data
            )
    
    def log_streaming_event(
        self,
        event_type: str,
        stream_id: str,
        context: LogContext,
        extra_data: Optional[Dict[str, Any]] = None
    ):
        """Log streaming event."""
        self.streaming_logger.info(
            f"Streaming event: {event_type}",
            context=context,
            extra_data={"stream_id": stream_id, **(extra_data or {})}
        )
    
    def log_security_event(
        self,
        event_type: str,
        severity: str,
        context: LogContext,
        details: Optional[Dict[str, Any]] = None
    ):
        """Log security event."""
        extra_data = {
            "event_type": event_type,
            "severity": severity,
            "details": details or {}
        }
        
        self.security_logger.security(
            f"Security event: {event_type}",
            context=context,
            extra_data=extra_data
        )
    
    def log_error(
        self,
        error: Exception,
        context: LogContext,
        category: Optional[ErrorCategory] = None,
        severity: Optional[ErrorSeverity] = None
    ):
        """Log error with enhanced context."""
        extra_data = {}
        if category:
            extra_data["error_category"] = category.value
        if severity:
            extra_data["error_severity"] = severity.value
        
        self.logger.error(
            f"Error occurred: {type(error).__name__}",
            error=error,
            context=context,
            extra_data=extra_data
        )


# Global logger instance
ai_logger = AIServiceLogger()


# Convenience functions
def get_logger(name: str = "ai_service") -> StructuredLogger:
    """Get a structured logger instance."""
    return StructuredLogger(name)


def create_log_context(
    request_id: Optional[str] = None,
    user_id: Optional[str] = None,
    endpoint: Optional[str] = None,
    **kwargs
) -> LogContext:
    """Create a log context."""
    return LogContext(
        request_id=request_id,
        user_id=user_id,
        endpoint=endpoint,
        **kwargs
    )


def start_performance_tracking() -> PerformanceMetrics:
    """Start performance tracking."""
    return PerformanceMetrics(start_time=time.time())
