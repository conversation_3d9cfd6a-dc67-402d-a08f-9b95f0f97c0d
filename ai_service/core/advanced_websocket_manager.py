"""
Advanced WebSocket Manager for Real-time AI Interactions.
Supports bidirectional communication, room management, and collaborative features.
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Set, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from fastapi import WebSocket, WebSocketDisconnect
from collections import defaultdict

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """WebSocket message types."""
    # Client to server
    JOIN_ROOM = "join_room"
    LEAVE_ROOM = "leave_room"
    AI_REQUEST = "ai_request"
    STREAM_REQUEST = "stream_request"
    PING = "ping"
    
    # Server to client
    ROOM_JOINED = "room_joined"
    ROOM_LEFT = "room_left"
    AI_RESPONSE = "ai_response"
    STREAM_CHUNK = "stream_chunk"
    STREAM_COMPLETE = "stream_complete"
    ERROR = "error"
    PONG = "pong"
    
    # Collaborative
    USER_JOINED = "user_joined"
    USER_LEFT = "user_left"
    BROADCAST = "broadcast"


class ConnectionState(Enum):
    """WebSocket connection states."""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATED = "authenticated"
    DISCONNECTED = "disconnected"
    ERROR = "error"


@dataclass
class WebSocketConnection:
    """WebSocket connection metadata."""
    websocket: WebSocket
    connection_id: str
    user_id: Optional[str] = None
    rooms: Set[str] = field(default_factory=set)
    state: ConnectionState = ConnectionState.CONNECTING
    connected_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_active(self) -> bool:
        """Check if connection is active."""
        return self.state in [ConnectionState.CONNECTED, ConnectionState.AUTHENTICATED]
    
    @property
    def idle_seconds(self) -> float:
        """Get idle time in seconds."""
        return time.time() - self.last_activity


@dataclass
class Room:
    """WebSocket room for collaborative features."""
    room_id: str
    name: str
    connections: Set[str] = field(default_factory=set)
    created_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    max_connections: int = 100
    
    @property
    def connection_count(self) -> int:
        """Get number of connections in room."""
        return len(self.connections)
    
    @property
    def is_full(self) -> bool:
        """Check if room is at capacity."""
        return self.connection_count >= self.max_connections


@dataclass
class WebSocketStats:
    """WebSocket performance statistics."""
    total_connections: int = 0
    active_connections: int = 0
    total_messages: int = 0
    messages_per_second: float = 0.0
    average_response_time: float = 0.0
    error_count: int = 0
    rooms_count: int = 0


class AdvancedWebSocketManager:
    """Advanced WebSocket manager with real-time features."""
    
    def __init__(self):
        self.connections: Dict[str, WebSocketConnection] = {}
        self.rooms: Dict[str, Room] = {}
        self.message_handlers: Dict[MessageType, Callable] = {}
        self.stats = WebSocketStats()
        
        # Performance tracking
        self.message_times: List[float] = []
        self.last_stats_update = time.time()
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._stats_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Register default message handlers
        self._register_default_handlers()
        
        logger.info("Advanced WebSocket Manager initialized")
    
    async def start(self):
        """Start background management tasks."""
        if self._running:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._background_cleanup())
        self._stats_task = asyncio.create_task(self._background_stats())
        
        logger.info("WebSocket Manager background tasks started")
    
    async def stop(self):
        """Stop background tasks and cleanup."""
        self._running = False
        
        # Cancel background tasks
        for task in [self._cleanup_task, self._stats_task]:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Close all connections
        for connection in list(self.connections.values()):
            await self.disconnect(connection.connection_id)
        
        logger.info("WebSocket Manager stopped")
    
    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None) -> str:
        """Handle new WebSocket connection."""
        connection_id = str(uuid.uuid4())
        
        try:
            await websocket.accept()
            
            connection = WebSocketConnection(
                websocket=websocket,
                connection_id=connection_id,
                user_id=user_id,
                state=ConnectionState.CONNECTED
            )
            
            self.connections[connection_id] = connection
            self.stats.total_connections += 1
            self.stats.active_connections += 1
            
            logger.info(f"WebSocket connected: {connection_id} (user: {user_id})")
            
            # Send connection confirmation
            await self.send_message(connection_id, {
                "type": MessageType.ROOM_JOINED.value,
                "connection_id": connection_id,
                "timestamp": time.time()
            })
            
            return connection_id
            
        except Exception as e:
            logger.error(f"Error connecting WebSocket: {e}")
            raise
    
    async def disconnect(self, connection_id: str):
        """Handle WebSocket disconnection."""
        if connection_id not in self.connections:
            return
        
        connection = self.connections[connection_id]
        
        try:
            # Leave all rooms
            for room_id in list(connection.rooms):
                await self.leave_room(connection_id, room_id)
            
            # Close WebSocket
            if connection.websocket and connection.is_active:
                await connection.websocket.close()
            
            # Update state
            connection.state = ConnectionState.DISCONNECTED
            
            # Remove from connections
            del self.connections[connection_id]
            self.stats.active_connections = max(0, self.stats.active_connections - 1)
            
            logger.info(f"WebSocket disconnected: {connection_id}")
            
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket {connection_id}: {e}")
    
    async def send_message(self, connection_id: str, message: Dict[str, Any]) -> bool:
        """Send message to specific connection."""
        if connection_id not in self.connections:
            return False
        
        connection = self.connections[connection_id]
        
        if not connection.is_active:
            return False
        
        try:
            message_json = json.dumps(message)
            await connection.websocket.send_text(message_json)
            
            # Update activity
            connection.last_activity = time.time()
            self.stats.total_messages += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending message to {connection_id}: {e}")
            await self.disconnect(connection_id)
            return False
    
    async def broadcast_to_room(self, room_id: str, message: Dict[str, Any], exclude: Optional[str] = None):
        """Broadcast message to all connections in a room."""
        if room_id not in self.rooms:
            return
        
        room = self.rooms[room_id]
        sent_count = 0
        
        for connection_id in list(room.connections):
            if exclude and connection_id == exclude:
                continue
            
            success = await self.send_message(connection_id, message)
            if success:
                sent_count += 1
        
        logger.debug(f"Broadcasted message to {sent_count} connections in room {room_id}")
    
    async def join_room(self, connection_id: str, room_id: str, room_name: Optional[str] = None) -> bool:
        """Add connection to a room."""
        if connection_id not in self.connections:
            return False
        
        connection = self.connections[connection_id]
        
        # Create room if it doesn't exist
        if room_id not in self.rooms:
            self.rooms[room_id] = Room(
                room_id=room_id,
                name=room_name or room_id
            )
            self.stats.rooms_count += 1
        
        room = self.rooms[room_id]
        
        # Check room capacity
        if room.is_full:
            await self.send_message(connection_id, {
                "type": MessageType.ERROR.value,
                "error": "Room is full",
                "room_id": room_id
            })
            return False
        
        # Add to room
        room.connections.add(connection_id)
        connection.rooms.add(room_id)
        
        # Notify user
        await self.send_message(connection_id, {
            "type": MessageType.ROOM_JOINED.value,
            "room_id": room_id,
            "room_name": room.name,
            "connection_count": room.connection_count
        })
        
        # Notify other users in room
        await self.broadcast_to_room(room_id, {
            "type": MessageType.USER_JOINED.value,
            "connection_id": connection_id,
            "user_id": connection.user_id,
            "room_id": room_id,
            "connection_count": room.connection_count
        }, exclude=connection_id)
        
        logger.info(f"Connection {connection_id} joined room {room_id}")
        return True
    
    async def leave_room(self, connection_id: str, room_id: str) -> bool:
        """Remove connection from a room."""
        if connection_id not in self.connections or room_id not in self.rooms:
            return False
        
        connection = self.connections[connection_id]
        room = self.rooms[room_id]
        
        # Remove from room
        room.connections.discard(connection_id)
        connection.rooms.discard(room_id)
        
        # Notify user
        await self.send_message(connection_id, {
            "type": MessageType.ROOM_LEFT.value,
            "room_id": room_id
        })
        
        # Notify other users in room
        await self.broadcast_to_room(room_id, {
            "type": MessageType.USER_LEFT.value,
            "connection_id": connection_id,
            "user_id": connection.user_id,
            "room_id": room_id,
            "connection_count": room.connection_count
        })
        
        # Remove empty room
        if room.connection_count == 0:
            del self.rooms[room_id]
            self.stats.rooms_count = max(0, self.stats.rooms_count - 1)
        
        logger.info(f"Connection {connection_id} left room {room_id}")
        return True
    
    async def handle_message(self, connection_id: str, message: str):
        """Handle incoming WebSocket message."""
        if connection_id not in self.connections:
            return
        
        connection = self.connections[connection_id]
        
        try:
            # Parse message
            data = json.loads(message)
            message_type = MessageType(data.get("type"))
            
            # Update activity
            connection.last_activity = time.time()
            
            # Handle message
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](connection_id, data)
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling message from {connection_id}: {e}")
            await self.send_message(connection_id, {
                "type": MessageType.ERROR.value,
                "error": str(e)
            })
    
    def register_handler(self, message_type: MessageType, handler: Callable):
        """Register custom message handler."""
        self.message_handlers[message_type] = handler
    
    def _register_default_handlers(self):
        """Register default message handlers."""
        
        async def handle_join_room(connection_id: str, data: Dict[str, Any]):
            room_id = data.get("room_id")
            room_name = data.get("room_name")
            if room_id:
                await self.join_room(connection_id, room_id, room_name)
        
        async def handle_leave_room(connection_id: str, data: Dict[str, Any]):
            room_id = data.get("room_id")
            if room_id:
                await self.leave_room(connection_id, room_id)
        
        async def handle_ping(connection_id: str, data: Dict[str, Any]):
            await self.send_message(connection_id, {
                "type": MessageType.PONG.value,
                "timestamp": time.time()
            })
        
        # Register handlers
        self.register_handler(MessageType.JOIN_ROOM, handle_join_room)
        self.register_handler(MessageType.LEAVE_ROOM, handle_leave_room)
        self.register_handler(MessageType.PING, handle_ping)
    
    async def _background_cleanup(self):
        """Background task for connection cleanup."""
        while self._running:
            try:
                current_time = time.time()
                idle_connections = []
                
                # Find idle connections (idle > 5 minutes)
                for connection_id, connection in self.connections.items():
                    if connection.idle_seconds > 300:  # 5 minutes
                        idle_connections.append(connection_id)
                
                # Disconnect idle connections
                for connection_id in idle_connections:
                    logger.info(f"Disconnecting idle connection: {connection_id}")
                    await self.disconnect(connection_id)
                
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in WebSocket cleanup: {e}")
                await asyncio.sleep(60)
    
    async def _background_stats(self):
        """Background task for statistics collection."""
        while self._running:
            try:
                current_time = time.time()
                
                # Calculate messages per second
                time_diff = current_time - self.last_stats_update
                if time_diff > 0:
                    self.stats.messages_per_second = len(self.message_times) / time_diff
                
                # Clear old message times
                self.message_times.clear()
                self.last_stats_update = current_time
                
                # Update active connections count
                self.stats.active_connections = len([
                    c for c in self.connections.values() if c.is_active
                ])
                
                await asyncio.sleep(10)  # Update every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in WebSocket stats: {e}")
                await asyncio.sleep(10)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get WebSocket statistics."""
        return {
            "connections": {
                "total": self.stats.total_connections,
                "active": self.stats.active_connections,
                "idle": len([c for c in self.connections.values() if c.idle_seconds > 60])
            },
            "rooms": {
                "total": self.stats.rooms_count,
                "active": len(self.rooms)
            },
            "messages": {
                "total": self.stats.total_messages,
                "per_second": self.stats.messages_per_second
            },
            "performance": {
                "average_response_time": self.stats.average_response_time,
                "error_count": self.stats.error_count
            }
        }


# Global WebSocket manager instance
advanced_websocket_manager = AdvancedWebSocketManager()
