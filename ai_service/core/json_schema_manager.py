"""
Enhanced JSON Schema Manager for Structured Output Generation.
Provides schema validation, template generation, and structured response formatting.
"""

import json
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from enum import Enum
import jsonschema
from jsonschema import validate, ValidationError, Draft7Validator

logger = logging.getLogger(__name__)


class SchemaType(Enum):
    """Types of JSON schemas."""
    OBJECT = "object"
    ARRAY = "array"
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    NULL = "null"


@dataclass
class SchemaValidationResult:
    """Result of schema validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    validated_data: Optional[Any] = None


class JSONSchemaManager:
    """Enhanced JSON schema management system."""
    
    def __init__(self):
        self.schemas: Dict[str, Dict[str, Any]] = {}
        self.templates: Dict[str, str] = {}
        self.validation_cache: Dict[str, SchemaValidationResult] = {}
        
        # Register common schemas
        self._register_common_schemas()
        
        logger.info("JSON Schema Manager initialized")
    
    def register_schema(self, name: str, schema: Dict[str, Any]) -> bool:
        """Register a JSON schema for validation.
        
        Args:
            name: Schema name
            schema: JSON schema definition
            
        Returns:
            True if registration successful
            
        Raises:
            ValueError: If schema is invalid
        """
        try:
            # Validate schema itself
            Draft7Validator.check_schema(schema)
            
            # Store schema
            self.schemas[name] = schema
            
            # Clear validation cache for this schema
            cache_keys_to_remove = [k for k in self.validation_cache.keys() if k.startswith(f"{name}:")]
            for key in cache_keys_to_remove:
                del self.validation_cache[key]
            
            logger.info(f"Schema '{name}' registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register schema '{name}': {e}")
            raise ValueError(f"Invalid schema: {e}")
    
    def validate_json(self, data: Any, schema_name: str, use_cache: bool = True) -> SchemaValidationResult:
        """Validate JSON data against registered schema.
        
        Args:
            data: Data to validate
            schema_name: Name of registered schema
            use_cache: Whether to use validation cache
            
        Returns:
            SchemaValidationResult with validation details
        """
        # Check cache first
        cache_key = f"{schema_name}:{hash(str(data))}"
        if use_cache and cache_key in self.validation_cache:
            return self.validation_cache[cache_key]
        
        # Check if schema exists
        if schema_name not in self.schemas:
            result = SchemaValidationResult(
                is_valid=False,
                errors=[f"Schema '{schema_name}' not found"]
            )
            return result
        
        schema = self.schemas[schema_name]
        
        try:
            # Validate data
            validate(instance=data, schema=schema)
            
            result = SchemaValidationResult(
                is_valid=True,
                validated_data=data
            )
            
            logger.debug(f"Validation successful for schema '{schema_name}'")
            
        except ValidationError as e:
            result = SchemaValidationResult(
                is_valid=False,
                errors=[e.message],
                validated_data=None
            )
            
            logger.warning(f"Validation failed for schema '{schema_name}': {e.message}")
        
        except Exception as e:
            result = SchemaValidationResult(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"]
            )
            
            logger.error(f"Unexpected validation error for schema '{schema_name}': {e}")
        
        # Cache result
        if use_cache:
            self.validation_cache[cache_key] = result
        
        return result
    
    def generate_template(self, schema_name: str) -> str:
        """Generate JSON template from schema.
        
        Args:
            schema_name: Name of registered schema
            
        Returns:
            JSON template string
        """
        if schema_name not in self.schemas:
            raise ValueError(f"Schema '{schema_name}' not found")
        
        schema = self.schemas[schema_name]
        template = self._schema_to_template(schema)
        
        # Store template for reuse
        self.templates[schema_name] = template
        
        return template
    
    def _schema_to_template(self, schema: Dict[str, Any], indent: int = 0) -> str:
        """Convert JSON schema to template string.
        
        Args:
            schema: JSON schema definition
            indent: Current indentation level
            
        Returns:
            Template string
        """
        schema_type = schema.get("type", "object")
        indent_str = "  " * indent
        
        if schema_type == "object":
            properties = schema.get("properties", {})
            required = schema.get("required", [])
            
            if not properties:
                return "{}"
            
            template_parts = []
            for prop, prop_schema in properties.items():
                is_required = prop in required
                prop_template = self._property_to_template(prop, prop_schema, indent + 1)
                
                if is_required:
                    template_parts.append(prop_template)
                else:
                    template_parts.append(f"// Optional: {prop_template}")
            
            inner_content = ",\n".join(template_parts)
            return f"{{\n{inner_content}\n{indent_str}}}"
        
        elif schema_type == "array":
            items_schema = schema.get("items", {})
            item_template = self._schema_to_template(items_schema, indent + 1)
            return f"[\n{indent_str}  {item_template}\n{indent_str}]"
        
        elif schema_type == "string":
            example = schema.get("example", "string_value")
            return f'"{example}"'
        
        elif schema_type == "number":
            example = schema.get("example", 0)
            return str(example)
        
        elif schema_type == "boolean":
            example = schema.get("example", True)
            return str(example).lower()
        
        elif schema_type == "null":
            return "null"
        
        else:
            return '""'  # Default fallback
    
    def _property_to_template(self, prop_name: str, prop_schema: Dict[str, Any], indent: int) -> str:
        """Convert property schema to template string."""
        indent_str = "  " * indent
        prop_template = self._schema_to_template(prop_schema, indent)
        
        # Add description as comment if available
        description = prop_schema.get("description", "")
        comment = f" // {description}" if description else ""
        
        return f'{indent_str}"{prop_name}": {prop_template}{comment}'
    
    def get_schema_info(self, schema_name: str) -> Dict[str, Any]:
        """Get information about a registered schema.
        
        Args:
            schema_name: Name of schema
            
        Returns:
            Schema information dictionary
        """
        if schema_name not in self.schemas:
            raise ValueError(f"Schema '{schema_name}' not found")
        
        schema = self.schemas[schema_name]
        
        return {
            "name": schema_name,
            "type": schema.get("type", "unknown"),
            "title": schema.get("title", ""),
            "description": schema.get("description", ""),
            "properties": list(schema.get("properties", {}).keys()),
            "required": schema.get("required", []),
            "has_template": schema_name in self.templates
        }
    
    def list_schemas(self) -> List[Dict[str, Any]]:
        """List all registered schemas.
        
        Returns:
            List of schema information dictionaries
        """
        return [self.get_schema_info(name) for name in self.schemas.keys()]
    
    def _register_common_schemas(self):
        """Register commonly used schemas."""
        
        # Article summary schema
        article_summary_schema = {
            "type": "object",
            "title": "Article Summary",
            "description": "Schema for article summarization",
            "properties": {
                "title": {
                    "type": "string",
                    "description": "Article title",
                    "example": "Breaking News: AI Advances"
                },
                "summary": {
                    "type": "string",
                    "description": "Brief summary of the article",
                    "example": "Recent developments in AI technology..."
                },
                "key_points": {
                    "type": "array",
                    "description": "Main points from the article",
                    "items": {
                        "type": "string"
                    },
                    "example": ["Point 1", "Point 2", "Point 3"]
                },
                "sentiment": {
                    "type": "string",
                    "enum": ["positive", "negative", "neutral"],
                    "description": "Overall sentiment of the article"
                },
                "word_count": {
                    "type": "number",
                    "description": "Approximate word count",
                    "example": 500
                }
            },
            "required": ["title", "summary", "key_points", "sentiment"]
        }
        
        # Product review schema
        product_review_schema = {
            "type": "object",
            "title": "Product Review",
            "description": "Schema for product review analysis",
            "properties": {
                "product_name": {
                    "type": "string",
                    "description": "Name of the product"
                },
                "rating": {
                    "type": "number",
                    "minimum": 1,
                    "maximum": 5,
                    "description": "Rating out of 5"
                },
                "pros": {
                    "type": "array",
                    "description": "Positive aspects",
                    "items": {"type": "string"}
                },
                "cons": {
                    "type": "array",
                    "description": "Negative aspects",
                    "items": {"type": "string"}
                },
                "recommendation": {
                    "type": "string",
                    "enum": ["highly_recommended", "recommended", "neutral", "not_recommended"],
                    "description": "Overall recommendation"
                },
                "review_summary": {
                    "type": "string",
                    "description": "Summary of the review"
                }
            },
            "required": ["product_name", "rating", "recommendation", "review_summary"]
        }
        
        # Data analysis schema
        data_analysis_schema = {
            "type": "object",
            "title": "Data Analysis",
            "description": "Schema for data analysis results",
            "properties": {
                "dataset_name": {
                    "type": "string",
                    "description": "Name of the analyzed dataset"
                },
                "analysis_type": {
                    "type": "string",
                    "enum": ["descriptive", "diagnostic", "predictive", "prescriptive"],
                    "description": "Type of analysis performed"
                },
                "findings": {
                    "type": "object",
                    "properties": {
                        "key_insights": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Main insights discovered"
                        },
                        "statistics": {
                            "type": "object",
                            "description": "Statistical measures",
                            "additionalProperties": {"type": "number"}
                        }
                    },
                    "required": ["key_insights"]
                },
                "recommendations": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Actionable recommendations"
                }
            },
            "required": ["dataset_name", "analysis_type", "findings"]
        }
        
        # Register schemas
        self.register_schema("article_summary", article_summary_schema)
        self.register_schema("product_review", product_review_schema)
        self.register_schema("data_analysis", data_analysis_schema)
        
        logger.info("Common schemas registered successfully")
    
    def clear_cache(self):
        """Clear validation cache."""
        self.validation_cache.clear()
        logger.info("Validation cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get validation cache statistics."""
        return {
            "cache_size": len(self.validation_cache),
            "schemas_count": len(self.schemas),
            "templates_count": len(self.templates)
        }


# Global schema manager instance
json_schema_manager = JSONSchemaManager()
