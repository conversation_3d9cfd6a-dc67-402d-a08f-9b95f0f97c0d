"""
Enhanced Streaming Manager for Production Stability.
Handles async generator cleanup, connection monitoring, and resource management.
"""

import asyncio
import logging
import time
import weakref
from typing import Dict, Set, Optional, AsyncGenerator, Any, List
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class StreamStatus(Enum):
    """Stream status enumeration."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ERROR = "error"
    CLEANUP = "cleanup"


@dataclass
class StreamMetrics:
    """Stream performance metrics."""
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    chunks_sent: int = 0
    bytes_sent: int = 0
    errors_count: int = 0
    last_activity: float = field(default_factory=time.time)
    
    @property
    def duration(self) -> float:
        """Get stream duration."""
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def throughput(self) -> float:
        """Get bytes per second throughput."""
        duration = self.duration
        return self.bytes_sent / duration if duration > 0 else 0


class StreamSession:
    """Enhanced stream session with comprehensive monitoring."""
    
    def __init__(self, stream_id: str, generator: AsyncGenerator, metadata: Optional[Dict] = None):
        self.stream_id = stream_id
        self.generator = generator
        self.metadata = metadata or {}
        self.status = StreamStatus.INITIALIZING
        self.metrics = StreamMetrics()
        self.cleanup_callbacks: List[callable] = []
        self._lock = asyncio.Lock()
        self._is_cleaned = False
        
        logger.info(f"Stream session created: {stream_id}")
    
    async def update_activity(self, chunk_size: int = 0):
        """Update stream activity metrics."""
        async with self._lock:
            self.metrics.last_activity = time.time()
            self.metrics.chunks_sent += 1
            self.metrics.bytes_sent += chunk_size
    
    async def mark_error(self, error: Exception):
        """Mark stream as having an error."""
        async with self._lock:
            self.metrics.errors_count += 1
            self.status = StreamStatus.ERROR
            logger.error(f"Stream {self.stream_id} error: {error}")
    
    async def cleanup(self):
        """Clean up stream resources."""
        if self._is_cleaned:
            return
        
        async with self._lock:
            if self._is_cleaned:
                return
            
            self._is_cleaned = True
            self.status = StreamStatus.CLEANUP
            self.metrics.end_time = time.time()
            
            # Close generator
            if self.generator:
                try:
                    await self.generator.aclose()
                    logger.debug(f"Generator closed for stream {self.stream_id}")
                except Exception as e:
                    logger.error(f"Error closing generator for stream {self.stream_id}: {e}")
            
            # Execute cleanup callbacks
            for callback in self.cleanup_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback()
                    else:
                        callback()
                except Exception as e:
                    logger.error(f"Error in cleanup callback for stream {self.stream_id}: {e}")
            
            logger.info(f"Stream {self.stream_id} cleaned up. Duration: {self.metrics.duration:.2f}s, "
                       f"Chunks: {self.metrics.chunks_sent}, Bytes: {self.metrics.bytes_sent}")
    
    def add_cleanup_callback(self, callback: callable):
        """Add cleanup callback."""
        self.cleanup_callbacks.append(callback)


class EnhancedStreamManager:
    """Enhanced stream manager with production-grade features."""
    
    def __init__(self, max_concurrent_streams: int = 100, cleanup_interval: int = 300):
        self.active_streams: Dict[str, StreamSession] = {}
        self.max_concurrent_streams = max_concurrent_streams
        self.cleanup_interval = cleanup_interval
        self._cleanup_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        self._lock = asyncio.Lock()
        
        # Start background cleanup task
        self._start_cleanup_task()
        
        logger.info(f"Enhanced stream manager initialized. Max concurrent: {max_concurrent_streams}")
    
    def _start_cleanup_task(self):
        """Start background cleanup task."""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._background_cleanup())
    
    async def _background_cleanup(self):
        """Background task to clean up stale streams."""
        while not self._shutdown_event.is_set():
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_stale_streams()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background cleanup: {e}")
    
    async def _cleanup_stale_streams(self):
        """Clean up stale or inactive streams."""
        current_time = time.time()
        stale_threshold = 3600  # 1 hour
        
        stale_streams = []
        
        async with self._lock:
            for stream_id, session in self.active_streams.items():
                if (current_time - session.metrics.last_activity) > stale_threshold:
                    stale_streams.append(stream_id)
        
        for stream_id in stale_streams:
            logger.warning(f"Cleaning up stale stream: {stream_id}")
            await self._cleanup_stream(stream_id)
    
    @asynccontextmanager
    async def managed_stream(
        self, 
        stream_id: str, 
        generator: AsyncGenerator,
        metadata: Optional[Dict] = None
    ):
        """Context manager for safe stream handling."""
        # Check concurrent stream limit
        if len(self.active_streams) >= self.max_concurrent_streams:
            raise RuntimeError(f"Maximum concurrent streams ({self.max_concurrent_streams}) exceeded")
        
        session = StreamSession(stream_id, generator, metadata)
        
        async with self._lock:
            self.active_streams[stream_id] = session
        
        session.status = StreamStatus.ACTIVE
        
        try:
            yield session
            session.status = StreamStatus.COMPLETED
        except asyncio.CancelledError:
            logger.info(f"Stream {stream_id} cancelled by client")
            session.status = StreamStatus.CANCELLED
            raise
        except Exception as e:
            logger.error(f"Stream {stream_id} error: {e}")
            await session.mark_error(e)
            raise
        finally:
            await self._cleanup_stream(stream_id)
    
    async def _cleanup_stream(self, stream_id: str):
        """Clean up a specific stream."""
        session = None
        
        async with self._lock:
            session = self.active_streams.pop(stream_id, None)
        
        if session:
            await session.cleanup()
    
    async def get_stream_metrics(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific stream."""
        async with self._lock:
            session = self.active_streams.get(stream_id)
            if session:
                return {
                    "stream_id": stream_id,
                    "status": session.status.value,
                    "duration": session.metrics.duration,
                    "chunks_sent": session.metrics.chunks_sent,
                    "bytes_sent": session.metrics.bytes_sent,
                    "errors_count": session.metrics.errors_count,
                    "throughput": session.metrics.throughput,
                    "metadata": session.metadata
                }
        return None
    
    async def get_all_streams_summary(self) -> Dict[str, Any]:
        """Get summary of all active streams."""
        async with self._lock:
            total_streams = len(self.active_streams)
            total_bytes = sum(s.metrics.bytes_sent for s in self.active_streams.values())
            total_chunks = sum(s.metrics.chunks_sent for s in self.active_streams.values())
            
            status_counts = {}
            for session in self.active_streams.values():
                status = session.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return {
                "total_active_streams": total_streams,
                "max_concurrent_streams": self.max_concurrent_streams,
                "total_bytes_sent": total_bytes,
                "total_chunks_sent": total_chunks,
                "status_breakdown": status_counts,
                "streams": [
                    {
                        "stream_id": sid,
                        "status": session.status.value,
                        "duration": session.metrics.duration,
                        "chunks": session.metrics.chunks_sent
                    }
                    for sid, session in self.active_streams.items()
                ]
            }
    
    async def force_cleanup_stream(self, stream_id: str) -> bool:
        """Force cleanup of a specific stream."""
        try:
            await self._cleanup_stream(stream_id)
            return True
        except Exception as e:
            logger.error(f"Error force cleaning stream {stream_id}: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown the stream manager and clean up all resources."""
        logger.info("Shutting down enhanced stream manager...")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Cancel cleanup task
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Clean up all active streams
        stream_ids = list(self.active_streams.keys())
        cleanup_tasks = [self._cleanup_stream(sid) for sid in stream_ids]
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        logger.info(f"Enhanced stream manager shutdown complete. Cleaned up {len(stream_ids)} streams.")


# Global instance
enhanced_stream_manager = EnhancedStreamManager()


# Utility functions for integration
async def create_managed_stream(stream_id: str, generator: AsyncGenerator, metadata: Optional[Dict] = None):
    """Create a managed stream with the global manager."""
    return enhanced_stream_manager.managed_stream(stream_id, generator, metadata)


async def get_stream_status(stream_id: str) -> Optional[Dict]:
    """Get status of a specific stream."""
    return await enhanced_stream_manager.get_stream_metrics(stream_id)


async def get_streams_summary() -> Dict:
    """Get summary of all streams."""
    return await enhanced_stream_manager.get_all_streams_summary()


# Cleanup on module shutdown
import atexit

def _cleanup_on_exit():
    """Cleanup function for module exit."""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            loop.create_task(enhanced_stream_manager.shutdown())
    except Exception:
        pass

atexit.register(_cleanup_on_exit)
