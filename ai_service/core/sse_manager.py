"""
Server-Sent Events (SSE) manager for real-time web client communication.
"""
import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, AsyncIterator
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import weakref

from fastapi import Request
from fastapi.responses import StreamingResponse
from ai_service.core.streaming_manager import streaming_manager, StreamType, StreamChunk
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class SSEEventType(Enum):
    """SSE event types."""
    MESSAGE = "message"
    STREAM_START = "stream_start"
    STREAM_CHUNK = "stream_chunk"
    STREAM_END = "stream_end"
    STREAM_ERROR = "stream_error"
    HEARTBEAT = "heartbeat"
    STATUS = "status"
    ERROR = "error"


@dataclass
class SSEEvent:
    """Server-Sent Event structure."""
    event_type: SSEEventType
    data: Any
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    retry: Optional[int] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    def to_sse_format(self) -> str:
        """Convert to SSE format."""
        lines = []
        
        # Event type
        lines.append(f"event: {self.event_type.value}")
        
        # Event ID
        lines.append(f"id: {self.event_id}")
        
        # Data (can be multi-line)
        data_str = json.dumps({
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "event_id": self.event_id
        }, default=str)
        
        for line in data_str.split('\n'):
            lines.append(f"data: {line}")
        
        # Retry interval
        if self.retry:
            lines.append(f"retry: {self.retry}")
        
        # End with double newline
        lines.append("")
        lines.append("")
        
        return "\n".join(lines)


@dataclass
class SSEClient:
    """SSE client information."""
    id: str
    request: Request
    connected_at: datetime = field(default_factory=datetime.utcnow)
    last_event: datetime = field(default_factory=datetime.utcnow)
    events_sent: int = 0
    bytes_sent: int = 0
    subscriptions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    active: bool = True


class SSEManager:
    """Server-Sent Events manager for web clients."""
    
    def __init__(self):
        # Client management
        self.clients: Dict[str, SSEClient] = {}
        self.client_queues: Dict[str, asyncio.Queue] = {}
        
        # Subscription management
        self.subscriptions: Dict[str, List[str]] = {}  # topic -> client_ids
        
        # Background tasks
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.total_connections = 0
        self.total_events_sent = 0
        
        # Start background tasks
        self._start_background_tasks()
        
        logger.info("SSE manager initialized")
    
    def _start_background_tasks(self):
        """Start background tasks."""
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def create_sse_stream(
        self,
        request: Request,
        client_id: Optional[str] = None,
        topics: Optional[List[str]] = None
    ) -> StreamingResponse:
        """Create SSE stream for client."""
        # Generate client ID if not provided
        if not client_id:
            client_id = str(uuid.uuid4())
        
        # Create client
        client = SSEClient(
            id=client_id,
            request=request,
            subscriptions=topics or []
        )
        
        # Create event queue
        event_queue = asyncio.Queue(maxsize=1000)
        
        # Store client
        self.clients[client_id] = client
        self.client_queues[client_id] = event_queue
        
        # Subscribe to topics
        for topic in client.subscriptions:
            await self.subscribe_client(client_id, topic)
        
        # Update statistics
        self.total_connections += 1
        
        # Send initial connection event
        await self.send_event(client_id, SSEEvent(
            event_type=SSEEventType.MESSAGE,
            data={
                "type": "connection",
                "client_id": client_id,
                "status": "connected",
                "subscriptions": client.subscriptions
            }
        ))
        
        logger.info(f"SSE client {client_id} connected")
        
        # Record metrics
        metrics_collector.record_counter("sse_connections_total", 1.0)
        metrics_collector.record_gauge("sse_active_connections", len(self.clients))
        
        # Create streaming response
        return StreamingResponse(
            self._event_stream_generator(client_id),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
    
    async def _event_stream_generator(self, client_id: str) -> AsyncIterator[str]:
        """Generate SSE event stream for client."""
        try:
            event_queue = self.client_queues[client_id]
            client = self.clients[client_id]
            
            while client.active and client_id in self.clients:
                try:
                    # Get event from queue with timeout
                    event = await asyncio.wait_for(event_queue.get(), timeout=30.0)
                    
                    # Convert to SSE format
                    sse_data = event.to_sse_format()
                    
                    # Update client statistics
                    client.events_sent += 1
                    client.bytes_sent += len(sse_data.encode('utf-8'))
                    client.last_event = datetime.utcnow()
                    
                    # Update global statistics
                    self.total_events_sent += 1
                    
                    yield sse_data
                    
                except asyncio.TimeoutError:
                    # Send heartbeat on timeout
                    heartbeat_event = SSEEvent(
                        event_type=SSEEventType.HEARTBEAT,
                        data={"timestamp": datetime.utcnow().isoformat()}
                    )
                    
                    sse_data = heartbeat_event.to_sse_format()
                    client.last_event = datetime.utcnow()
                    
                    yield sse_data
                    
                except Exception as e:
                    logger.error(f"Error in SSE stream for client {client_id}: {e}")
                    break
            
        except Exception as e:
            logger.error(f"SSE stream generator error for client {client_id}: {e}")
        finally:
            # Cleanup client
            await self.disconnect_client(client_id)
    
    async def send_event(self, client_id: str, event: SSEEvent) -> bool:
        """Send event to specific client."""
        if client_id not in self.clients or client_id not in self.client_queues:
            return False
        
        try:
            event_queue = self.client_queues[client_id]
            
            # Add event to queue (non-blocking)
            try:
                event_queue.put_nowait(event)
                return True
            except asyncio.QueueFull:
                logger.warning(f"Event queue full for client {client_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending event to client {client_id}: {e}")
            return False
    
    async def broadcast_event(self, event: SSEEvent, topic: Optional[str] = None) -> int:
        """Broadcast event to all clients or topic subscribers."""
        sent_count = 0
        
        if topic:
            # Send to topic subscribers
            client_ids = self.subscriptions.get(topic, [])
        else:
            # Send to all clients
            client_ids = list(self.clients.keys())
        
        for client_id in client_ids:
            if await self.send_event(client_id, event):
                sent_count += 1
        
        return sent_count
    
    async def subscribe_client(self, client_id: str, topic: str) -> bool:
        """Subscribe client to topic."""
        if client_id not in self.clients:
            return False
        
        # Add to subscriptions
        if topic not in self.subscriptions:
            self.subscriptions[topic] = []
        
        if client_id not in self.subscriptions[topic]:
            self.subscriptions[topic].append(client_id)
        
        # Update client subscriptions
        client = self.clients[client_id]
        if topic not in client.subscriptions:
            client.subscriptions.append(topic)
        
        logger.info(f"SSE client {client_id} subscribed to topic {topic}")
        
        # Send confirmation event
        await self.send_event(client_id, SSEEvent(
            event_type=SSEEventType.MESSAGE,
            data={
                "type": "subscription",
                "topic": topic,
                "status": "subscribed"
            }
        ))
        
        return True
    
    async def unsubscribe_client(self, client_id: str, topic: str) -> bool:
        """Unsubscribe client from topic."""
        if client_id not in self.clients:
            return False
        
        # Remove from subscriptions
        if topic in self.subscriptions:
            if client_id in self.subscriptions[topic]:
                self.subscriptions[topic].remove(client_id)
            
            # Remove empty topic
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
        
        # Update client subscriptions
        client = self.clients[client_id]
        if topic in client.subscriptions:
            client.subscriptions.remove(topic)
        
        logger.info(f"SSE client {client_id} unsubscribed from topic {topic}")
        
        # Send confirmation event
        await self.send_event(client_id, SSEEvent(
            event_type=SSEEventType.MESSAGE,
            data={
                "type": "unsubscription",
                "topic": topic,
                "status": "unsubscribed"
            }
        ))
        
        return True
    
    async def disconnect_client(self, client_id: str):
        """Disconnect SSE client."""
        if client_id not in self.clients:
            return
        
        client = self.clients[client_id]
        client.active = False
        
        # Remove from all subscriptions
        for topic in list(client.subscriptions):
            await self.unsubscribe_client(client_id, topic)
        
        # Remove client
        del self.clients[client_id]
        if client_id in self.client_queues:
            del self.client_queues[client_id]
        
        logger.info(f"SSE client {client_id} disconnected")
        
        # Record metrics
        metrics_collector.record_counter("sse_disconnections_total", 1.0)
        metrics_collector.record_gauge("sse_active_connections", len(self.clients))
    
    async def start_stream_for_client(
        self,
        client_id: str,
        stream_type: StreamType,
        stream_config: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Start streaming for SSE client."""
        if client_id not in self.clients:
            return None
        
        # Create stream
        stream_id = await streaming_manager.create_stream(
            stream_type=stream_type,
            client_id=client_id,
            metadata=stream_config or {}
        )
        
        # Start stream
        await streaming_manager.start_stream(stream_id)
        
        # Send stream start event
        await self.send_event(client_id, SSEEvent(
            event_type=SSEEventType.STREAM_START,
            data={
                "stream_id": stream_id,
                "stream_type": stream_type.value,
                "status": "started"
            }
        ))
        
        # Start streaming task
        asyncio.create_task(self._stream_to_client(client_id, stream_id))
        
        return stream_id
    
    async def _stream_to_client(self, client_id: str, stream_id: str):
        """Stream chunks to SSE client."""
        try:
            # Get stream iterator
            iterator = await streaming_manager.get_stream_iterator(stream_id)
            if not iterator:
                return
            
            # Stream chunks
            async for chunk in iterator:
                if client_id not in self.clients:
                    break
                
                # Send chunk event
                await self.send_event(client_id, SSEEvent(
                    event_type=SSEEventType.STREAM_CHUNK,
                    data={
                        "stream_id": stream_id,
                        "chunk_id": chunk.id,
                        "chunk_type": chunk.chunk_type,
                        "data": chunk.data,
                        "sequence": chunk.sequence,
                        "metadata": chunk.metadata,
                        "is_final": chunk.is_final
                    }
                ))
                
                # Break if final chunk
                if chunk.is_final:
                    break
            
            # Send stream end event
            await self.send_event(client_id, SSEEvent(
                event_type=SSEEventType.STREAM_END,
                data={
                    "stream_id": stream_id,
                    "status": "completed"
                }
            ))
            
        except Exception as e:
            logger.error(f"Error streaming to SSE client {client_id}: {e}")
            
            # Send error event
            await self.send_event(client_id, SSEEvent(
                event_type=SSEEventType.STREAM_ERROR,
                data={
                    "stream_id": stream_id,
                    "error": str(e)
                }
            ))
    
    def get_client_info(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Get SSE client information."""
        if client_id not in self.clients:
            return None
        
        client = self.clients[client_id]
        
        return {
            "id": client.id,
            "connected_at": client.connected_at.isoformat(),
            "last_event": client.last_event.isoformat(),
            "events_sent": client.events_sent,
            "bytes_sent": client.bytes_sent,
            "subscriptions": client.subscriptions,
            "metadata": client.metadata,
            "active": client.active
        }
    
    def get_sse_stats(self) -> Dict[str, Any]:
        """Get SSE statistics."""
        return {
            "total_connections": self.total_connections,
            "active_connections": len(self.clients),
            "total_events_sent": self.total_events_sent,
            "active_subscriptions": len(self.subscriptions),
            "clients_by_topic": {
                topic: len(clients) for topic, clients in self.subscriptions.items()
            }
        }
    
    async def _heartbeat_loop(self):
        """Background heartbeat loop."""
        while True:
            try:
                await asyncio.sleep(60)  # Heartbeat every minute
                
                # Send heartbeat to all active clients
                heartbeat_event = SSEEvent(
                    event_type=SSEEventType.HEARTBEAT,
                    data={"timestamp": datetime.utcnow().isoformat()}
                )
                
                await self.broadcast_event(heartbeat_event)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"SSE heartbeat loop error: {e}")
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
                current_time = datetime.utcnow()
                
                # Find inactive clients (no events sent in last 10 minutes)
                inactive_clients = []
                for client_id, client in self.clients.items():
                    time_since_event = (current_time - client.last_event).total_seconds()
                    
                    if time_since_event > 600:  # 10 minutes
                        inactive_clients.append(client_id)
                
                # Disconnect inactive clients
                for client_id in inactive_clients:
                    logger.warning(f"Disconnecting inactive SSE client: {client_id}")
                    await self.disconnect_client(client_id)
                
                # Clean up empty subscriptions
                empty_topics = [
                    topic for topic, clients in self.subscriptions.items()
                    if not clients
                ]
                
                for topic in empty_topics:
                    del self.subscriptions[topic]
                    logger.debug(f"Cleaned up empty SSE topic: {topic}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"SSE cleanup loop error: {e}")
    
    async def shutdown(self):
        """Shutdown SSE manager."""
        # Cancel background tasks
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Disconnect all clients
        for client_id in list(self.clients.keys()):
            await self.disconnect_client(client_id)
        
        logger.info("SSE manager shutdown completed")


# Global SSE manager instance
sse_manager = SSEManager()
