"""
Comprehensive logging configuration for the AI service.
"""
import logging
import logging.handlers
import json
import time
import os
import uuid
from typing import Dict, Any, Optional
from datetime import datetime
from contextvars import ContextVar
from ai_service.config.settings import settings

# Context variables for request tracking
request_id_var: ContextVar[str] = ContextVar('request_id', default='')
correlation_id_var: ContextVar[str] = ContextVar('correlation_id', default='')


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        
        if hasattr(record, 'model'):
            log_entry["model"] = record.model
        
        if hasattr(record, 'duration'):
            log_entry["duration_ms"] = record.duration
        
        if hasattr(record, 'tokens'):
            log_entry["tokens"] = record.tokens
        
        return json.dumps(log_entry)


class PerformanceLogger:
    """Logger for tracking performance metrics."""
    
    def __init__(self):
        self.logger = logging.getLogger("ai_service.performance")
    
    def log_request_start(self, request_id: str, endpoint: str, model: str = None, **kwargs):
        """Log the start of a request."""
        extra = {
            "request_id": request_id,
            "endpoint": endpoint,
            "event": "request_start",
            "timestamp": time.time()
        }
        
        if model:
            extra["model"] = model
        
        extra.update(kwargs)
        
        self.logger.info(f"Request started: {endpoint}", extra=extra)
    
    def log_request_end(self, request_id: str, endpoint: str, duration_ms: float, 
                       status_code: int = 200, tokens: Dict[str, int] = None, **kwargs):
        """Log the end of a request."""
        extra = {
            "request_id": request_id,
            "endpoint": endpoint,
            "event": "request_end",
            "duration": duration_ms,
            "status_code": status_code,
            "timestamp": time.time()
        }
        
        if tokens:
            extra["tokens"] = tokens
        
        extra.update(kwargs)
        
        level = logging.INFO if status_code < 400 else logging.ERROR
        self.logger.log(level, f"Request completed: {endpoint} ({duration_ms:.2f}ms)", extra=extra)
    
    def log_model_call(self, request_id: str, model: str, operation: str, 
                      duration_ms: float, tokens: Dict[str, int] = None, **kwargs):
        """Log a model API call."""
        extra = {
            "request_id": request_id,
            "model": model,
            "operation": operation,
            "event": "model_call",
            "duration": duration_ms,
            "timestamp": time.time()
        }
        
        if tokens:
            extra["tokens"] = tokens
        
        extra.update(kwargs)
        
        self.logger.info(f"Model call: {model} - {operation} ({duration_ms:.2f}ms)", extra=extra)


class SecurityLogger:
    """Logger for security-related events."""
    
    def __init__(self):
        self.logger = logging.getLogger("ai_service.security")
    
    def log_validation_error(self, request_id: str, field: str, error: str, value: str = None):
        """Log validation errors."""
        extra = {
            "request_id": request_id,
            "event": "validation_error",
            "field": field,
            "error": error,
            "timestamp": time.time()
        }
        
        if value and len(str(value)) < 100:  # Don't log long values
            extra["value"] = str(value)
        
        self.logger.warning(f"Validation error in {field}: {error}", extra=extra)
    
    def log_suspicious_activity(self, request_id: str, activity: str, details: Dict[str, Any] = None):
        """Log suspicious activities."""
        extra = {
            "request_id": request_id,
            "event": "suspicious_activity",
            "activity": activity,
            "timestamp": time.time()
        }
        
        if details:
            extra["details"] = details
        
        self.logger.warning(f"Suspicious activity: {activity}", extra=extra)
    
    def log_rate_limit(self, request_id: str, client_ip: str, endpoint: str):
        """Log rate limiting events."""
        extra = {
            "request_id": request_id,
            "event": "rate_limit",
            "client_ip": client_ip,
            "endpoint": endpoint,
            "timestamp": time.time()
        }
        
        self.logger.warning(f"Rate limit exceeded for {client_ip} on {endpoint}", extra=extra)


def setup_logging():
    """Setup comprehensive logging configuration."""
    
    # Create logs directory if it doesn't exist
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler with simple format for development
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.INFO)
    
    # File handler with JSON format for production
    file_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "ai_service.log"),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(JSONFormatter())
    file_handler.setLevel(logging.DEBUG)
    
    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "ai_service_errors.log"),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    error_handler.setFormatter(JSONFormatter())
    error_handler.setLevel(logging.ERROR)
    
    # Performance log handler
    performance_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "ai_service_performance.log"),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    performance_handler.setFormatter(JSONFormatter())
    
    # Security log handler
    security_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join(log_dir, "ai_service_security.log"),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    security_handler.setFormatter(JSONFormatter())
    
    # Add handlers to root logger
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    
    # Setup specific loggers
    performance_logger = logging.getLogger("ai_service.performance")
    performance_logger.addHandler(performance_handler)
    performance_logger.setLevel(logging.INFO)
    performance_logger.propagate = False
    
    security_logger = logging.getLogger("ai_service.security")
    security_logger.addHandler(security_handler)
    security_logger.setLevel(logging.WARNING)
    security_logger.propagate = False
    
    # Suppress noisy third-party loggers
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    logging.info("Logging configuration completed")


class RequestTracker:
    """Context manager for tracking request lifecycle."""
    
    def __init__(self, request_id: str, endpoint: str, model: str = None):
        self.request_id = request_id
        self.endpoint = endpoint
        self.model = model
        self.start_time = None
        self.performance_logger = PerformanceLogger()
    
    def __enter__(self):
        self.start_time = time.time()
        self.performance_logger.log_request_start(
            self.request_id, 
            self.endpoint, 
            self.model
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000
        status_code = 500 if exc_type else 200
        
        self.performance_logger.log_request_end(
            self.request_id,
            self.endpoint,
            duration_ms,
            status_code
        )
        
        if exc_type:
            logger = logging.getLogger(__name__)
            logger.error(
                f"Request {self.request_id} failed with {exc_type.__name__}: {exc_val}",
                extra={"request_id": self.request_id},
                exc_info=True
            )


# Context management functions
def get_request_id() -> str:
    """Get current request ID."""
    return request_id_var.get('')


def set_request_context(request_id: str = None, correlation_id: str = None):
    """Set request context for logging."""
    if request_id:
        request_id_var.set(request_id)
    if correlation_id:
        correlation_id_var.set(correlation_id)


def generate_request_id() -> str:
    """Generate a new request ID."""
    return str(uuid.uuid4())


def clear_request_context():
    """Clear request context."""
    request_id_var.set('')
    correlation_id_var.set('')


# Global instances
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()
