"""
Comprehensive metrics collection system for production monitoring.
"""
import time
import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
from contextlib import asynccontextmanager
import threading
import psutil

logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """Individual metric data point."""
    name: str
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    metric_type: str = "gauge"  # gauge, counter, histogram


@dataclass
class RequestMetrics:
    """Request-specific metrics."""
    endpoint: str
    method: str
    status_code: int
    duration_ms: float
    timestamp: datetime
    user_agent: Optional[str] = None
    client_ip: Optional[str] = None


class MetricsCollector:
    """Comprehensive metrics collection system."""
    
    def __init__(self, max_history_size: int = 10000):
        self.max_history_size = max_history_size
        self.metrics_history: deque = deque(maxlen=max_history_size)
        self.request_history: deque = deque(maxlen=max_history_size)
        
        # Counters
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = defaultdict(float)
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        
        # Request tracking
        self.active_requests: Dict[str, datetime] = {}
        self.request_counts: Dict[str, int] = defaultdict(int)
        self.error_counts: Dict[str, int] = defaultdict(int)
        
        # System metrics
        self.system_metrics: Dict[str, float] = {}
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Start background metrics collection
        self._start_background_collection()
        
        logger.info("Metrics collector initialized")
    
    def _start_background_collection(self):
        """Start background system metrics collection."""
        def collect_system_metrics():
            while True:
                try:
                    self._collect_system_metrics()
                    time.sleep(30)  # Collect every 30 seconds
                except Exception as e:
                    logger.error(f"Error collecting system metrics: {e}")
                    time.sleep(60)  # Wait longer on error
        
        thread = threading.Thread(target=collect_system_metrics, daemon=True)
        thread.start()
    
    def _collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_gauge("system_cpu_percent", cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.record_gauge("system_memory_percent", memory.percent)
            self.record_gauge("system_memory_available_gb", memory.available / (1024**3))
            self.record_gauge("system_memory_used_gb", memory.used / (1024**3))
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            self.record_gauge("system_disk_percent", (disk.used / disk.total) * 100)
            self.record_gauge("system_disk_free_gb", disk.free / (1024**3))
            
            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            self.record_gauge("process_memory_rss_mb", process_memory.rss / (1024**2))
            self.record_gauge("process_memory_vms_mb", process_memory.vms / (1024**2))
            self.record_gauge("process_cpu_percent", process.cpu_percent())
            
            # Network metrics (if available)
            try:
                net_io = psutil.net_io_counters()
                self.record_counter("network_bytes_sent", net_io.bytes_sent)
                self.record_counter("network_bytes_recv", net_io.bytes_recv)
            except:
                pass  # Network metrics might not be available
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def record_counter(self, name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """Record a counter metric."""
        with self._lock:
            key = self._make_key(name, labels)
            self.counters[key] += value
            
            metric = MetricPoint(
                name=name,
                value=self.counters[key],
                timestamp=datetime.utcnow(),
                labels=labels or {},
                metric_type="counter"
            )
            self.metrics_history.append(metric)
    
    def record_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record a gauge metric."""
        with self._lock:
            key = self._make_key(name, labels)
            self.gauges[key] = value
            
            metric = MetricPoint(
                name=name,
                value=value,
                timestamp=datetime.utcnow(),
                labels=labels or {},
                metric_type="gauge"
            )
            self.metrics_history.append(metric)
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record a histogram metric."""
        with self._lock:
            key = self._make_key(name, labels)
            self.histograms[key].append(value)
            
            # Keep only recent values (last 1000)
            if len(self.histograms[key]) > 1000:
                self.histograms[key] = self.histograms[key][-1000:]
            
            metric = MetricPoint(
                name=name,
                value=value,
                timestamp=datetime.utcnow(),
                labels=labels or {},
                metric_type="histogram"
            )
            self.metrics_history.append(metric)
    
    def record_request(self, metrics: RequestMetrics):
        """Record request metrics."""
        with self._lock:
            self.request_history.append(metrics)
            
            # Update counters
            endpoint_key = f"requests_total_{metrics.endpoint}_{metrics.method}"
            self.record_counter(endpoint_key)
            
            # Record response time
            self.record_histogram(
                "request_duration_ms",
                metrics.duration_ms,
                {
                    "endpoint": metrics.endpoint,
                    "method": metrics.method,
                    "status_code": str(metrics.status_code)
                }
            )
            
            # Track errors
            if metrics.status_code >= 400:
                error_key = f"errors_total_{metrics.endpoint}_{metrics.method}"
                self.record_counter(error_key)
    
    @asynccontextmanager
    async def track_request(self, endpoint: str, method: str = "GET"):
        """Context manager to track request duration."""
        request_id = f"{endpoint}_{method}_{time.time()}"
        start_time = time.time()
        
        with self._lock:
            self.active_requests[request_id] = datetime.utcnow()
        
        try:
            yield
            status_code = 200
        except Exception as e:
            status_code = 500
            raise
        finally:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            with self._lock:
                self.active_requests.pop(request_id, None)
            
            # Record request metrics
            request_metrics = RequestMetrics(
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                duration_ms=duration_ms,
                timestamp=datetime.utcnow()
            )
            self.record_request(request_metrics)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary."""
        with self._lock:
            now = datetime.utcnow()
            
            # Recent metrics (last 5 minutes)
            recent_cutoff = now - timedelta(minutes=5)
            recent_requests = [
                r for r in self.request_history 
                if r.timestamp >= recent_cutoff
            ]
            
            # Calculate statistics
            total_requests = len(self.request_history)
            recent_request_count = len(recent_requests)
            
            # Response time statistics
            if recent_requests:
                response_times = [r.duration_ms for r in recent_requests]
                avg_response_time = sum(response_times) / len(response_times)
                max_response_time = max(response_times)
                min_response_time = min(response_times)
            else:
                avg_response_time = max_response_time = min_response_time = 0
            
            # Error rate
            recent_errors = [r for r in recent_requests if r.status_code >= 400]
            error_rate = (len(recent_errors) / recent_request_count * 100) if recent_request_count > 0 else 0
            
            # Active requests
            active_request_count = len(self.active_requests)
            
            return {
                "timestamp": now,
                "requests": {
                    "total": total_requests,
                    "recent_5min": recent_request_count,
                    "active": active_request_count,
                    "error_rate_percent": round(error_rate, 2)
                },
                "performance": {
                    "avg_response_time_ms": round(avg_response_time, 2),
                    "max_response_time_ms": round(max_response_time, 2),
                    "min_response_time_ms": round(min_response_time, 2)
                },
                "system": {
                    "cpu_percent": self.gauges.get("system_cpu_percent", 0),
                    "memory_percent": self.gauges.get("system_memory_percent", 0),
                    "disk_percent": self.gauges.get("system_disk_percent", 0),
                    "process_memory_mb": self.gauges.get("process_memory_rss_mb", 0)
                },
                "counters": dict(self.counters),
                "gauges": dict(self.gauges)
            }
    
    def get_prometheus_metrics(self) -> str:
        """Generate Prometheus-compatible metrics."""
        lines = []
        
        with self._lock:
            # Counters
            for key, value in self.counters.items():
                name, labels = self._parse_key(key)
                label_str = self._format_labels(labels) if labels else ""
                lines.append(f"# TYPE {name} counter")
                lines.append(f"{name}{label_str} {value}")
            
            # Gauges
            for key, value in self.gauges.items():
                name, labels = self._parse_key(key)
                label_str = self._format_labels(labels) if labels else ""
                lines.append(f"# TYPE {name} gauge")
                lines.append(f"{name}{label_str} {value}")
            
            # Histograms (simplified)
            for key, values in self.histograms.items():
                if values:
                    name, labels = self._parse_key(key)
                    label_str = self._format_labels(labels) if labels else ""
                    lines.append(f"# TYPE {name} histogram")
                    lines.append(f"{name}_sum{label_str} {sum(values)}")
                    lines.append(f"{name}_count{label_str} {len(values)}")
        
        return "\n".join(lines)
    
    def _make_key(self, name: str, labels: Optional[Dict[str, str]] = None) -> str:
        """Create a unique key for metric storage."""
        if not labels:
            return name
        
        label_parts = [f"{k}={v}" for k, v in sorted(labels.items())]
        return f"{name}|{','.join(label_parts)}"
    
    def _parse_key(self, key: str) -> tuple:
        """Parse a metric key back to name and labels."""
        if "|" not in key:
            return key, {}
        
        name, label_str = key.split("|", 1)
        labels = {}
        
        if label_str:
            for part in label_str.split(","):
                if "=" in part:
                    k, v = part.split("=", 1)
                    labels[k] = v
        
        return name, labels
    
    def _format_labels(self, labels: Dict[str, str]) -> str:
        """Format labels for Prometheus output."""
        if not labels:
            return ""
        
        label_parts = [f'{k}="{v}"' for k, v in sorted(labels.items())]
        return "{" + ",".join(label_parts) + "}"


# Global metrics collector instance
metrics_collector = MetricsCollector()
