"""
Custom exceptions and error handling for the AI service.
"""
import logging
from typing import Dict, Any, Optional
from enum import Enum

from ai_service.core.error_handler import (
    StandardError, ErrorContext, ErrorCategory, ErrorSeverity,
    error_handler
)

logger = logging.getLogger(__name__)


class ErrorCode(Enum):
    """Error codes for different types of failures."""

    # Client errors (4xx)
    INVALID_REQUEST = "INVALID_REQUEST"
    INVALID_MODEL = "INVALID_MODEL"
    INVALID_PARAMETERS = "INVALID_PARAMETERS"
    MISSING_API_KEY = "MISSING_API_KEY"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    NOT_FOUND = "NOT_FOUND"

    # Server errors (5xx)
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    TIMEOUT = "TIMEOUT"
    API_ERROR = "API_ERROR"

    # AI-specific errors
    MODEL_OVERLOADED = "MODEL_OVERLOADED"
    CONTENT_FILTERED = "CONTENT_FILTERED"
    CONTEXT_LENGTH_EXCEEDED = "CONTEXT_LENGTH_EXCEEDED"
    GENERATION_FAILED = "GENERATION_FAILED"

    # File handling errors
    FILE_NOT_FOUND = "FILE_NOT_FOUND"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    UNSUPPORTED_FILE_TYPE = "UNSUPPORTED_FILE_TYPE"
    FILE_UPLOAD_FAILED = "FILE_UPLOAD_FAILED"

    # Function calling errors
    FUNCTION_ERROR = "FUNCTION_ERROR"


class AIServiceException(Exception):
    """Base exception for AI service errors with enhanced error handling."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[ErrorContext] = None,
        suggestions: Optional[list] = None,
        retry_after: Optional[int] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}

        # Map ErrorCode to ErrorCategory
        category_map = {
            ErrorCode.INVALID_REQUEST: ErrorCategory.VALIDATION,
            ErrorCode.INVALID_MODEL: ErrorCategory.VALIDATION,
            ErrorCode.INVALID_PARAMETERS: ErrorCategory.VALIDATION,
            ErrorCode.MISSING_API_KEY: ErrorCategory.AUTHENTICATION,
            ErrorCode.RATE_LIMIT_EXCEEDED: ErrorCategory.RATE_LIMIT,
            ErrorCode.QUOTA_EXCEEDED: ErrorCategory.RATE_LIMIT,
            ErrorCode.UNAUTHORIZED: ErrorCategory.AUTHENTICATION,
            ErrorCode.FORBIDDEN: ErrorCategory.AUTHORIZATION,
            ErrorCode.NOT_FOUND: ErrorCategory.VALIDATION,
            ErrorCode.INTERNAL_ERROR: ErrorCategory.INTERNAL,
            ErrorCode.SERVICE_UNAVAILABLE: ErrorCategory.EXTERNAL_API,
            ErrorCode.TIMEOUT: ErrorCategory.TIMEOUT,
            ErrorCode.API_ERROR: ErrorCategory.EXTERNAL_API,
            ErrorCode.MODEL_OVERLOADED: ErrorCategory.MODEL_ERROR,
            ErrorCode.CONTENT_FILTERED: ErrorCategory.MODEL_ERROR,
            ErrorCode.CONTEXT_LENGTH_EXCEEDED: ErrorCategory.MODEL_ERROR,
            ErrorCode.GENERATION_FAILED: ErrorCategory.MODEL_ERROR,
            ErrorCode.FILE_NOT_FOUND: ErrorCategory.RESOURCE,
            ErrorCode.FILE_TOO_LARGE: ErrorCategory.RESOURCE,
            ErrorCode.UNSUPPORTED_FILE_TYPE: ErrorCategory.VALIDATION,
            ErrorCode.FILE_UPLOAD_FAILED: ErrorCategory.RESOURCE,
            ErrorCode.FUNCTION_ERROR: ErrorCategory.FUNCTION_CALLING,
        }

        # Determine severity based on status code
        if status_code >= 500:
            severity = ErrorSeverity.HIGH
        elif status_code >= 400:
            severity = ErrorSeverity.MEDIUM
        else:
            severity = ErrorSeverity.LOW

        # Create standardized error
        self.standard_error = error_handler.create_error(
            code=error_code.value,
            message=message,
            category=category_map.get(error_code, ErrorCategory.INTERNAL),
            severity=severity,
            context=context or ErrorContext(),
            details=details,
            suggestions=suggestions,
            retry_after=retry_after
        )

        super().__init__(self.message)

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API response."""
        return error_handler.to_http_response(self.standard_error)

    def get_http_status_code(self) -> int:
        """Get HTTP status code for this exception."""
        return error_handler.get_http_status_code(self.standard_error)


class ValidationError(AIServiceException):
    """Exception for input validation errors."""

    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        details = details or {}
        if field:
            details["field"] = field
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_REQUEST,
            status_code=400,
            details=details
        )


class ModelError(AIServiceException):
    """Exception for model-related errors."""

    def __init__(self, message: str, model_name: str = None, details: Dict[str, Any] = None):
        details = details or {}
        if model_name:
            details["model"] = model_name
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_MODEL,
            status_code=400,
            details=details
        )


class APIError(AIServiceException):
    """Exception for external API errors."""

    def __init__(self, message: str, api_response: Dict[str, Any] = None, details: Dict[str, Any] = None):
        details = details or {}
        if api_response:
            details["api_response"] = api_response
        super().__init__(
            message=message,
            error_code=ErrorCode.API_ERROR,
            status_code=502,
            details=details
        )


class FileError(AIServiceException):
    """Exception for file handling errors."""

    def __init__(self, message: str, file_path: str = None, error_code: ErrorCode = ErrorCode.FILE_NOT_FOUND, details: Dict[str, Any] = None):
        details = details or {}
        if file_path:
            details["file_path"] = file_path

        status_code = 404 if error_code == ErrorCode.FILE_NOT_FOUND else 400

        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details=details
        )


class FunctionCallError(AIServiceException):
    """Exception for function calling errors."""

    def __init__(self, message: str, function_name: str = None, details: Dict[str, Any] = None):
        details = details or {}
        if function_name:
            details["function_name"] = function_name
        super().__init__(
            message=message,
            error_code=ErrorCode.FUNCTION_ERROR,
            status_code=400,
            details=details
        )


class FunctionExecutionError(FunctionCallError):
    """Exception for function execution errors."""

    def __init__(self, message: str, function_name: str = None, execution_time: float = None, details: Dict[str, Any] = None):
        details = details or {}
        if execution_time:
            details["execution_time"] = execution_time
        super().__init__(message, function_name, details)


class FunctionDefinitionError(FunctionCallError):
    """Exception for invalid function definitions."""

    def __init__(self, message: str, function_name: str = None, details: Dict[str, Any] = None):
        super().__init__(message, function_name, details)


class RateLimitError(AIServiceException):
    """Exception for rate limiting errors."""

    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = None, details: Dict[str, Any] = None):
        details = details or {}
        if retry_after:
            details["retry_after"] = retry_after
        super().__init__(
            message=message,
            error_code=ErrorCode.RATE_LIMIT_EXCEEDED,
            status_code=429,
            details=details
        )


def handle_api_exception(e: Exception, context: str = "") -> AIServiceException:
    """Convert various exceptions to AIServiceException with proper error codes."""

    logger.error(f"Exception in {context}: {str(e)}", exc_info=True)

    # Handle specific Google API errors
    if "quota" in str(e).lower():
        return AIServiceException(
            message="API quota exceeded",
            error_code=ErrorCode.QUOTA_EXCEEDED,
            status_code=429,
            details={"original_error": str(e), "context": context}
        )

    if "rate limit" in str(e).lower():
        return RateLimitError(
            message="Rate limit exceeded",
            details={"original_error": str(e), "context": context}
        )

    if "unauthorized" in str(e).lower() or "api key" in str(e).lower():
        return AIServiceException(
            message="Invalid or missing API key",
            error_code=ErrorCode.UNAUTHORIZED,
            status_code=401,
            details={"original_error": str(e), "context": context}
        )

    if "not found" in str(e).lower():
        return AIServiceException(
            message="Resource not found",
            error_code=ErrorCode.NOT_FOUND,
            status_code=404,
            details={"original_error": str(e), "context": context}
        )

    if "timeout" in str(e).lower():
        return AIServiceException(
            message="Request timeout",
            error_code=ErrorCode.TIMEOUT,
            status_code=408,
            details={"original_error": str(e), "context": context}
        )

    if "content" in str(e).lower() and "filter" in str(e).lower():
        return AIServiceException(
            message="Content was filtered by safety settings",
            error_code=ErrorCode.CONTENT_FILTERED,
            status_code=400,
            details={"original_error": str(e), "context": context}
        )

    # Default to internal server error
    return AIServiceException(
        message="Internal server error",
        error_code=ErrorCode.INTERNAL_ERROR,
        status_code=500,
        details={"original_error": str(e), "context": context}
    )


def log_error(exception: AIServiceException, request_id: str = None):
    """Log error with proper formatting and context."""

    log_data = {
        "error_code": exception.error_code.value,
        "message": exception.message,
        "status_code": exception.status_code,
        "details": exception.details
    }

    if request_id:
        log_data["request_id"] = request_id

    if exception.status_code >= 500:
        logger.error(f"Server error: {log_data}")
    elif exception.status_code >= 400:
        logger.warning(f"Client error: {log_data}")
    else:
        logger.info(f"Error handled: {log_data}")
