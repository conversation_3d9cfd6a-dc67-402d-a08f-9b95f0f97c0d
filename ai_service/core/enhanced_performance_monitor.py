"""
Enhanced Performance Monitoring System for AI Service.
Tracks metrics, performance KPIs, and provides comprehensive health monitoring.
"""

import time
import psutil
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import json
import threading
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics to track."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricData:
    """Individual metric data point."""
    name: str
    value: float
    metric_type: MetricType
    timestamp: float = field(default_factory=time.time)
    labels: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "value": self.value,
            "type": self.metric_type.value,
            "timestamp": self.timestamp,
            "labels": self.labels
        }


@dataclass
class RequestMetrics:
    """Enhanced metrics for individual requests."""
    endpoint: str
    method: str
    status_code: int
    response_time: float
    timestamp: float
    model_used: Optional[str] = None
    tokens_used: Optional[int] = None
    error_type: Optional[str] = None
    user_id: Optional[str] = None
    request_size: Optional[int] = None
    response_size: Optional[int] = None
    correlation_id: Optional[str] = None


class EnhancedPerformanceMonitor:
    """Comprehensive performance monitoring system."""
    
    def __init__(self, max_history: int = 10000, aggregation_window: int = 300):
        self.max_history = max_history
        self.aggregation_window = aggregation_window
        
        # Metrics storage
        self.request_history: deque = deque(maxlen=max_history)
        self.metrics_history: deque = deque(maxlen=max_history)
        self.endpoint_stats: Dict[str, Dict] = defaultdict(dict)
        
        # Real-time counters
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        
        # System metrics
        self.system_metrics: Dict[str, Any] = {}
        
        # Background monitoring
        self._monitoring_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        
        # Thread safety
        self._lock = threading.Lock()
        
        logger.info("Enhanced performance monitor initialized")
    
    def start_monitoring(self):
        """Start background monitoring tasks."""
        if self._monitoring_task is None or self._monitoring_task.done():
            self._monitoring_task = asyncio.create_task(self._background_monitoring())
    
    async def _background_monitoring(self):
        """Background task for system monitoring."""
        while not self._shutdown_event.is_set():
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(30)  # Collect every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background monitoring: {e}")
    
    async def _collect_system_metrics(self):
        """Collect comprehensive system-level metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else (0, 0, 0)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            # Network metrics
            try:
                network = psutil.net_io_counters()
                bytes_sent = network.bytes_sent
                bytes_recv = network.bytes_recv
                packets_sent = network.packets_sent
                packets_recv = network.packets_recv
            except:
                bytes_sent = bytes_recv = packets_sent = packets_recv = 0
            
            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()
            process_threads = process.num_threads()
            
            # File descriptors (Unix only)
            try:
                process_fds = process.num_fds()
            except:
                process_fds = 0
            
            with self._lock:
                self.system_metrics.update({
                    # CPU metrics
                    "cpu_percent": cpu_percent,
                    "cpu_count": cpu_count,
                    "load_avg_1m": load_avg[0],
                    "load_avg_5m": load_avg[1],
                    "load_avg_15m": load_avg[2],
                    
                    # Memory metrics
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "memory_used_gb": memory.used / (1024**3),
                    "memory_total_gb": memory.total / (1024**3),
                    "swap_percent": swap.percent,
                    "swap_used_gb": swap.used / (1024**3),
                    
                    # Disk metrics
                    "disk_percent": disk.percent,
                    "disk_free_gb": disk.free / (1024**3),
                    "disk_used_gb": disk.used / (1024**3),
                    "disk_total_gb": disk.total / (1024**3),
                    "disk_read_bytes": disk_io.read_bytes if disk_io else 0,
                    "disk_write_bytes": disk_io.write_bytes if disk_io else 0,
                    
                    # Network metrics
                    "network_bytes_sent": bytes_sent,
                    "network_bytes_recv": bytes_recv,
                    "network_packets_sent": packets_sent,
                    "network_packets_recv": packets_recv,
                    
                    # Process metrics
                    "process_memory_mb": process_memory.rss / (1024**2),
                    "process_memory_vms_mb": process_memory.vms / (1024**2),
                    "process_cpu_percent": process_cpu,
                    "process_threads": process_threads,
                    "process_fds": process_fds,
                    
                    "timestamp": time.time()
                })
                
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def record_request(self, metrics: RequestMetrics):
        """Record request metrics with enhanced tracking."""
        with self._lock:
            self.request_history.append(metrics)
            self._update_endpoint_stats(metrics)
            
            # Update counters
            self.counters["total_requests"] += 1
            self.counters[f"requests_{metrics.endpoint}"] += 1
            self.counters[f"requests_{metrics.method}"] += 1
            
            if metrics.status_code >= 400:
                self.counters["error_requests"] += 1
                self.counters[f"errors_{metrics.status_code}"] += 1
            else:
                self.counters["success_requests"] += 1
            
            # Update histograms
            self.histograms["response_times"].append(metrics.response_time)
            if metrics.tokens_used:
                self.histograms["tokens_used"].append(metrics.tokens_used)
            if metrics.request_size:
                self.histograms["request_sizes"].append(metrics.request_size)
            if metrics.response_size:
                self.histograms["response_sizes"].append(metrics.response_size)
    
    def _update_endpoint_stats(self, metrics: RequestMetrics):
        """Update comprehensive endpoint-specific statistics."""
        endpoint = metrics.endpoint
        
        if endpoint not in self.endpoint_stats:
            self.endpoint_stats[endpoint] = {
                "total_requests": 0,
                "success_requests": 0,
                "error_requests": 0,
                "total_response_time": 0,
                "min_response_time": float('inf'),
                "max_response_time": 0,
                "total_tokens": 0,
                "total_request_size": 0,
                "total_response_size": 0,
                "error_types": defaultdict(int),
                "status_codes": defaultdict(int),
                "models_used": defaultdict(int),
                "hourly_stats": defaultdict(lambda: {"requests": 0, "errors": 0})
            }
        
        stats = self.endpoint_stats[endpoint]
        stats["total_requests"] += 1
        stats["total_response_time"] += metrics.response_time
        stats["min_response_time"] = min(stats["min_response_time"], metrics.response_time)
        stats["max_response_time"] = max(stats["max_response_time"], metrics.response_time)
        
        # Status code tracking
        stats["status_codes"][metrics.status_code] += 1
        
        if metrics.status_code >= 400:
            stats["error_requests"] += 1
            if metrics.error_type:
                stats["error_types"][metrics.error_type] += 1
        else:
            stats["success_requests"] += 1
        
        # Additional metrics
        if metrics.tokens_used:
            stats["total_tokens"] += metrics.tokens_used
        if metrics.request_size:
            stats["total_request_size"] += metrics.request_size
        if metrics.response_size:
            stats["total_response_size"] += metrics.response_size
        if metrics.model_used:
            stats["models_used"][metrics.model_used] += 1
        
        # Hourly statistics
        hour_key = datetime.fromtimestamp(metrics.timestamp).strftime("%Y-%m-%d-%H")
        stats["hourly_stats"][hour_key]["requests"] += 1
        if metrics.status_code >= 400:
            stats["hourly_stats"][hour_key]["errors"] += 1
    
    def get_performance_summary(self, time_window: int = 3600) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        cutoff_time = time.time() - time_window
        
        with self._lock:
            # Filter recent requests
            recent_requests = [
                r for r in self.request_history 
                if r.timestamp > cutoff_time
            ]
            
            if not recent_requests:
                return {
                    "time_window": time_window,
                    "total_requests": 0,
                    "message": "No requests in time window"
                }
            
            # Calculate comprehensive statistics
            total_requests = len(recent_requests)
            success_requests = sum(1 for r in recent_requests if r.status_code < 400)
            error_requests = total_requests - success_requests
            
            response_times = [r.response_time for r in recent_requests]
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            # Calculate percentiles
            sorted_times = sorted(response_times)
            p50 = sorted_times[int(len(sorted_times) * 0.5)]
            p90 = sorted_times[int(len(sorted_times) * 0.9)]
            p95 = sorted_times[int(len(sorted_times) * 0.95)]
            p99 = sorted_times[int(len(sorted_times) * 0.99)]
            
            # Error analysis
            error_breakdown = defaultdict(int)
            status_code_breakdown = defaultdict(int)
            for r in recent_requests:
                status_code_breakdown[r.status_code] += 1
                if r.status_code >= 400 and r.error_type:
                    error_breakdown[r.error_type] += 1
            
            # Model usage analysis
            model_usage = defaultdict(int)
            for r in recent_requests:
                if r.model_used:
                    model_usage[r.model_used] += 1
            
            # Token usage analysis
            token_stats = {}
            tokens_used = [r.tokens_used for r in recent_requests if r.tokens_used]
            if tokens_used:
                token_stats = {
                    "total_tokens": sum(tokens_used),
                    "avg_tokens": sum(tokens_used) / len(tokens_used),
                    "min_tokens": min(tokens_used),
                    "max_tokens": max(tokens_used)
                }
            
            return {
                "time_window": time_window,
                "timestamp": time.time(),
                "total_requests": total_requests,
                "success_requests": success_requests,
                "error_requests": error_requests,
                "success_rate": success_requests / total_requests,
                "error_rate": error_requests / total_requests,
                "response_time": {
                    "avg": avg_response_time,
                    "min": min_response_time,
                    "max": max_response_time,
                    "p50": p50,
                    "p90": p90,
                    "p95": p95,
                    "p99": p99
                },
                "requests_per_second": total_requests / time_window,
                "error_breakdown": dict(error_breakdown),
                "status_code_breakdown": dict(status_code_breakdown),
                "model_usage": dict(model_usage),
                "token_stats": token_stats,
                "system_metrics": self.system_metrics.copy()
            }


# Global enhanced performance monitor instance
enhanced_performance_monitor = EnhancedPerformanceMonitor()


# Utility functions
def record_enhanced_request_metrics(
    endpoint: str,
    method: str,
    status_code: int,
    response_time: float,
    **kwargs
):
    """Utility function to record enhanced request metrics."""
    metrics = RequestMetrics(
        endpoint=endpoint,
        method=method,
        status_code=status_code,
        response_time=response_time,
        timestamp=time.time(),
        **kwargs
    )
    enhanced_performance_monitor.record_request(metrics)
