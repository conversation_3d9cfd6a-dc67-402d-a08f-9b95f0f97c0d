"""
Resource management for AI service operations.
"""
import asyncio
import logging
import os
import tempfile
import time
import weakref
from contextlib import asynccontextmanager, contextmanager
from typing import Any, Dict, List, Optional, Set
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)


class ResourceTracker:
    """Track and manage resources like files, connections, and memory."""

    def __init__(self):
        self._temp_files: Set[str] = set()
        self._active_streams: Dict[Any, Dict[str, Any]] = {}  # Enhanced tracking with metadata
        self._thread_pools: List[ThreadPoolExecutor] = []
        self._lock = threading.Lock()
        self._stream_timeouts: Dict[Any, float] = {}  # Track stream creation times
        self._max_stream_lifetime = 3600  # 1 hour max lifetime

    def register_temp_file(self, file_path: str):
        """Register a temporary file for cleanup."""
        with self._lock:
            self._temp_files.add(file_path)
            logger.debug(f"Registered temp file: {file_path}")

    def unregister_temp_file(self, file_path: str):
        """Unregister a temporary file."""
        with self._lock:
            self._temp_files.discard(file_path)
            logger.debug(f"Unregistered temp file: {file_path}")

    def register_stream(self, stream: Any, metadata: Optional[Dict[str, Any]] = None):
        """Register an active stream with enhanced tracking."""
        with self._lock:
            current_time = time.time()
            self._active_streams[stream] = {
                "created_at": current_time,
                "type": type(stream).__name__,
                "metadata": metadata or {},
                "access_count": 0
            }
            self._stream_timeouts[stream] = current_time
            logger.debug(f"Registered stream: {type(stream).__name__} with metadata: {metadata}")

    def unregister_stream(self, stream: Any):
        """Unregister a stream with cleanup verification."""
        with self._lock:
            if stream in self._active_streams:
                stream_info = self._active_streams[stream]
                lifetime = time.time() - stream_info["created_at"]
                logger.debug(f"Unregistered stream: {stream_info['type']} (lifetime: {lifetime:.2f}s)")
                del self._active_streams[stream]

            self._stream_timeouts.pop(stream, None)

    def register_thread_pool(self, pool: ThreadPoolExecutor):
        """Register a thread pool for cleanup."""
        with self._lock:
            self._thread_pools.append(pool)
            logger.debug(f"Registered thread pool with {pool._max_workers} workers")
    
    def cleanup_temp_files(self):
        """Clean up all registered temporary files."""
        with self._lock:
            files_to_remove = list(self._temp_files)
        
        removed_count = 0
        for file_path in files_to_remove:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    removed_count += 1
                    logger.debug(f"Removed temp file: {file_path}")
                self.unregister_temp_file(file_path)
            except Exception as e:
                logger.warning(f"Failed to remove temp file {file_path}: {e}")
        
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} temporary files")
    
    def cleanup_streams(self, force_cleanup: bool = False):
        """Clean up all active streams with enhanced error handling."""
        with self._lock:
            streams_to_close = list(self._active_streams.keys())

        closed_count = 0
        error_count = 0
        current_time = time.time()

        for stream in streams_to_close:
            try:
                stream_info = self._active_streams.get(stream, {})
                stream_age = current_time - stream_info.get("created_at", 0)

                # Force cleanup if stream is too old or force_cleanup is True
                should_cleanup = force_cleanup or stream_age > self._max_stream_lifetime

                if should_cleanup:
                    # Try multiple cleanup methods
                    cleanup_success = False

                    # Method 1: aclose for async streams
                    if hasattr(stream, 'aclose'):
                        try:
                            if asyncio.iscoroutinefunction(stream.aclose):
                                # Schedule async cleanup
                                asyncio.create_task(stream.aclose())
                            else:
                                stream.aclose()
                            cleanup_success = True
                        except Exception as e:
                            logger.debug(f"aclose failed for {stream_info.get('type', 'unknown')}: {e}")

                    # Method 2: close for sync streams
                    if not cleanup_success and hasattr(stream, 'close'):
                        try:
                            stream.close()
                            cleanup_success = True
                        except Exception as e:
                            logger.debug(f"close failed for {stream_info.get('type', 'unknown')}: {e}")

                    # Method 3: cancel for asyncio tasks/generators
                    if not cleanup_success and hasattr(stream, 'cancel'):
                        try:
                            stream.cancel()
                            cleanup_success = True
                        except Exception as e:
                            logger.debug(f"cancel failed for {stream_info.get('type', 'unknown')}: {e}")

                    if cleanup_success:
                        closed_count += 1
                        logger.debug(f"Closed stream: {stream_info.get('type', 'unknown')} (age: {stream_age:.2f}s)")
                    else:
                        error_count += 1
                        logger.warning(f"Failed to close stream: {stream_info.get('type', 'unknown')}")

                    self.unregister_stream(stream)

            except Exception as e:
                error_count += 1
                logger.warning(f"Error during stream cleanup: {e}")
                # Still try to unregister
                self.unregister_stream(stream)

        if closed_count > 0:
            logger.info(f"Cleaned up {closed_count} streams")
        if error_count > 0:
            logger.warning(f"Failed to clean up {error_count} streams")
    
    def cleanup_thread_pools(self):
        """Clean up all thread pools."""
        with self._lock:
            pools_to_shutdown = list(self._thread_pools)
            self._thread_pools.clear()
        
        shutdown_count = 0
        for pool in pools_to_shutdown:
            try:
                pool.shutdown(wait=True, cancel_futures=True)
                shutdown_count += 1
                logger.debug("Shutdown thread pool")
            except Exception as e:
                logger.warning(f"Failed to shutdown thread pool: {e}")
        
        if shutdown_count > 0:
            logger.info(f"Shutdown {shutdown_count} thread pools")
    
    def cleanup_all(self):
        """Clean up all resources."""
        logger.info("Starting resource cleanup")
        self.cleanup_streams()
        self.cleanup_temp_files()
        self.cleanup_thread_pools()
        logger.info("Resource cleanup completed")
    
    def get_stats(self) -> Dict[str, int]:
        """Get resource usage statistics."""
        with self._lock:
            return {
                "temp_files": len(self._temp_files),
                "active_streams": len(self._active_streams),
                "thread_pools": len(self._thread_pools)
            }


class ManagedTempFile:
    """Context manager for temporary files with automatic cleanup."""
    
    def __init__(self, suffix: str = "", prefix: str = "ai_service_", dir: Optional[str] = None):
        self.suffix = suffix
        self.prefix = prefix
        self.dir = dir
        self.file_path: Optional[str] = None
        self.file_obj = None
    
    def __enter__(self):
        self.file_obj = tempfile.NamedTemporaryFile(
            suffix=self.suffix,
            prefix=self.prefix,
            dir=self.dir,
            delete=False
        )
        self.file_path = self.file_obj.name
        resource_tracker.register_temp_file(self.file_path)
        logger.debug(f"Created managed temp file: {self.file_path}")
        return self.file_obj
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.file_obj:
            self.file_obj.close()
        
        if self.file_path:
            try:
                if os.path.exists(self.file_path):
                    os.remove(self.file_path)
                    logger.debug(f"Removed managed temp file: {self.file_path}")
                resource_tracker.unregister_temp_file(self.file_path)
            except Exception as e:
                logger.warning(f"Failed to remove managed temp file {self.file_path}: {e}")


class ManagedThreadPool:
    """Context manager for thread pools with automatic cleanup."""
    
    def __init__(self, max_workers: int = 4, thread_name_prefix: str = "ai_service"):
        self.max_workers = max_workers
        self.thread_name_prefix = thread_name_prefix
        self.pool: Optional[ThreadPoolExecutor] = None
    
    def __enter__(self):
        self.pool = ThreadPoolExecutor(
            max_workers=self.max_workers,
            thread_name_prefix=self.thread_name_prefix
        )
        resource_tracker.register_thread_pool(self.pool)
        logger.debug(f"Created managed thread pool with {self.max_workers} workers")
        return self.pool
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.pool:
            try:
                self.pool.shutdown(wait=True, cancel_futures=True)
                logger.debug("Shutdown managed thread pool")
            except Exception as e:
                logger.warning(f"Failed to shutdown managed thread pool: {e}")


@contextmanager
def managed_temp_file(suffix: str = "", prefix: str = "ai_service_", dir: Optional[str] = None):
    """Context manager for temporary files."""
    with ManagedTempFile(suffix=suffix, prefix=prefix, dir=dir) as temp_file:
        yield temp_file


@contextmanager
def managed_thread_pool(max_workers: int = 4, thread_name_prefix: str = "ai_service"):
    """Context manager for thread pools."""
    with ManagedThreadPool(max_workers=max_workers, thread_name_prefix=thread_name_prefix) as pool:
        yield pool


@asynccontextmanager
async def managed_stream(stream_factory, *args, **kwargs):
    """Context manager for streams with automatic cleanup."""
    stream = None
    try:
        stream = await stream_factory(*args, **kwargs) if asyncio.iscoroutinefunction(stream_factory) else stream_factory(*args, **kwargs)
        resource_tracker.register_stream(stream)
        logger.debug(f"Created managed stream: {type(stream).__name__}")
        yield stream
    finally:
        if stream:
            try:
                if hasattr(stream, 'aclose'):
                    await stream.aclose()
                elif hasattr(stream, 'close'):
                    stream.close()
                logger.debug(f"Closed managed stream: {type(stream).__name__}")
                resource_tracker.unregister_stream(stream)
            except Exception as e:
                logger.warning(f"Failed to close managed stream: {e}")


class MemoryMonitor:
    """Monitor memory usage and trigger cleanup when needed."""
    
    def __init__(self, threshold_mb: int = 500, check_interval: int = 30):
        self.threshold_mb = threshold_mb
        self.check_interval = check_interval
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            logger.warning("psutil not available, cannot monitor memory")
            return 0.0
    
    async def start_monitoring(self):
        """Start memory monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info(f"Started memory monitoring (threshold: {self.threshold_mb}MB)")
    
    async def stop_monitoring(self):
        """Stop memory monitoring."""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped memory monitoring")
    
    async def _monitor_loop(self):
        """Memory monitoring loop."""
        while self._monitoring:
            try:
                memory_mb = self.get_memory_usage()
                if memory_mb > self.threshold_mb:
                    logger.warning(f"Memory usage ({memory_mb:.1f}MB) exceeds threshold ({self.threshold_mb}MB)")
                    resource_tracker.cleanup_all()
                
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                await asyncio.sleep(self.check_interval)


# Global instances
resource_tracker = ResourceTracker()
memory_monitor = MemoryMonitor()
