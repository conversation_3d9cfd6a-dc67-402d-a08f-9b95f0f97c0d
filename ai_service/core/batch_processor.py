"""
Batch processing system for bulk operations and performance optimization.
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable, Union, TypeVar, Generic
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque
from enum import Enum
import uuid

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)

T = TypeVar('T')
R = TypeVar('R')


class BatchStatus(Enum):
    """Batch processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BatchItem:
    """Individual item in a batch."""
    id: str
    data: Any
    created_at: datetime = field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    status: BatchStatus = BatchStatus.PENDING


@dataclass
class BatchJob:
    """Batch job containing multiple items."""
    id: str
    items: List[BatchItem]
    processor_func: Callable
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: BatchStatus = BatchStatus.PENDING
    progress: float = 0.0
    total_items: int = 0
    processed_items: int = 0
    failed_items: int = 0
    batch_size: int = 10
    max_concurrent: int = 5


@dataclass
class BatchConfig:
    """Batch processing configuration."""
    batch_size: int = 10
    max_concurrent: int = 5
    timeout_seconds: int = 300
    retry_attempts: int = 3
    retry_delay: float = 1.0
    auto_flush_interval: int = 30
    max_queue_size: int = 1000


class BatchProcessor(Generic[T, R]):
    """High-performance batch processor for bulk operations."""
    
    def __init__(self, config: Optional[BatchConfig] = None):
        self.config = config or BatchConfig()
        
        # Job management
        self.jobs: Dict[str, BatchJob] = {}
        self.job_queue: deque = deque()
        self.processing_jobs: Dict[str, asyncio.Task] = {}
        
        # Auto-flush mechanism
        self.pending_items: Dict[str, List[BatchItem]] = {}
        self.last_flush_time: Dict[str, datetime] = {}
        
        # Statistics
        self.total_jobs_processed = 0
        self.total_items_processed = 0
        self.total_processing_time = 0.0
        
        # Background tasks
        self.auto_flush_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Start background tasks
        self._start_background_tasks()
        
        logger.info(f"Batch processor initialized with config: {self.config}")
    
    def _start_background_tasks(self):
        """Start background tasks for auto-flush and cleanup."""
        self.auto_flush_task = asyncio.create_task(self._auto_flush_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def add_item(
        self,
        processor_name: str,
        data: T,
        processor_func: Callable[[List[T]], List[R]],
        item_id: Optional[str] = None
    ) -> str:
        """Add item to batch queue."""
        item_id = item_id or str(uuid.uuid4())
        
        # Create batch item
        item = BatchItem(
            id=item_id,
            data=data
        )
        
        # Add to pending items
        if processor_name not in self.pending_items:
            self.pending_items[processor_name] = []
            self.last_flush_time[processor_name] = datetime.utcnow()
        
        self.pending_items[processor_name].append(item)
        
        # Check if batch is ready for processing
        if len(self.pending_items[processor_name]) >= self.config.batch_size:
            await self._flush_batch(processor_name, processor_func)
        
        return item_id
    
    async def process_batch(
        self,
        items: List[T],
        processor_func: Callable[[List[T]], List[R]],
        batch_id: Optional[str] = None
    ) -> str:
        """Process a batch of items immediately."""
        batch_id = batch_id or str(uuid.uuid4())
        
        # Create batch items
        batch_items = []
        for i, data in enumerate(items):
            item = BatchItem(
                id=f"{batch_id}_{i}",
                data=data
            )
            batch_items.append(item)
        
        # Create batch job
        job = BatchJob(
            id=batch_id,
            items=batch_items,
            processor_func=processor_func,
            total_items=len(batch_items),
            batch_size=self.config.batch_size,
            max_concurrent=self.config.max_concurrent
        )
        
        # Store job
        self.jobs[batch_id] = job
        
        # Start processing
        task = asyncio.create_task(self._process_job(job))
        self.processing_jobs[batch_id] = task
        
        return batch_id
    
    async def get_job_status(self, job_id: str) -> Optional[BatchJob]:
        """Get status of a batch job."""
        return self.jobs.get(job_id)
    
    async def get_item_result(self, job_id: str, item_id: str) -> Optional[BatchItem]:
        """Get result of a specific item."""
        job = self.jobs.get(job_id)
        if job:
            for item in job.items:
                if item.id == item_id:
                    return item
        return None
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a batch job."""
        if job_id in self.processing_jobs:
            task = self.processing_jobs[job_id]
            task.cancel()
            
            job = self.jobs.get(job_id)
            if job:
                job.status = BatchStatus.CANCELLED
            
            return True
        return False
    
    async def _flush_batch(self, processor_name: str, processor_func: Callable):
        """Flush pending items as a batch."""
        if processor_name not in self.pending_items or not self.pending_items[processor_name]:
            return
        
        # Get pending items
        items = self.pending_items[processor_name]
        self.pending_items[processor_name] = []
        self.last_flush_time[processor_name] = datetime.utcnow()
        
        # Create batch job
        batch_id = f"auto_{processor_name}_{int(time.time())}"
        job = BatchJob(
            id=batch_id,
            items=items,
            processor_func=processor_func,
            total_items=len(items),
            batch_size=self.config.batch_size,
            max_concurrent=self.config.max_concurrent
        )
        
        # Store and process job
        self.jobs[batch_id] = job
        task = asyncio.create_task(self._process_job(job))
        self.processing_jobs[batch_id] = task
        
        logger.info(f"Auto-flushed batch {batch_id} with {len(items)} items")
    
    async def _process_job(self, job: BatchJob):
        """Process a batch job."""
        job.status = BatchStatus.PROCESSING
        job.started_at = datetime.utcnow()
        start_time = time.time()
        
        try:
            # Process items in batches with concurrency control
            semaphore = asyncio.Semaphore(job.max_concurrent)
            
            async def process_batch_chunk(chunk: List[BatchItem]):
                async with semaphore:
                    return await self._process_chunk(chunk, job.processor_func)
            
            # Split items into chunks
            chunks = []
            for i in range(0, len(job.items), job.batch_size):
                chunk = job.items[i:i + job.batch_size]
                chunks.append(chunk)
            
            # Process chunks concurrently
            tasks = [process_batch_chunk(chunk) for chunk in chunks]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Update job status
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # Mark chunk items as failed
                    for item in chunks[i]:
                        item.status = BatchStatus.FAILED
                        item.error = str(result)
                        item.processed_at = datetime.utcnow()
                        job.failed_items += 1
                else:
                    # Mark chunk items as completed
                    for j, item in enumerate(chunks[i]):
                        if j < len(result):
                            item.result = result[j]
                            item.status = BatchStatus.COMPLETED
                        else:
                            item.status = BatchStatus.FAILED
                            item.error = "No result returned"
                            job.failed_items += 1
                        
                        item.processed_at = datetime.utcnow()
                        job.processed_items += 1
            
            # Update job progress
            job.progress = job.processed_items / job.total_items
            job.status = BatchStatus.COMPLETED if job.failed_items == 0 else BatchStatus.FAILED
            job.completed_at = datetime.utcnow()
            
            # Update statistics
            processing_time = time.time() - start_time
            self.total_jobs_processed += 1
            self.total_items_processed += job.total_items
            self.total_processing_time += processing_time
            
            # Record metrics
            metrics_collector.record_histogram(
                "batch_processing_duration_ms",
                processing_time * 1000,
                {
                    "job_id": job.id,
                    "total_items": str(job.total_items),
                    "status": job.status.value
                }
            )
            
            logger.info(
                f"Batch job {job.id} completed: {job.processed_items}/{job.total_items} items "
                f"({job.failed_items} failed) in {processing_time:.2f}s"
            )
            
        except Exception as e:
            job.status = BatchStatus.FAILED
            job.completed_at = datetime.utcnow()
            
            # Mark all items as failed
            for item in job.items:
                if item.status == BatchStatus.PENDING:
                    item.status = BatchStatus.FAILED
                    item.error = str(e)
                    item.processed_at = datetime.utcnow()
            
            logger.error(f"Batch job {job.id} failed: {e}")
            
        finally:
            # Clean up processing job
            self.processing_jobs.pop(job.id, None)
    
    async def _process_chunk(self, chunk: List[BatchItem], processor_func: Callable) -> List[Any]:
        """Process a chunk of items."""
        try:
            # Extract data from items
            data_list = [item.data for item in chunk]
            
            # Process with retry logic
            for attempt in range(self.config.retry_attempts):
                try:
                    if asyncio.iscoroutinefunction(processor_func):
                        results = await processor_func(data_list)
                    else:
                        results = processor_func(data_list)
                    
                    return results
                    
                except Exception as e:
                    if attempt == self.config.retry_attempts - 1:
                        raise
                    
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                    logger.warning(f"Chunk processing failed (attempt {attempt + 1}): {e}")
            
        except Exception as e:
            logger.error(f"Chunk processing failed after all retries: {e}")
            raise
    
    async def _auto_flush_loop(self):
        """Background task for auto-flushing batches."""
        while True:
            try:
                await asyncio.sleep(self.config.auto_flush_interval)
                
                current_time = datetime.utcnow()
                
                # Check each processor for auto-flush
                for processor_name in list(self.pending_items.keys()):
                    if not self.pending_items[processor_name]:
                        continue
                    
                    last_flush = self.last_flush_time.get(processor_name, current_time)
                    time_since_flush = (current_time - last_flush).total_seconds()
                    
                    if time_since_flush >= self.config.auto_flush_interval:
                        # Note: We need the processor function for flushing
                        # This is a limitation - we'll flush when we have the function
                        logger.debug(f"Auto-flush triggered for {processor_name}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Auto-flush loop error: {e}")
    
    async def _cleanup_loop(self):
        """Background task for cleaning up completed jobs."""
        while True:
            try:
                await asyncio.sleep(300)  # Clean up every 5 minutes
                
                current_time = datetime.utcnow()
                cleanup_threshold = current_time - timedelta(hours=1)
                
                # Clean up old completed jobs
                jobs_to_remove = []
                for job_id, job in self.jobs.items():
                    if (job.status in [BatchStatus.COMPLETED, BatchStatus.FAILED, BatchStatus.CANCELLED] and
                        job.completed_at and job.completed_at < cleanup_threshold):
                        jobs_to_remove.append(job_id)
                
                for job_id in jobs_to_remove:
                    del self.jobs[job_id]
                    logger.debug(f"Cleaned up old job: {job_id}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get batch processing statistics."""
        active_jobs = len(self.processing_jobs)
        pending_jobs = len(self.job_queue)
        total_jobs = len(self.jobs)
        
        avg_processing_time = 0.0
        if self.total_jobs_processed > 0:
            avg_processing_time = self.total_processing_time / self.total_jobs_processed
        
        return {
            "total_jobs_processed": self.total_jobs_processed,
            "total_items_processed": self.total_items_processed,
            "active_jobs": active_jobs,
            "pending_jobs": pending_jobs,
            "total_jobs": total_jobs,
            "average_processing_time_seconds": avg_processing_time,
            "pending_items_by_processor": {
                name: len(items) for name, items in self.pending_items.items()
            }
        }
    
    async def shutdown(self):
        """Shutdown batch processor."""
        # Cancel background tasks
        if self.auto_flush_task:
            self.auto_flush_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Cancel all processing jobs
        for task in self.processing_jobs.values():
            task.cancel()
        
        # Wait for tasks to complete
        if self.processing_jobs:
            await asyncio.gather(*self.processing_jobs.values(), return_exceptions=True)
        
        logger.info("Batch processor shutdown completed")


# Global batch processor instance
batch_processor = BatchProcessor()
