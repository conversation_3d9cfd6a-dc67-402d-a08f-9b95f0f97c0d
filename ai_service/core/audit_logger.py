"""
Comprehensive audit logging and compliance system.
"""
import asyncio
import json
import logging
import uuid
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum
import hashlib
import gzip
import os
from pathlib import Path

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class AuditEventType(Enum):
    """Audit event types."""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    API_ACCESS = "api_access"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    SYSTEM_CONFIGURATION = "system_configuration"
    USER_MANAGEMENT = "user_management"
    SECURITY_EVENT = "security_event"
    COMPLIANCE_EVENT = "compliance_event"
    ERROR_EVENT = "error_event"
    PERFORMANCE_EVENT = "performance_event"


class AuditLevel(Enum):
    """Audit event levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ComplianceStandard(Enum):
    """Compliance standards."""
    GDPR = "gdpr"
    HIPAA = "hipaa"
    SOX = "sox"
    PCI_DSS = "pci_dss"
    ISO_27001 = "iso_27001"
    SOC2 = "soc2"


@dataclass
class AuditEvent:
    """Audit event structure."""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.utcnow)
    event_type: AuditEventType = AuditEventType.API_ACCESS
    level: AuditLevel = AuditLevel.INFO
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    tenant_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    resource: Optional[str] = None
    action: Optional[str] = None
    outcome: str = "success"  # success, failure, error
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    compliance_tags: List[ComplianceStandard] = field(default_factory=list)
    sensitive_data: bool = False
    retention_days: int = 2555  # 7 years default
    checksum: Optional[str] = None
    
    def __post_init__(self):
        """Calculate checksum after initialization."""
        if not self.checksum:
            self.checksum = self._calculate_checksum()
    
    def _calculate_checksum(self) -> str:
        """Calculate event checksum for integrity verification."""
        # Create a copy without checksum for hashing
        data = asdict(self)
        data.pop('checksum', None)
        
        # Convert to JSON string
        json_str = json.dumps(data, sort_keys=True, default=str)
        
        # Calculate SHA-256 hash
        return hashlib.sha256(json_str.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify event integrity using checksum."""
        expected_checksum = self._calculate_checksum()
        return self.checksum == expected_checksum
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), default=str)


@dataclass
class AuditQuery:
    """Audit log query parameters."""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    event_types: Optional[List[AuditEventType]] = None
    levels: Optional[List[AuditLevel]] = None
    user_ids: Optional[List[str]] = None
    tenant_ids: Optional[List[str]] = None
    resources: Optional[List[str]] = None
    actions: Optional[List[str]] = None
    outcomes: Optional[List[str]] = None
    compliance_tags: Optional[List[ComplianceStandard]] = None
    limit: int = 1000
    offset: int = 0


class AuditStorage:
    """Audit log storage interface."""
    
    async def store_event(self, event: AuditEvent) -> bool:
        """Store audit event."""
        raise NotImplementedError
    
    async def query_events(self, query: AuditQuery) -> List[AuditEvent]:
        """Query audit events."""
        raise NotImplementedError
    
    async def get_event(self, event_id: str) -> Optional[AuditEvent]:
        """Get specific audit event."""
        raise NotImplementedError
    
    async def delete_expired_events(self, before_date: datetime) -> int:
        """Delete expired audit events."""
        raise NotImplementedError


class FileAuditStorage(AuditStorage):
    """File-based audit storage."""
    
    def __init__(self, storage_path: str = "audit_logs"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        # Create daily log files
        self.current_date = None
        self.current_file = None
        self.file_lock = asyncio.Lock()
    
    async def store_event(self, event: AuditEvent) -> bool:
        """Store audit event to file."""
        try:
            async with self.file_lock:
                # Get current date for file rotation
                current_date = event.timestamp.date()
                
                # Rotate file if date changed
                if self.current_date != current_date:
                    if self.current_file:
                        self.current_file.close()
                    
                    filename = f"audit_{current_date.isoformat()}.jsonl"
                    filepath = self.storage_path / filename
                    self.current_file = open(filepath, 'a', encoding='utf-8')
                    self.current_date = current_date
                
                # Write event
                self.current_file.write(event.to_json() + '\n')
                self.current_file.flush()
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to store audit event: {e}")
            return False
    
    async def query_events(self, query: AuditQuery) -> List[AuditEvent]:
        """Query audit events from files."""
        events = []
        
        try:
            # Determine date range for file scanning
            start_date = query.start_time.date() if query.start_time else datetime.min.date()
            end_date = query.end_time.date() if query.end_time else datetime.max.date()
            
            # Scan relevant files
            for filepath in self.storage_path.glob("audit_*.jsonl"):
                # Extract date from filename
                try:
                    date_str = filepath.stem.replace("audit_", "")
                    file_date = datetime.fromisoformat(date_str).date()
                    
                    if start_date <= file_date <= end_date:
                        events.extend(await self._read_events_from_file(filepath, query))
                        
                except ValueError:
                    continue
            
            # Sort by timestamp
            events.sort(key=lambda e: e.timestamp)
            
            # Apply limit and offset
            return events[query.offset:query.offset + query.limit]
            
        except Exception as e:
            logger.error(f"Failed to query audit events: {e}")
            return []
    
    async def _read_events_from_file(self, filepath: Path, query: AuditQuery) -> List[AuditEvent]:
        """Read events from a single file."""
        events = []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        event = AuditEvent(**data)
                        
                        # Apply filters
                        if self._matches_query(event, query):
                            events.append(event)
                            
                    except (json.JSONDecodeError, TypeError):
                        continue
                        
        except Exception as e:
            logger.error(f"Failed to read audit file {filepath}: {e}")
        
        return events
    
    def _matches_query(self, event: AuditEvent, query: AuditQuery) -> bool:
        """Check if event matches query criteria."""
        # Time range
        if query.start_time and event.timestamp < query.start_time:
            return False
        if query.end_time and event.timestamp > query.end_time:
            return False
        
        # Event types
        if query.event_types and event.event_type not in query.event_types:
            return False
        
        # Levels
        if query.levels and event.level not in query.levels:
            return False
        
        # User IDs
        if query.user_ids and event.user_id not in query.user_ids:
            return False
        
        # Tenant IDs
        if query.tenant_ids and event.tenant_id not in query.tenant_ids:
            return False
        
        # Resources
        if query.resources and event.resource not in query.resources:
            return False
        
        # Actions
        if query.actions and event.action not in query.actions:
            return False
        
        # Outcomes
        if query.outcomes and event.outcome not in query.outcomes:
            return False
        
        # Compliance tags
        if query.compliance_tags:
            if not any(tag in event.compliance_tags for tag in query.compliance_tags):
                return False
        
        return True
    
    async def get_event(self, event_id: str) -> Optional[AuditEvent]:
        """Get specific audit event."""
        # This is inefficient for file storage, but works for small datasets
        query = AuditQuery(limit=10000)  # Large limit to search all
        events = await self.query_events(query)
        
        for event in events:
            if event.event_id == event_id:
                return event
        
        return None
    
    async def delete_expired_events(self, before_date: datetime) -> int:
        """Delete expired audit events."""
        deleted_count = 0
        
        try:
            # Find files older than retention date
            for filepath in self.storage_path.glob("audit_*.jsonl"):
                try:
                    date_str = filepath.stem.replace("audit_", "")
                    file_date = datetime.fromisoformat(date_str)
                    
                    if file_date < before_date:
                        # Compress before deletion for archival
                        await self._compress_file(filepath)
                        filepath.unlink()
                        deleted_count += 1
                        logger.info(f"Deleted expired audit file: {filepath}")
                        
                except ValueError:
                    continue
                    
        except Exception as e:
            logger.error(f"Failed to delete expired audit events: {e}")
        
        return deleted_count
    
    async def _compress_file(self, filepath: Path):
        """Compress audit file for archival."""
        try:
            compressed_path = filepath.with_suffix('.jsonl.gz')
            
            with open(filepath, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.writelines(f_in)
                    
            logger.info(f"Compressed audit file: {filepath} -> {compressed_path}")
            
        except Exception as e:
            logger.error(f"Failed to compress audit file {filepath}: {e}")


class AuditLogger:
    """Comprehensive audit logging system."""
    
    def __init__(self, storage: Optional[AuditStorage] = None):
        # Storage backend
        self.storage = storage or FileAuditStorage()
        
        # Event queue for async processing
        self.event_queue: asyncio.Queue = asyncio.Queue(maxsize=10000)
        self.processing_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.events_logged = 0
        self.events_failed = 0
        
        # Compliance configurations
        self.compliance_configs: Dict[ComplianceStandard, Dict[str, Any]] = {
            ComplianceStandard.GDPR: {
                "retention_days": 2555,  # 7 years
                "required_fields": ["user_id", "action", "timestamp"],
                "sensitive_data_handling": True
            },
            ComplianceStandard.HIPAA: {
                "retention_days": 2190,  # 6 years
                "required_fields": ["user_id", "action", "timestamp", "resource"],
                "sensitive_data_handling": True
            },
            ComplianceStandard.SOX: {
                "retention_days": 2555,  # 7 years
                "required_fields": ["user_id", "action", "timestamp"],
                "sensitive_data_handling": False
            }
        }
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self._start_background_tasks()
        
        logger.info("Audit logger initialized")
    
    def _start_background_tasks(self):
        """Start background tasks."""
        self.processing_task = asyncio.create_task(self._process_events())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def log_event(
        self,
        event_type: AuditEventType,
        message: str,
        level: AuditLevel = AuditLevel.INFO,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        outcome: str = "success",
        details: Optional[Dict[str, Any]] = None,
        compliance_tags: Optional[List[ComplianceStandard]] = None,
        sensitive_data: bool = False
    ) -> str:
        """Log audit event."""
        # Create audit event
        event = AuditEvent(
            event_type=event_type,
            level=level,
            user_id=user_id,
            session_id=session_id,
            tenant_id=tenant_id,
            ip_address=ip_address,
            user_agent=user_agent,
            resource=resource,
            action=action,
            outcome=outcome,
            message=message,
            details=details or {},
            compliance_tags=compliance_tags or [],
            sensitive_data=sensitive_data
        )
        
        # Apply compliance configurations
        self._apply_compliance_config(event)
        
        # Queue event for processing
        try:
            await self.event_queue.put(event)
            self.events_logged += 1
            
            # Record metrics
            metrics_collector.record_counter(
                "audit_events_total",
                1.0,
                {
                    "event_type": event_type.value,
                    "level": level.value,
                    "outcome": outcome
                }
            )
            
            return event.event_id
            
        except asyncio.QueueFull:
            self.events_failed += 1
            logger.error("Audit event queue full, dropping event")
            return ""
    
    def _apply_compliance_config(self, event: AuditEvent):
        """Apply compliance configurations to event."""
        for compliance_tag in event.compliance_tags:
            config = self.compliance_configs.get(compliance_tag)
            if config:
                # Set retention period
                event.retention_days = config["retention_days"]
                
                # Validate required fields
                required_fields = config["required_fields"]
                for field in required_fields:
                    if not getattr(event, field, None):
                        logger.warning(f"Missing required field '{field}' for {compliance_tag.value} compliance")
    
    async def _process_events(self):
        """Background task to process audit events."""
        while True:
            try:
                # Get event from queue
                event = await self.event_queue.get()
                
                # Store event
                success = await self.storage.store_event(event)
                
                if not success:
                    self.events_failed += 1
                    logger.error(f"Failed to store audit event: {event.event_id}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.events_failed += 1
                logger.error(f"Error processing audit event: {e}")
    
    async def query_events(self, query: AuditQuery) -> List[AuditEvent]:
        """Query audit events."""
        return await self.storage.query_events(query)
    
    async def get_event(self, event_id: str) -> Optional[AuditEvent]:
        """Get specific audit event."""
        return await self.storage.get_event(event_id)
    
    async def generate_compliance_report(
        self,
        compliance_standard: ComplianceStandard,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """Generate compliance report."""
        query = AuditQuery(
            start_time=start_time,
            end_time=end_time,
            compliance_tags=[compliance_standard],
            limit=10000
        )
        
        events = await self.query_events(query)
        
        # Generate report
        report = {
            "compliance_standard": compliance_standard.value,
            "report_period": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat()
            },
            "total_events": len(events),
            "events_by_type": {},
            "events_by_level": {},
            "events_by_outcome": {},
            "integrity_check": {
                "total_events": len(events),
                "verified_events": 0,
                "failed_verification": 0
            },
            "compliance_violations": []
        }
        
        # Analyze events
        for event in events:
            # Count by type
            event_type = event.event_type.value
            report["events_by_type"][event_type] = report["events_by_type"].get(event_type, 0) + 1
            
            # Count by level
            level = event.level.value
            report["events_by_level"][level] = report["events_by_level"].get(level, 0) + 1
            
            # Count by outcome
            outcome = event.outcome
            report["events_by_outcome"][outcome] = report["events_by_outcome"].get(outcome, 0) + 1
            
            # Verify integrity
            if event.verify_integrity():
                report["integrity_check"]["verified_events"] += 1
            else:
                report["integrity_check"]["failed_verification"] += 1
                report["compliance_violations"].append({
                    "event_id": event.event_id,
                    "violation_type": "integrity_failure",
                    "timestamp": event.timestamp.isoformat()
                })
        
        return report
    
    def get_audit_stats(self) -> Dict[str, Any]:
        """Get audit logging statistics."""
        return {
            "events_logged": self.events_logged,
            "events_failed": self.events_failed,
            "success_rate": self.events_logged / max(1, self.events_logged + self.events_failed),
            "queue_size": self.event_queue.qsize(),
            "queue_max_size": self.event_queue.maxsize,
            "supported_compliance_standards": [std.value for std in ComplianceStandard]
        }
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(86400)  # Daily cleanup
                
                # Delete expired events based on retention policies
                current_time = datetime.utcnow()
                
                # Find the shortest retention period
                min_retention_days = min(
                    config["retention_days"] 
                    for config in self.compliance_configs.values()
                )
                
                # Delete events older than minimum retention
                cutoff_date = current_time - timedelta(days=min_retention_days)
                deleted_count = await self.storage.delete_expired_events(cutoff_date)
                
                if deleted_count > 0:
                    logger.info(f"Deleted {deleted_count} expired audit log files")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Audit cleanup error: {e}")
    
    async def shutdown(self):
        """Shutdown audit logger."""
        # Cancel background tasks
        if self.processing_task:
            self.processing_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Process remaining events
        while not self.event_queue.empty():
            try:
                event = self.event_queue.get_nowait()
                await self.storage.store_event(event)
            except asyncio.QueueEmpty:
                break
            except Exception as e:
                logger.error(f"Error processing final audit event: {e}")
        
        logger.info("Audit logger shutdown completed")


# Global audit logger instance
audit_logger = AuditLogger()
