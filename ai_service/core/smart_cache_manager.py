"""
Smart Cache Manager for Advanced Context Caching.
Implements intelligent caching strategies with performance optimization.
"""

import asyncio
import hashlib
import json
import logging
import time
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import psutil
import threading
from collections import defaultdict, OrderedDict

logger = logging.getLogger(__name__)


class CacheStrategy(Enum):
    """Cache eviction strategies."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    ADAPTIVE = "adaptive"  # AI-driven adaptive strategy
    SEMANTIC = "semantic"  # Semantic similarity-based


class CacheTier(Enum):
    """Cache tier levels."""
    HOT = "hot"      # Frequently accessed, in-memory
    WARM = "warm"    # Moderately accessed, compressed
    COLD = "cold"    # Rarely accessed, disk-based


@dataclass
class CacheEntry:
    """Enhanced cache entry with metadata."""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int
    size_bytes: int
    tier: CacheTier = CacheTier.HOT
    ttl: Optional[float] = None
    semantic_hash: Optional[str] = None
    compression_ratio: float = 1.0
    
    @property
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    @property
    def age_seconds(self) -> float:
        """Get entry age in seconds."""
        return time.time() - self.created_at
    
    @property
    def idle_seconds(self) -> float:
        """Get idle time in seconds."""
        return time.time() - self.last_accessed


@dataclass
class CacheStats:
    """Cache performance statistics."""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size_bytes: int = 0
    entry_count: int = 0
    hot_tier_count: int = 0
    warm_tier_count: int = 0
    cold_tier_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def size_mb(self) -> float:
        """Get cache size in MB."""
        return self.size_bytes / (1024 * 1024)


class SmartCacheManager:
    """Advanced cache manager with intelligent strategies."""
    
    def __init__(
        self,
        max_size_mb: int = 1024,
        strategy: CacheStrategy = CacheStrategy.ADAPTIVE,
        default_ttl: int = 3600,
        enable_compression: bool = True,
        enable_semantic_caching: bool = True
    ):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.strategy = strategy
        self.default_ttl = default_ttl
        self.enable_compression = enable_compression
        self.enable_semantic_caching = enable_semantic_caching
        
        # Cache storage by tier
        self.hot_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.warm_cache: Dict[str, CacheEntry] = {}
        self.cold_cache: Dict[str, CacheEntry] = {}
        
        # Statistics
        self.stats = CacheStats()
        
        # Semantic similarity cache
        self.semantic_index: Dict[str, List[str]] = defaultdict(list)
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._stats_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Performance monitoring
        self.performance_history: List[Dict[str, Any]] = []
        
        logger.info(f"Smart Cache Manager initialized with {max_size_mb}MB capacity")
    
    async def start(self):
        """Start background cache management tasks."""
        if self._running:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._background_cleanup())
        self._stats_task = asyncio.create_task(self._background_stats_collection())
        
        logger.info("Smart Cache Manager background tasks started")
    
    async def stop(self):
        """Stop background tasks and cleanup."""
        self._running = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        if self._stats_task:
            self._stats_task.cancel()
            try:
                await self._stats_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Smart Cache Manager stopped")
    
    def _generate_cache_key(self, prompt: str, model: str, **kwargs) -> str:
        """Generate cache key from request parameters."""
        # Create deterministic key from parameters
        key_data = {
            "prompt": prompt,
            "model": model,
            **{k: v for k, v in sorted(kwargs.items()) if v is not None}
        }
        
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()
    
    def _generate_semantic_hash(self, prompt: str) -> str:
        """Generate semantic hash for similarity matching."""
        # Simple semantic hash based on normalized prompt
        normalized = prompt.lower().strip()
        words = sorted(set(normalized.split()))
        semantic_content = " ".join(words[:20])  # Use first 20 unique words
        return hashlib.md5(semantic_content.encode()).hexdigest()[:16]
    
    async def get(self, key: str, semantic_search: bool = True) -> Optional[Any]:
        """Get value from cache with semantic search support."""
        # Direct key lookup
        entry = self._find_entry(key)
        if entry:
            if entry.is_expired:
                await self._remove_entry(key)
                self.stats.misses += 1
                return None
            
            # Update access statistics
            entry.last_accessed = time.time()
            entry.access_count += 1
            
            # Promote to hot tier if frequently accessed
            if entry.tier != CacheTier.HOT and entry.access_count > 5:
                await self._promote_to_hot(entry)
            
            self.stats.hits += 1
            return entry.value
        
        # Semantic search if enabled
        if semantic_search and self.enable_semantic_caching:
            semantic_result = await self._semantic_search(key)
            if semantic_result:
                self.stats.hits += 1
                return semantic_result
        
        self.stats.misses += 1
        return None
    
    async def put(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tier: CacheTier = CacheTier.HOT
    ) -> bool:
        """Put value in cache with intelligent placement."""
        try:
            # Calculate size
            size_bytes = self._calculate_size(value)
            
            # Check if we need to make space
            while self._get_total_size() + size_bytes > self.max_size_bytes:
                if not await self._evict_entry():
                    logger.warning("Cannot evict enough space for new entry")
                    return False
            
            # Remove existing entry if present
            if self._find_entry(key):
                await self._remove_entry(key)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                last_accessed=time.time(),
                access_count=1,
                size_bytes=size_bytes,
                tier=tier,
                ttl=ttl or self.default_ttl
            )
            
            # Add semantic hash if enabled
            if self.enable_semantic_caching and isinstance(value, str):
                entry.semantic_hash = self._generate_semantic_hash(value)
                self.semantic_index[entry.semantic_hash].append(key)
            
            # Compress if enabled and appropriate
            if self.enable_compression and tier != CacheTier.HOT:
                entry = await self._compress_entry(entry)
            
            # Store in appropriate tier
            await self._store_entry(entry)
            
            # Update statistics
            self.stats.entry_count += 1
            self.stats.size_bytes += entry.size_bytes
            self._update_tier_counts()
            
            return True
            
        except Exception as e:
            logger.error(f"Error storing cache entry: {e}")
            return False
    
    def _find_entry(self, key: str) -> Optional[CacheEntry]:
        """Find entry across all tiers."""
        if key in self.hot_cache:
            return self.hot_cache[key]
        elif key in self.warm_cache:
            return self.warm_cache[key]
        elif key in self.cold_cache:
            return self.cold_cache[key]
        return None
    
    async def _store_entry(self, entry: CacheEntry):
        """Store entry in appropriate tier."""
        if entry.tier == CacheTier.HOT:
            self.hot_cache[entry.key] = entry
        elif entry.tier == CacheTier.WARM:
            self.warm_cache[entry.key] = entry
        else:  # COLD
            self.cold_cache[entry.key] = entry
    
    async def _remove_entry(self, key: str) -> bool:
        """Remove entry from cache."""
        entry = self._find_entry(key)
        if not entry:
            return False
        
        # Remove from appropriate tier
        if entry.tier == CacheTier.HOT and key in self.hot_cache:
            del self.hot_cache[key]
        elif entry.tier == CacheTier.WARM and key in self.warm_cache:
            del self.warm_cache[key]
        elif entry.tier == CacheTier.COLD and key in self.cold_cache:
            del self.cold_cache[key]
        
        # Remove from semantic index
        if entry.semantic_hash and entry.semantic_hash in self.semantic_index:
            if key in self.semantic_index[entry.semantic_hash]:
                self.semantic_index[entry.semantic_hash].remove(key)
                if not self.semantic_index[entry.semantic_hash]:
                    del self.semantic_index[entry.semantic_hash]
        
        # Update statistics
        self.stats.entry_count -= 1
        self.stats.size_bytes -= entry.size_bytes
        self._update_tier_counts()
        
        return True
    
    async def _evict_entry(self) -> bool:
        """Evict entry based on strategy."""
        if self.strategy == CacheStrategy.LRU:
            return await self._evict_lru()
        elif self.strategy == CacheStrategy.LFU:
            return await self._evict_lfu()
        elif self.strategy == CacheStrategy.TTL:
            return await self._evict_expired()
        elif self.strategy == CacheStrategy.ADAPTIVE:
            return await self._evict_adaptive()
        else:
            return await self._evict_lru()  # Default fallback
    
    async def _evict_lru(self) -> bool:
        """Evict least recently used entry."""
        oldest_entry = None
        oldest_time = float('inf')
        
        for cache in [self.cold_cache, self.warm_cache, self.hot_cache]:
            for entry in cache.values():
                if entry.last_accessed < oldest_time:
                    oldest_time = entry.last_accessed
                    oldest_entry = entry
        
        if oldest_entry:
            await self._remove_entry(oldest_entry.key)
            self.stats.evictions += 1
            return True
        
        return False
    
    async def _evict_adaptive(self) -> bool:
        """Adaptive eviction based on multiple factors."""
        # Score entries based on multiple factors
        candidates = []
        current_time = time.time()
        
        for cache in [self.cold_cache, self.warm_cache, self.hot_cache]:
            for entry in cache.values():
                # Calculate composite score
                recency_score = 1.0 / (current_time - entry.last_accessed + 1)
                frequency_score = entry.access_count
                size_penalty = entry.size_bytes / (1024 * 1024)  # MB
                age_penalty = entry.age_seconds / 3600  # Hours
                
                # Lower score = better candidate for eviction
                score = (recency_score * frequency_score) / (size_penalty + age_penalty + 1)
                candidates.append((score, entry))
        
        if candidates:
            # Sort by score (lowest first)
            candidates.sort(key=lambda x: x[0])
            victim = candidates[0][1]
            await self._remove_entry(victim.key)
            self.stats.evictions += 1
            return True
        
        return False
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value in bytes."""
        import sys
        return sys.getsizeof(value)
    
    def _get_total_size(self) -> int:
        """Get total cache size across all tiers."""
        return self.stats.size_bytes
    
    def _update_tier_counts(self):
        """Update tier statistics."""
        self.stats.hot_tier_count = len(self.hot_cache)
        self.stats.warm_tier_count = len(self.warm_cache)
        self.stats.cold_tier_count = len(self.cold_cache)
    
    async def _background_cleanup(self):
        """Background task for cache maintenance."""
        while self._running:
            try:
                # Clean expired entries
                await self._cleanup_expired()
                
                # Rebalance tiers
                await self._rebalance_tiers()
                
                # Collect performance metrics
                await self._collect_performance_metrics()
                
                # Sleep for 60 seconds
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background cleanup: {e}")
                await asyncio.sleep(60)
    
    async def _background_stats_collection(self):
        """Background task for statistics collection."""
        while self._running:
            try:
                # Collect system metrics
                system_stats = {
                    'timestamp': time.time(),
                    'memory_usage': psutil.virtual_memory().percent,
                    'cpu_usage': psutil.cpu_percent(),
                    'cache_hit_rate': self.stats.hit_rate,
                    'cache_size_mb': self.stats.size_mb,
                    'entry_count': self.stats.entry_count
                }
                
                self.performance_history.append(system_stats)
                
                # Keep only last 24 hours of data
                cutoff_time = time.time() - 86400
                self.performance_history = [
                    stat for stat in self.performance_history
                    if stat['timestamp'] > cutoff_time
                ]
                
                await asyncio.sleep(300)  # Collect every 5 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in stats collection: {e}")
                await asyncio.sleep(300)


    async def _cleanup_expired(self):
        """Clean up expired entries."""
        expired_keys = []

        for cache in [self.hot_cache, self.warm_cache, self.cold_cache]:
            for key, entry in cache.items():
                if entry.is_expired:
                    expired_keys.append(key)

        for key in expired_keys:
            await self._remove_entry(key)

        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

    async def _rebalance_tiers(self):
        """Rebalance entries between cache tiers."""
        # Promote frequently accessed warm/cold entries to hot
        promotion_candidates = []

        for cache, tier in [(self.warm_cache, CacheTier.WARM), (self.cold_cache, CacheTier.COLD)]:
            for entry in cache.values():
                if entry.access_count > 10:  # Frequently accessed
                    promotion_candidates.append(entry)

        for entry in promotion_candidates[:10]:  # Limit promotions
            await self._promote_to_hot(entry)

        # Demote old hot entries to warm
        if len(self.hot_cache) > 100:  # Hot cache size limit
            old_entries = sorted(
                self.hot_cache.values(),
                key=lambda e: e.last_accessed
            )[:20]  # Demote oldest 20

            for entry in old_entries:
                await self._demote_to_warm(entry)

    async def _promote_to_hot(self, entry: CacheEntry):
        """Promote entry to hot tier."""
        if entry.tier == CacheTier.HOT:
            return

        # Remove from current tier
        if entry.tier == CacheTier.WARM and entry.key in self.warm_cache:
            del self.warm_cache[entry.key]
        elif entry.tier == CacheTier.COLD and entry.key in self.cold_cache:
            del self.cold_cache[entry.key]

        # Decompress if needed
        if hasattr(entry, 'compressed') and entry.compressed:
            entry = await self._decompress_entry(entry)

        # Add to hot tier
        entry.tier = CacheTier.HOT
        self.hot_cache[entry.key] = entry
        self._update_tier_counts()

    async def _demote_to_warm(self, entry: CacheEntry):
        """Demote entry to warm tier."""
        if entry.tier != CacheTier.HOT:
            return

        # Remove from hot tier
        if entry.key in self.hot_cache:
            del self.hot_cache[entry.key]

        # Compress if enabled
        if self.enable_compression:
            entry = await self._compress_entry(entry)

        # Add to warm tier
        entry.tier = CacheTier.WARM
        self.warm_cache[entry.key] = entry
        self._update_tier_counts()

    async def _compress_entry(self, entry: CacheEntry) -> CacheEntry:
        """Compress cache entry to save memory."""
        try:
            import gzip
            import pickle

            # Serialize and compress value
            serialized = pickle.dumps(entry.value)
            compressed = gzip.compress(serialized)

            # Calculate compression ratio
            original_size = len(serialized)
            compressed_size = len(compressed)
            compression_ratio = compressed_size / original_size if original_size > 0 else 1.0

            # Update entry
            entry.value = compressed
            entry.size_bytes = compressed_size
            entry.compression_ratio = compression_ratio
            entry.compressed = True

            return entry

        except Exception as e:
            logger.warning(f"Failed to compress cache entry: {e}")
            return entry

    async def _decompress_entry(self, entry: CacheEntry) -> CacheEntry:
        """Decompress cache entry."""
        try:
            import gzip
            import pickle

            if hasattr(entry, 'compressed') and entry.compressed:
                # Decompress and deserialize
                decompressed = gzip.decompress(entry.value)
                entry.value = pickle.loads(decompressed)
                entry.size_bytes = len(decompressed)
                entry.compressed = False

            return entry

        except Exception as e:
            logger.warning(f"Failed to decompress cache entry: {e}")
            return entry

    async def _semantic_search(self, key: str) -> Optional[Any]:
        """Search for semantically similar cached content."""
        # This is a simplified semantic search
        # In production, you might use embeddings or more sophisticated NLP
        return None

    async def _collect_performance_metrics(self):
        """Collect detailed performance metrics."""
        metrics = {
            'timestamp': time.time(),
            'hit_rate': self.stats.hit_rate,
            'total_size_mb': self.stats.size_mb,
            'entry_count': self.stats.entry_count,
            'hot_tier_count': self.stats.hot_tier_count,
            'warm_tier_count': self.stats.warm_tier_count,
            'cold_tier_count': self.stats.cold_tier_count,
            'evictions': self.stats.evictions,
            'memory_efficiency': self._calculate_memory_efficiency()
        }

        # Store in performance history
        self.performance_history.append(metrics)

    def _calculate_memory_efficiency(self) -> float:
        """Calculate memory efficiency score."""
        if self.stats.entry_count == 0:
            return 0.0

        # Consider hit rate, compression ratio, and tier distribution
        hit_rate_score = self.stats.hit_rate

        # Calculate average compression ratio
        total_compression = 0.0
        compressed_entries = 0

        for cache in [self.warm_cache, self.cold_cache]:
            for entry in cache.values():
                if hasattr(entry, 'compression_ratio'):
                    total_compression += entry.compression_ratio
                    compressed_entries += 1

        compression_score = (total_compression / compressed_entries) if compressed_entries > 0 else 1.0

        # Tier distribution score (prefer hot tier for frequently accessed)
        tier_score = (self.stats.hot_tier_count * 1.0 +
                     self.stats.warm_tier_count * 0.7 +
                     self.stats.cold_tier_count * 0.4) / self.stats.entry_count

        return (hit_rate_score * 0.5 + compression_score * 0.3 + tier_score * 0.2)

    async def get_analytics(self) -> Dict[str, Any]:
        """Get comprehensive cache analytics."""
        return {
            'stats': {
                'hits': self.stats.hits,
                'misses': self.stats.misses,
                'hit_rate': self.stats.hit_rate,
                'evictions': self.stats.evictions,
                'total_size_mb': self.stats.size_mb,
                'entry_count': self.stats.entry_count
            },
            'tier_distribution': {
                'hot': self.stats.hot_tier_count,
                'warm': self.stats.warm_tier_count,
                'cold': self.stats.cold_tier_count
            },
            'performance': {
                'memory_efficiency': self._calculate_memory_efficiency(),
                'average_entry_size_kb': (self.stats.size_bytes / self.stats.entry_count / 1024) if self.stats.entry_count > 0 else 0,
                'cache_utilization': (self.stats.size_bytes / self.max_size_bytes) * 100
            },
            'recent_performance': self.performance_history[-10:] if self.performance_history else []
        }

    async def clear_cache(self, tier: Optional[CacheTier] = None):
        """Clear cache entries."""
        if tier is None:
            # Clear all tiers
            self.hot_cache.clear()
            self.warm_cache.clear()
            self.cold_cache.clear()
            self.semantic_index.clear()
        elif tier == CacheTier.HOT:
            self.hot_cache.clear()
        elif tier == CacheTier.WARM:
            self.warm_cache.clear()
        elif tier == CacheTier.COLD:
            self.cold_cache.clear()

        # Reset statistics
        self.stats = CacheStats()
        logger.info(f"Cache cleared: {tier.value if tier else 'all tiers'}")

    async def evict_expired(self) -> int:
        """Manually evict expired entries and return count."""
        expired_keys = []

        for cache in [self.hot_cache, self.warm_cache, self.cold_cache]:
            for key, entry in cache.items():
                if entry.is_expired:
                    expired_keys.append(key)

        for key in expired_keys:
            await self._remove_entry(key)

        return len(expired_keys)


# Global smart cache manager instance
smart_cache_manager = SmartCacheManager()
