"""
Enhanced Authentication & Authorization Manager for Enterprise Security.
Implements JWT-based authentication, RBAC, API key management, and advanced security features.
"""

import asyncio
import hashlib
import hmac
import json
import logging
import secrets
import time
from typing import Dict, List, Optional, Set, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import jwt
from passlib.context import CryptContext

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """User roles for RBAC."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    DEVELOPER = "developer"
    USER = "user"
    VIEWER = "viewer"
    API_CLIENT = "api_client"
    GUEST = "guest"


class Permission(Enum):
    """System permissions with granular control."""
    # AI Operations
    AI_GENERATE_TEXT = "ai:generate:text"
    AI_GENERATE_IMAGE = "ai:generate:image"
    AI_MULTIMODAL = "ai:multimodal"
    AI_FUNCTION_CALLING = "ai:function_calling"
    AI_EMBEDDINGS = "ai:embeddings"
    AI_TTS = "ai:tts"
    
    # Advanced Features
    AI_STRUCTURED_OUTPUT = "ai:structured_output"
    AI_SCHEMA_VALIDATION = "ai:schema_validation"
    AI_TEMPLATE_RENDERING = "ai:template_rendering"
    AI_BATCH_PROCESSING = "ai:batch_processing"
    
    # Cache Management
    CACHE_READ = "cache:read"
    CACHE_WRITE = "cache:write"
    CACHE_DELETE = "cache:delete"
    CACHE_ADMIN = "cache:admin"
    
    # System Management
    SYSTEM_HEALTH = "system:health"
    SYSTEM_METRICS = "system:metrics"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_ADMIN = "system:admin"
    
    # User Management
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    USER_ADMIN = "user:admin"
    
    # API Management
    API_KEY_CREATE = "api:key:create"
    API_KEY_READ = "api:key:read"
    API_KEY_REVOKE = "api:key:revoke"
    API_KEY_ADMIN = "api:key:admin"
    
    # Monitoring & Analytics
    ANALYTICS_READ = "analytics:read"
    ANALYTICS_ADMIN = "analytics:admin"
    LOGS_READ = "logs:read"
    LOGS_ADMIN = "logs:admin"


class AuthenticationMethod(Enum):
    """Authentication methods."""
    PASSWORD = "password"
    API_KEY = "api_key"
    JWT_TOKEN = "jwt_token"
    OAUTH = "oauth"
    SSO = "sso"


@dataclass
class SecurityPolicy:
    """Security policy configuration."""
    password_min_length: int = 8
    password_require_uppercase: bool = True
    password_require_lowercase: bool = True
    password_require_numbers: bool = True
    password_require_symbols: bool = True
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30
    token_expiry_hours: int = 24
    api_key_expiry_days: int = 365
    require_2fa: bool = False
    session_timeout_minutes: int = 60


@dataclass
class User:
    """Enhanced user account information."""
    user_id: str
    username: str
    email: str
    roles: Set[UserRole] = field(default_factory=set)
    permissions: Set[Permission] = field(default_factory=set)
    is_active: bool = True
    is_verified: bool = False
    created_at: float = field(default_factory=time.time)
    last_login: Optional[float] = None
    login_attempts: int = 0
    locked_until: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if user has specific permission."""
        return permission in self.permissions
    
    def has_role(self, role: UserRole) -> bool:
        """Check if user has specific role."""
        return role in self.roles
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if self.locked_until is None:
            return False
        return time.time() < self.locked_until


@dataclass
class APIKey:
    """Enhanced API key for programmatic access."""
    key_id: str
    key_hash: str
    name: str
    user_id: str
    permissions: Set[Permission] = field(default_factory=set)
    is_active: bool = True
    created_at: float = field(default_factory=time.time)
    last_used: Optional[float] = None
    expires_at: Optional[float] = None
    rate_limit_per_minute: Optional[int] = None
    rate_limit_per_hour: Optional[int] = None
    allowed_ips: Optional[List[str]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_expired(self) -> bool:
        """Check if API key is expired."""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at


@dataclass
class AuthSession:
    """Authentication session."""
    session_id: str
    user_id: str
    token: str
    created_at: float
    expires_at: float
    last_activity: float
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    permissions: Set[Permission] = field(default_factory=set)
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return time.time() > self.expires_at
    
    @property
    def is_idle(self, timeout_minutes: int = 60) -> bool:
        """Check if session is idle."""
        return time.time() - self.last_activity > (timeout_minutes * 60)


class EnhancedAuthManager:
    """Enhanced authentication and authorization manager."""
    
    def __init__(self, secret_key: str = None, security_policy: SecurityPolicy = None):
        self.secret_key = secret_key or secrets.token_urlsafe(32)
        self.algorithm = "HS256"
        self.security_policy = security_policy or SecurityPolicy()
        
        # Password hashing
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Storage
        self.users: Dict[str, User] = {}
        self.api_keys: Dict[str, APIKey] = {}
        self.sessions: Dict[str, AuthSession] = {}
        
        # Security tracking
        self.failed_attempts: Dict[str, List[float]] = {}
        self.rate_limits: Dict[str, List[float]] = {}
        
        # Role permissions mapping
        self.role_permissions = self._initialize_role_permissions()
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Initialize default admin user
        asyncio.create_task(self._create_default_admin())
        
        logger.info("Enhanced Authentication Manager initialized")
    
    def _initialize_role_permissions(self) -> Dict[UserRole, Set[Permission]]:
        """Initialize comprehensive role permissions."""
        return {
            UserRole.SUPER_ADMIN: set(Permission),  # All permissions
            
            UserRole.ADMIN: {
                # AI Operations
                Permission.AI_GENERATE_TEXT, Permission.AI_GENERATE_IMAGE,
                Permission.AI_MULTIMODAL, Permission.AI_FUNCTION_CALLING,
                Permission.AI_EMBEDDINGS, Permission.AI_TTS,
                Permission.AI_STRUCTURED_OUTPUT, Permission.AI_SCHEMA_VALIDATION,
                Permission.AI_TEMPLATE_RENDERING, Permission.AI_BATCH_PROCESSING,
                
                # Cache Management
                Permission.CACHE_READ, Permission.CACHE_WRITE,
                Permission.CACHE_DELETE, Permission.CACHE_ADMIN,
                
                # System Management
                Permission.SYSTEM_HEALTH, Permission.SYSTEM_METRICS,
                Permission.SYSTEM_CONFIG, Permission.SYSTEM_ADMIN,
                
                # User Management
                Permission.USER_READ, Permission.USER_WRITE, Permission.USER_ADMIN,
                
                # API Management
                Permission.API_KEY_CREATE, Permission.API_KEY_READ,
                Permission.API_KEY_REVOKE, Permission.API_KEY_ADMIN,
                
                # Analytics
                Permission.ANALYTICS_READ, Permission.ANALYTICS_ADMIN,
                Permission.LOGS_READ, Permission.LOGS_ADMIN
            },
            
            UserRole.DEVELOPER: {
                # AI Operations
                Permission.AI_GENERATE_TEXT, Permission.AI_GENERATE_IMAGE,
                Permission.AI_MULTIMODAL, Permission.AI_FUNCTION_CALLING,
                Permission.AI_EMBEDDINGS, Permission.AI_TTS,
                Permission.AI_STRUCTURED_OUTPUT, Permission.AI_SCHEMA_VALIDATION,
                Permission.AI_TEMPLATE_RENDERING, Permission.AI_BATCH_PROCESSING,
                
                # Cache Management
                Permission.CACHE_READ, Permission.CACHE_WRITE,
                
                # System Monitoring
                Permission.SYSTEM_HEALTH, Permission.SYSTEM_METRICS,
                
                # API Management
                Permission.API_KEY_CREATE, Permission.API_KEY_READ,
                
                # Analytics
                Permission.ANALYTICS_READ, Permission.LOGS_READ
            },
            
            UserRole.USER: {
                # Basic AI Operations
                Permission.AI_GENERATE_TEXT, Permission.AI_GENERATE_IMAGE,
                Permission.AI_MULTIMODAL, Permission.AI_EMBEDDINGS,
                Permission.AI_TTS, Permission.AI_STRUCTURED_OUTPUT,
                
                # Basic Cache
                Permission.CACHE_READ,
                
                # Basic System
                Permission.SYSTEM_HEALTH
            },
            
            UserRole.VIEWER: {
                # Read-only access
                Permission.SYSTEM_HEALTH, Permission.SYSTEM_METRICS,
                Permission.ANALYTICS_READ, Permission.LOGS_READ
            },
            
            UserRole.API_CLIENT: {
                # API-specific permissions
                Permission.AI_GENERATE_TEXT, Permission.AI_GENERATE_IMAGE,
                Permission.AI_MULTIMODAL, Permission.AI_FUNCTION_CALLING,
                Permission.AI_EMBEDDINGS, Permission.AI_STRUCTURED_OUTPUT,
                Permission.CACHE_READ
            },
            
            UserRole.GUEST: {
                # Minimal permissions
                Permission.SYSTEM_HEALTH
            }
        }
    
    async def start(self):
        """Start background management tasks."""
        if self._running:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._background_cleanup())
        
        logger.info("Enhanced Auth Manager background tasks started")
    
    async def stop(self):
        """Stop background tasks."""
        self._running = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Enhanced Auth Manager stopped")
    
    async def _create_default_admin(self):
        """Create default admin user if none exists."""
        # Check if any admin users exist
        admin_exists = any(
            UserRole.SUPER_ADMIN in user.roles or UserRole.ADMIN in user.roles
            for user in self.users.values()
        )
        
        if not admin_exists:
            await self.create_user(
                username="admin",
                email="admin@localhost",
                password="admin123!",
                roles=[UserRole.SUPER_ADMIN]
            )
            logger.info("Created default admin user (username: admin, password: admin123!)")
    
    async def create_user(
        self,
        username: str,
        email: str,
        password: str,
        roles: List[UserRole] = None
    ) -> User:
        """Create new user account with enhanced security."""
        # Validate password
        if not self._validate_password(password):
            raise ValueError("Password does not meet security requirements")
        
        # Check if username/email already exists
        for user in self.users.values():
            if user.username == username:
                raise ValueError("Username already exists")
            if user.email == email:
                raise ValueError("Email already exists")
        
        user_id = secrets.token_urlsafe(16)
        
        # Hash password
        password_hash = self.pwd_context.hash(password)
        
        # Assign default role if none provided
        if roles is None:
            roles = [UserRole.USER]
        
        # Calculate permissions from roles
        permissions = set()
        for role in roles:
            permissions.update(self.role_permissions.get(role, set()))
        
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            roles=set(roles),
            permissions=permissions,
            metadata={"password_hash": password_hash}
        )
        
        self.users[user_id] = user
        logger.info(f"Created user: {username} ({user_id}) with roles: {[r.value for r in roles]}")
        
        return user
    
    def _validate_password(self, password: str) -> bool:
        """Validate password against security policy."""
        policy = self.security_policy
        
        if len(password) < policy.password_min_length:
            return False
        
        if policy.password_require_uppercase and not any(c.isupper() for c in password):
            return False
        
        if policy.password_require_lowercase and not any(c.islower() for c in password):
            return False
        
        if policy.password_require_numbers and not any(c.isdigit() for c in password):
            return False
        
        if policy.password_require_symbols and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            return False
        
        return True
    
    async def authenticate_user(
        self,
        username: str,
        password: str,
        ip_address: Optional[str] = None
    ) -> Optional[AuthSession]:
        """Authenticate user with enhanced security checks."""
        # Find user by username
        user = None
        for u in self.users.values():
            if u.username == username:
                user = u
                break
        
        if not user:
            await self._record_failed_attempt(username, ip_address)
            return None
        
        # Check if account is locked
        if user.is_locked:
            logger.warning(f"Authentication attempt on locked account: {username}")
            return None
        
        # Check if account is active
        if not user.is_active:
            logger.warning(f"Authentication attempt on inactive account: {username}")
            return None
        
        # Verify password
        password_hash = user.metadata.get("password_hash")
        if not password_hash or not self.pwd_context.verify(password, password_hash):
            await self._record_failed_attempt(username, ip_address)
            user.login_attempts += 1
            
            # Lock account if too many failed attempts
            if user.login_attempts >= self.security_policy.max_login_attempts:
                user.locked_until = time.time() + (self.security_policy.lockout_duration_minutes * 60)
                logger.warning(f"Account locked due to failed attempts: {username}")
            
            return None
        
        # Reset failed attempts on successful login
        user.login_attempts = 0
        user.locked_until = None
        user.last_login = time.time()
        
        # Create session
        session = await self._create_session(user, ip_address)
        
        logger.info(f"User authenticated: {username} (session: {session.session_id})")
        return session
    
    async def _create_session(self, user: User, ip_address: Optional[str] = None) -> AuthSession:
        """Create authentication session."""
        session_id = secrets.token_urlsafe(32)
        
        # Create JWT token
        payload = {
            "user_id": user.user_id,
            "username": user.username,
            "session_id": session_id,
            "roles": [role.value for role in user.roles],
            "permissions": [perm.value for perm in user.permissions],
            "iat": time.time(),
            "exp": time.time() + (self.security_policy.token_expiry_hours * 3600)
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        # Create session
        session = AuthSession(
            session_id=session_id,
            user_id=user.user_id,
            token=token,
            created_at=time.time(),
            expires_at=payload["exp"],
            last_activity=time.time(),
            ip_address=ip_address,
            permissions=user.permissions
        )
        
        self.sessions[session_id] = session
        return session


    async def _record_failed_attempt(self, username: str, ip_address: Optional[str] = None):
        """Record failed authentication attempt."""
        identifier = ip_address or username
        current_time = time.time()

        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []

        self.failed_attempts[identifier].append(current_time)

        # Keep only attempts from last hour
        hour_ago = current_time - 3600
        self.failed_attempts[identifier] = [
            t for t in self.failed_attempts[identifier] if t > hour_ago
        ]

    async def verify_session(self, token: str) -> Optional[AuthSession]:
        """Verify authentication session."""
        try:
            # Decode JWT token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            session_id = payload.get("session_id")

            if not session_id or session_id not in self.sessions:
                return None

            session = self.sessions[session_id]

            # Check if session is expired
            if session.is_expired:
                del self.sessions[session_id]
                return None

            # Check if session is idle
            if session.is_idle(self.security_policy.session_timeout_minutes):
                del self.sessions[session_id]
                return None

            # Update last activity
            session.last_activity = time.time()

            return session

        except jwt.InvalidTokenError:
            return None

    async def create_api_key(
        self,
        user_id: str,
        name: str,
        permissions: List[Permission] = None,
        expires_in_days: Optional[int] = None,
        rate_limit_per_minute: Optional[int] = None,
        rate_limit_per_hour: Optional[int] = None,
        allowed_ips: Optional[List[str]] = None
    ) -> Tuple[str, APIKey]:
        """Create API key with enhanced security."""
        if user_id not in self.users:
            raise ValueError("User not found")

        user = self.users[user_id]

        # Check if user has permission to create API keys
        if not user.has_permission(Permission.API_KEY_CREATE):
            raise ValueError("User does not have permission to create API keys")

        # Generate API key
        key_value = f"ak_{secrets.token_urlsafe(32)}"
        key_hash = hashlib.sha256(key_value.encode()).hexdigest()
        key_id = secrets.token_urlsafe(16)

        # Set expiration
        expires_at = None
        if expires_in_days:
            expires_at = time.time() + (expires_in_days * 86400)
        else:
            expires_at = time.time() + (self.security_policy.api_key_expiry_days * 86400)

        # Set permissions (limited to user's permissions)
        if permissions is None:
            permissions = list(user.permissions)
        else:
            # Ensure user has all requested permissions
            requested_perms = set(permissions)
            if not requested_perms.issubset(user.permissions):
                raise ValueError("Cannot grant permissions user doesn't have")

        api_key = APIKey(
            key_id=key_id,
            key_hash=key_hash,
            name=name,
            user_id=user_id,
            permissions=set(permissions),
            expires_at=expires_at,
            rate_limit_per_minute=rate_limit_per_minute,
            rate_limit_per_hour=rate_limit_per_hour,
            allowed_ips=allowed_ips
        )

        self.api_keys[key_id] = api_key

        logger.info(f"Created API key: {name} for user {user_id}")
        return key_value, api_key

    async def verify_api_key(self, key_value: str, ip_address: Optional[str] = None) -> Optional[APIKey]:
        """Verify API key with enhanced security checks."""
        key_hash = hashlib.sha256(key_value.encode()).hexdigest()

        # Find API key by hash
        for api_key in self.api_keys.values():
            if (api_key.key_hash == key_hash and
                api_key.is_active and
                not api_key.is_expired):

                # Check IP restrictions
                if api_key.allowed_ips and ip_address:
                    if ip_address not in api_key.allowed_ips:
                        logger.warning(f"API key access denied from IP: {ip_address}")
                        return None

                # Check rate limits
                if api_key.rate_limit_per_minute:
                    if not await self._check_api_key_rate_limit(
                        api_key.key_id, api_key.rate_limit_per_minute, 60
                    ):
                        return None

                if api_key.rate_limit_per_hour:
                    if not await self._check_api_key_rate_limit(
                        api_key.key_id, api_key.rate_limit_per_hour, 3600
                    ):
                        return None

                # Update last used
                api_key.last_used = time.time()
                return api_key

        return None

    async def _check_api_key_rate_limit(self, key_id: str, limit: int, window_seconds: int) -> bool:
        """Check rate limit for API key."""
        current_time = time.time()
        window_start = current_time - window_seconds

        rate_key = f"api_key_{key_id}_{window_seconds}"

        # Clean old entries
        if rate_key in self.rate_limits:
            self.rate_limits[rate_key] = [
                t for t in self.rate_limits[rate_key] if t > window_start
            ]
        else:
            self.rate_limits[rate_key] = []

        # Check limit
        if len(self.rate_limits[rate_key]) >= limit:
            return False

        # Add current request
        self.rate_limits[rate_key].append(current_time)
        return True

    async def check_permission(
        self,
        auth_context: Union[AuthSession, APIKey],
        permission: Permission
    ) -> bool:
        """Check if authentication context has specific permission."""
        return permission in auth_context.permissions

    async def revoke_session(self, session_id: str) -> bool:
        """Revoke authentication session."""
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"Session revoked: {session_id}")
            return True
        return False

    async def revoke_api_key(self, key_id: str, user_id: str) -> bool:
        """Revoke API key with user authorization."""
        if key_id not in self.api_keys:
            return False

        api_key = self.api_keys[key_id]

        # Check if user owns the key or has admin permissions
        user = self.users.get(user_id)
        if not user:
            return False

        if (api_key.user_id != user_id and
            not user.has_permission(Permission.API_KEY_ADMIN)):
            return False

        api_key.is_active = False
        logger.info(f"API key revoked: {key_id} by user {user_id}")
        return True

    async def _background_cleanup(self):
        """Background task for security maintenance."""
        while self._running:
            try:
                current_time = time.time()

                # Clean expired sessions
                expired_sessions = [
                    sid for sid, session in self.sessions.items()
                    if session.is_expired or session.is_idle(self.security_policy.session_timeout_minutes)
                ]

                for session_id in expired_sessions:
                    del self.sessions[session_id]

                if expired_sessions:
                    logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")

                # Clean old failed attempts (older than 24 hours)
                day_ago = current_time - 86400
                for identifier in list(self.failed_attempts.keys()):
                    self.failed_attempts[identifier] = [
                        t for t in self.failed_attempts[identifier] if t > day_ago
                    ]
                    if not self.failed_attempts[identifier]:
                        del self.failed_attempts[identifier]

                # Clean old rate limit entries
                for rate_key in list(self.rate_limits.keys()):
                    # Extract window from rate key
                    if "_" in rate_key:
                        try:
                            window = int(rate_key.split("_")[-1])
                            window_start = current_time - window
                            self.rate_limits[rate_key] = [
                                t for t in self.rate_limits[rate_key] if t > window_start
                            ]
                            if not self.rate_limits[rate_key]:
                                del self.rate_limits[rate_key]
                        except (ValueError, IndexError):
                            pass

                await asyncio.sleep(300)  # Clean every 5 minutes

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in auth cleanup: {e}")
                await asyncio.sleep(300)

    def get_security_stats(self) -> Dict[str, Any]:
        """Get comprehensive security statistics."""
        current_time = time.time()

        # Count active sessions
        active_sessions = len([
            s for s in self.sessions.values()
            if not s.is_expired and not s.is_idle(self.security_policy.session_timeout_minutes)
        ])

        # Count active API keys
        active_api_keys = len([
            k for k in self.api_keys.values()
            if k.is_active and not k.is_expired
        ])

        # Count locked users
        locked_users = len([
            u for u in self.users.values() if u.is_locked
        ])

        # Count recent failed attempts (last hour)
        hour_ago = current_time - 3600
        recent_failures = sum(
            len([t for t in attempts if t > hour_ago])
            for attempts in self.failed_attempts.values()
        )

        return {
            "users": {
                "total": len(self.users),
                "active": len([u for u in self.users.values() if u.is_active]),
                "locked": locked_users,
                "verified": len([u for u in self.users.values() if u.is_verified])
            },
            "sessions": {
                "active": active_sessions,
                "total": len(self.sessions)
            },
            "api_keys": {
                "active": active_api_keys,
                "total": len(self.api_keys)
            },
            "security": {
                "failed_attempts_last_hour": recent_failures,
                "rate_limited_keys": len(self.rate_limits),
                "security_policy": {
                    "password_min_length": self.security_policy.password_min_length,
                    "max_login_attempts": self.security_policy.max_login_attempts,
                    "lockout_duration_minutes": self.security_policy.lockout_duration_minutes,
                    "token_expiry_hours": self.security_policy.token_expiry_hours
                }
            }
        }


# Global enhanced auth manager instance
enhanced_auth_manager = EnhancedAuthManager()
