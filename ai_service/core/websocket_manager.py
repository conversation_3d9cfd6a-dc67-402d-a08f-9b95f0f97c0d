"""
WebSocket manager for real-time AI service communication.
"""
import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, Set, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import weakref

from fastapi import WebSocket, WebSocketDisconnect
from ai_service.core.streaming_manager import streaming_manager, StreamType
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """WebSocket message types."""
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    STREAM_START = "stream_start"
    STREAM_CHUNK = "stream_chunk"
    STREAM_END = "stream_end"
    STREAM_ERROR = "stream_error"
    HEARTBEAT = "heartbeat"
    STATUS = "status"
    ERROR = "error"


@dataclass
class WebSocketMessage:
    """WebSocket message structure."""
    type: MessageType
    data: Any
    timestamp: datetime = field(default_factory=datetime.utcnow)
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    stream_id: Optional[str] = None
    client_id: Optional[str] = None


@dataclass
class WebSocketClient:
    """WebSocket client information."""
    id: str
    websocket: WebSocket
    connected_at: datetime = field(default_factory=datetime.utcnow)
    last_heartbeat: datetime = field(default_factory=datetime.utcnow)
    subscriptions: Set[str] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0


class WebSocketManager:
    """WebSocket manager for real-time communication."""
    
    def __init__(self):
        # Client management
        self.clients: Dict[str, WebSocketClient] = {}
        self.websocket_to_client: weakref.WeakKeyDictionary = weakref.WeakKeyDictionary()
        
        # Subscription management
        self.subscriptions: Dict[str, Set[str]] = {}  # topic -> client_ids
        self.client_subscriptions: Dict[str, Set[str]] = {}  # client_id -> topics
        
        # Message handlers
        self.message_handlers: Dict[MessageType, Callable] = {}
        self._register_default_handlers()
        
        # Background tasks
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.total_connections = 0
        self.total_messages_sent = 0
        self.total_messages_received = 0
        
        # Start background tasks
        self._start_background_tasks()
        
        logger.info("WebSocket manager initialized")
    
    def _register_default_handlers(self):
        """Register default message handlers."""
        self.message_handlers[MessageType.CONNECT] = self._handle_connect
        self.message_handlers[MessageType.DISCONNECT] = self._handle_disconnect
        self.message_handlers[MessageType.SUBSCRIBE] = self._handle_subscribe
        self.message_handlers[MessageType.UNSUBSCRIBE] = self._handle_unsubscribe
        self.message_handlers[MessageType.HEARTBEAT] = self._handle_heartbeat
        self.message_handlers[MessageType.STATUS] = self._handle_status
    
    def _start_background_tasks(self):
        """Start background tasks."""
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def connect_client(self, websocket: WebSocket, client_id: Optional[str] = None) -> str:
        """Connect a new WebSocket client."""
        await websocket.accept()
        
        # Generate client ID if not provided
        if not client_id:
            client_id = str(uuid.uuid4())
        
        # Create client
        client = WebSocketClient(
            id=client_id,
            websocket=websocket
        )
        
        # Store client
        self.clients[client_id] = client
        self.websocket_to_client[websocket] = client_id
        self.client_subscriptions[client_id] = set()
        
        # Update statistics
        self.total_connections += 1
        
        # Send connection confirmation
        await self.send_message(client_id, WebSocketMessage(
            type=MessageType.CONNECT,
            data={
                "client_id": client_id,
                "connected_at": client.connected_at.isoformat(),
                "status": "connected"
            },
            client_id=client_id
        ))
        
        logger.info(f"WebSocket client {client_id} connected")
        
        # Record metrics
        metrics_collector.record_counter("websocket_connections_total", 1.0)
        metrics_collector.record_gauge("websocket_active_connections", len(self.clients))
        
        return client_id
    
    async def disconnect_client(self, client_id: str):
        """Disconnect a WebSocket client."""
        if client_id not in self.clients:
            return
        
        client = self.clients[client_id]
        
        # Remove subscriptions
        for topic in list(self.client_subscriptions[client_id]):
            await self.unsubscribe_client(client_id, topic)
        
        # Remove client
        del self.clients[client_id]
        del self.client_subscriptions[client_id]
        
        # Remove from websocket mapping
        if client.websocket in self.websocket_to_client:
            del self.websocket_to_client[client.websocket]
        
        logger.info(f"WebSocket client {client_id} disconnected")
        
        # Record metrics
        metrics_collector.record_counter("websocket_disconnections_total", 1.0)
        metrics_collector.record_gauge("websocket_active_connections", len(self.clients))
    
    async def send_message(self, client_id: str, message: WebSocketMessage) -> bool:
        """Send message to specific client."""
        if client_id not in self.clients:
            return False
        
        client = self.clients[client_id]
        
        try:
            # Serialize message
            message_data = {
                "type": message.type.value,
                "data": message.data,
                "timestamp": message.timestamp.isoformat(),
                "message_id": message.message_id,
                "stream_id": message.stream_id,
                "client_id": message.client_id
            }
            
            message_json = json.dumps(message_data, default=str)
            
            # Send message
            await client.websocket.send_text(message_json)
            
            # Update statistics
            client.messages_sent += 1
            client.bytes_sent += len(message_json.encode('utf-8'))
            self.total_messages_sent += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending message to client {client_id}: {e}")
            await self.disconnect_client(client_id)
            return False
    
    async def broadcast_message(self, message: WebSocketMessage, topic: Optional[str] = None) -> int:
        """Broadcast message to all clients or topic subscribers."""
        sent_count = 0
        
        if topic:
            # Send to topic subscribers
            client_ids = self.subscriptions.get(topic, set())
        else:
            # Send to all clients
            client_ids = set(self.clients.keys())
        
        for client_id in list(client_ids):
            if await self.send_message(client_id, message):
                sent_count += 1
        
        return sent_count
    
    async def subscribe_client(self, client_id: str, topic: str) -> bool:
        """Subscribe client to topic."""
        if client_id not in self.clients:
            return False
        
        # Add to subscriptions
        if topic not in self.subscriptions:
            self.subscriptions[topic] = set()
        
        self.subscriptions[topic].add(client_id)
        self.client_subscriptions[client_id].add(topic)
        
        logger.info(f"Client {client_id} subscribed to topic {topic}")
        
        # Send confirmation
        await self.send_message(client_id, WebSocketMessage(
            type=MessageType.SUBSCRIBE,
            data={
                "topic": topic,
                "status": "subscribed"
            },
            client_id=client_id
        ))
        
        return True
    
    async def unsubscribe_client(self, client_id: str, topic: str) -> bool:
        """Unsubscribe client from topic."""
        if client_id not in self.clients:
            return False
        
        # Remove from subscriptions
        if topic in self.subscriptions:
            self.subscriptions[topic].discard(client_id)
            
            # Remove empty topic
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
        
        self.client_subscriptions[client_id].discard(topic)
        
        logger.info(f"Client {client_id} unsubscribed from topic {topic}")
        
        # Send confirmation
        await self.send_message(client_id, WebSocketMessage(
            type=MessageType.UNSUBSCRIBE,
            data={
                "topic": topic,
                "status": "unsubscribed"
            },
            client_id=client_id
        ))
        
        return True
    
    async def handle_client_message(self, client_id: str, message_data: str):
        """Handle incoming message from client."""
        if client_id not in self.clients:
            return
        
        client = self.clients[client_id]
        
        try:
            # Parse message
            data = json.loads(message_data)
            message_type = MessageType(data.get("type"))
            
            message = WebSocketMessage(
                type=message_type,
                data=data.get("data", {}),
                message_id=data.get("message_id", str(uuid.uuid4())),
                stream_id=data.get("stream_id"),
                client_id=client_id
            )
            
            # Update statistics
            client.messages_received += 1
            client.bytes_received += len(message_data.encode('utf-8'))
            self.total_messages_received += 1
            
            # Handle message
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](client_id, message)
            else:
                logger.warning(f"Unknown message type: {message_type}")
            
        except Exception as e:
            logger.error(f"Error handling message from client {client_id}: {e}")
            
            # Send error response
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.ERROR,
                data={
                    "error": "Invalid message format",
                    "details": str(e)
                },
                client_id=client_id
            ))
    
    async def start_stream_for_client(
        self,
        client_id: str,
        stream_type: StreamType,
        stream_config: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Start streaming for a client."""
        if client_id not in self.clients:
            return None
        
        # Create stream
        stream_id = await streaming_manager.create_stream(
            stream_type=stream_type,
            client_id=client_id,
            metadata=stream_config or {}
        )
        
        # Start stream
        await streaming_manager.start_stream(stream_id)
        
        # Send stream start message
        await self.send_message(client_id, WebSocketMessage(
            type=MessageType.STREAM_START,
            data={
                "stream_id": stream_id,
                "stream_type": stream_type.value,
                "status": "started"
            },
            stream_id=stream_id,
            client_id=client_id
        ))
        
        # Start streaming task
        asyncio.create_task(self._stream_to_client(client_id, stream_id))
        
        return stream_id
    
    async def _stream_to_client(self, client_id: str, stream_id: str):
        """Stream chunks to client via WebSocket."""
        try:
            # Get stream iterator
            iterator = await streaming_manager.get_stream_iterator(stream_id)
            if not iterator:
                return
            
            # Stream chunks
            async for chunk in iterator:
                if client_id not in self.clients:
                    break
                
                # Send chunk
                await self.send_message(client_id, WebSocketMessage(
                    type=MessageType.STREAM_CHUNK,
                    data={
                        "chunk_id": chunk.id,
                        "chunk_type": chunk.chunk_type,
                        "data": chunk.data,
                        "sequence": chunk.sequence,
                        "metadata": chunk.metadata,
                        "is_final": chunk.is_final
                    },
                    stream_id=stream_id,
                    client_id=client_id
                ))
                
                # Break if final chunk
                if chunk.is_final:
                    break
            
            # Send stream end message
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.STREAM_END,
                data={
                    "stream_id": stream_id,
                    "status": "completed"
                },
                stream_id=stream_id,
                client_id=client_id
            ))
            
        except Exception as e:
            logger.error(f"Error streaming to client {client_id}: {e}")
            
            # Send error message
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.STREAM_ERROR,
                data={
                    "stream_id": stream_id,
                    "error": str(e)
                },
                stream_id=stream_id,
                client_id=client_id
            ))
    
    # Message Handlers
    async def _handle_connect(self, client_id: str, message: WebSocketMessage):
        """Handle connect message."""
        # Connection already handled in connect_client
        pass
    
    async def _handle_disconnect(self, client_id: str, message: WebSocketMessage):
        """Handle disconnect message."""
        await self.disconnect_client(client_id)
    
    async def _handle_subscribe(self, client_id: str, message: WebSocketMessage):
        """Handle subscribe message."""
        topic = message.data.get("topic")
        if topic:
            await self.subscribe_client(client_id, topic)
    
    async def _handle_unsubscribe(self, client_id: str, message: WebSocketMessage):
        """Handle unsubscribe message."""
        topic = message.data.get("topic")
        if topic:
            await self.unsubscribe_client(client_id, topic)
    
    async def _handle_heartbeat(self, client_id: str, message: WebSocketMessage):
        """Handle heartbeat message."""
        if client_id in self.clients:
            client = self.clients[client_id]
            client.last_heartbeat = datetime.utcnow()
            
            # Send heartbeat response
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.HEARTBEAT,
                data={
                    "timestamp": datetime.utcnow().isoformat(),
                    "status": "alive"
                },
                client_id=client_id
            ))
    
    async def _handle_status(self, client_id: str, message: WebSocketMessage):
        """Handle status request message."""
        if client_id in self.clients:
            client = self.clients[client_id]
            
            status_data = {
                "client_id": client_id,
                "connected_at": client.connected_at.isoformat(),
                "last_heartbeat": client.last_heartbeat.isoformat(),
                "subscriptions": list(client.subscriptions),
                "messages_sent": client.messages_sent,
                "messages_received": client.messages_received,
                "bytes_sent": client.bytes_sent,
                "bytes_received": client.bytes_received
            }
            
            await self.send_message(client_id, WebSocketMessage(
                type=MessageType.STATUS,
                data=status_data,
                client_id=client_id
            ))
    
    def get_client_info(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Get client information."""
        if client_id not in self.clients:
            return None
        
        client = self.clients[client_id]
        
        return {
            "id": client.id,
            "connected_at": client.connected_at.isoformat(),
            "last_heartbeat": client.last_heartbeat.isoformat(),
            "subscriptions": list(client.subscriptions),
            "messages_sent": client.messages_sent,
            "messages_received": client.messages_received,
            "bytes_sent": client.bytes_sent,
            "bytes_received": client.bytes_received,
            "metadata": client.metadata
        }
    
    def get_websocket_stats(self) -> Dict[str, Any]:
        """Get WebSocket statistics."""
        return {
            "total_connections": self.total_connections,
            "active_connections": len(self.clients),
            "total_messages_sent": self.total_messages_sent,
            "total_messages_received": self.total_messages_received,
            "active_subscriptions": len(self.subscriptions),
            "clients_by_subscriptions": {
                topic: len(clients) for topic, clients in self.subscriptions.items()
            }
        }
    
    async def _heartbeat_loop(self):
        """Background heartbeat loop."""
        while True:
            try:
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
                
                current_time = datetime.utcnow()
                
                # Check for inactive clients
                inactive_clients = []
                for client_id, client in self.clients.items():
                    time_since_heartbeat = (current_time - client.last_heartbeat).total_seconds()
                    
                    if time_since_heartbeat > 120:  # 2 minutes timeout
                        inactive_clients.append(client_id)
                
                # Disconnect inactive clients
                for client_id in inactive_clients:
                    logger.warning(f"Disconnecting inactive client: {client_id}")
                    await self.disconnect_client(client_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Heartbeat loop error: {e}")
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
                # Clean up empty subscriptions
                empty_topics = [
                    topic for topic, clients in self.subscriptions.items()
                    if not clients
                ]
                
                for topic in empty_topics:
                    del self.subscriptions[topic]
                    logger.debug(f"Cleaned up empty topic: {topic}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
    
    async def shutdown(self):
        """Shutdown WebSocket manager."""
        # Cancel background tasks
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Disconnect all clients
        for client_id in list(self.clients.keys()):
            await self.disconnect_client(client_id)
        
        logger.info("WebSocket manager shutdown completed")


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
