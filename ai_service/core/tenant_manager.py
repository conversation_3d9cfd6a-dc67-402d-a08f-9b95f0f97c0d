"""
Multi-tenant support and isolation system.
"""
import asyncio
import logging
import secrets
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class TenantStatus(Enum):
    """Tenant status."""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    INACTIVE = "inactive"
    PENDING = "pending"


class TenantTier(Enum):
    """Tenant service tiers."""
    FREE = "free"
    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class IsolationLevel(Enum):
    """Data isolation levels."""
    SHARED = "shared"           # Shared infrastructure, logical separation
    DEDICATED = "dedicated"     # Dedicated resources
    HYBRID = "hybrid"          # Mix of shared and dedicated


@dataclass
class TenantQuota:
    """Tenant resource quotas."""
    requests_per_minute: int = 100
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    tokens_per_minute: int = 10000
    tokens_per_hour: int = 100000
    tokens_per_day: int = 1000000
    concurrent_requests: int = 10
    storage_mb: int = 1000
    users_limit: int = 10
    api_keys_limit: int = 5
    collaboration_sessions: int = 5
    streaming_connections: int = 50


@dataclass
class TenantUsage:
    """Current tenant usage."""
    requests_today: int = 0
    requests_this_hour: int = 0
    requests_this_minute: int = 0
    tokens_today: int = 0
    tokens_this_hour: int = 0
    tokens_this_minute: int = 0
    current_concurrent_requests: int = 0
    storage_used_mb: float = 0.0
    active_users: int = 0
    active_api_keys: int = 0
    active_sessions: int = 0
    active_streams: int = 0
    last_updated: datetime = field(default_factory=datetime.utcnow)


@dataclass
class TenantConfiguration:
    """Tenant-specific configuration."""
    allowed_models: List[str] = field(default_factory=list)
    blocked_models: List[str] = field(default_factory=list)
    allowed_features: List[str] = field(default_factory=list)
    blocked_features: List[str] = field(default_factory=list)
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    security_settings: Dict[str, Any] = field(default_factory=dict)
    compliance_requirements: List[str] = field(default_factory=list)
    data_retention_days: int = 90
    audit_level: str = "standard"  # minimal, standard, detailed, comprehensive


@dataclass
class Tenant:
    """Tenant entity."""
    id: str
    name: str
    display_name: str
    status: TenantStatus = TenantStatus.ACTIVE
    tier: TenantTier = TenantTier.FREE
    isolation_level: IsolationLevel = IsolationLevel.SHARED
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    owner_user_id: Optional[str] = None
    contact_email: Optional[str] = None
    quotas: TenantQuota = field(default_factory=TenantQuota)
    usage: TenantUsage = field(default_factory=TenantUsage)
    configuration: TenantConfiguration = field(default_factory=TenantConfiguration)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_active(self) -> bool:
        """Check if tenant is active."""
        if self.status != TenantStatus.ACTIVE:
            return False
        
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return False
        
        return True
    
    def is_quota_exceeded(self, quota_type: str) -> bool:
        """Check if specific quota is exceeded."""
        quota_value = getattr(self.quotas, quota_type, 0)
        usage_value = getattr(self.usage, quota_type, 0)
        
        return usage_value >= quota_value
    
    def get_quota_utilization(self, quota_type: str) -> float:
        """Get quota utilization percentage."""
        quota_value = getattr(self.quotas, quota_type, 1)
        usage_value = getattr(self.usage, quota_type, 0)
        
        return (usage_value / quota_value) * 100 if quota_value > 0 else 0


class TenantManager:
    """Multi-tenant support and isolation system."""
    
    def __init__(self):
        # Tenant storage
        self.tenants: Dict[str, Tenant] = {}
        self.tenant_name_index: Dict[str, str] = {}  # name -> tenant_id
        
        # Default tenant for single-tenant mode
        self.default_tenant_id = "default"
        
        # Tier configurations
        self.tier_configs = self._initialize_tier_configs()
        
        # Background tasks
        self.usage_reset_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.total_tenants_created = 0
        self.active_tenants = 0
        
        # Initialize default tenant
        self._create_default_tenant()
        
        # Start background tasks
        self._start_background_tasks()
        
        logger.info("Tenant manager initialized")
    
    def _initialize_tier_configs(self) -> Dict[TenantTier, TenantQuota]:
        """Initialize default tier configurations."""
        return {
            TenantTier.FREE: TenantQuota(
                requests_per_minute=10,
                requests_per_hour=100,
                requests_per_day=1000,
                tokens_per_minute=1000,
                tokens_per_hour=10000,
                tokens_per_day=100000,
                concurrent_requests=2,
                storage_mb=100,
                users_limit=1,
                api_keys_limit=2,
                collaboration_sessions=1,
                streaming_connections=5
            ),
            TenantTier.BASIC: TenantQuota(
                requests_per_minute=50,
                requests_per_hour=500,
                requests_per_day=5000,
                tokens_per_minute=5000,
                tokens_per_hour=50000,
                tokens_per_day=500000,
                concurrent_requests=5,
                storage_mb=500,
                users_limit=5,
                api_keys_limit=5,
                collaboration_sessions=3,
                streaming_connections=20
            ),
            TenantTier.PROFESSIONAL: TenantQuota(
                requests_per_minute=200,
                requests_per_hour=2000,
                requests_per_day=20000,
                tokens_per_minute=20000,
                tokens_per_hour=200000,
                tokens_per_day=2000000,
                concurrent_requests=20,
                storage_mb=2000,
                users_limit=20,
                api_keys_limit=10,
                collaboration_sessions=10,
                streaming_connections=100
            ),
            TenantTier.ENTERPRISE: TenantQuota(
                requests_per_minute=1000,
                requests_per_hour=10000,
                requests_per_day=100000,
                tokens_per_minute=100000,
                tokens_per_hour=1000000,
                tokens_per_day=10000000,
                concurrent_requests=100,
                storage_mb=10000,
                users_limit=100,
                api_keys_limit=50,
                collaboration_sessions=50,
                streaming_connections=500
            )
        }
    
    def _create_default_tenant(self):
        """Create default tenant for single-tenant mode."""
        default_tenant = Tenant(
            id=self.default_tenant_id,
            name="default",
            display_name="Default Tenant",
            tier=TenantTier.ENTERPRISE,  # Give default tenant enterprise limits
            quotas=self.tier_configs[TenantTier.ENTERPRISE]
        )
        
        self.tenants[self.default_tenant_id] = default_tenant
        self.tenant_name_index["default"] = self.default_tenant_id
    
    def _start_background_tasks(self):
        """Start background tasks."""
        self.usage_reset_task = asyncio.create_task(self._usage_reset_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def create_tenant(
        self,
        name: str,
        display_name: str,
        tier: TenantTier = TenantTier.FREE,
        owner_user_id: Optional[str] = None,
        contact_email: Optional[str] = None,
        expires_at: Optional[datetime] = None,
        custom_quotas: Optional[TenantQuota] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tenant:
        """Create a new tenant."""
        # Check if tenant name already exists
        if name in self.tenant_name_index:
            raise ValueError(f"Tenant name '{name}' already exists")
        
        # Generate tenant ID
        tenant_id = f"tenant_{secrets.token_urlsafe(16)}"
        
        # Get quotas for tier
        quotas = custom_quotas or self.tier_configs.get(tier, TenantQuota())
        
        # Create tenant
        tenant = Tenant(
            id=tenant_id,
            name=name,
            display_name=display_name,
            tier=tier,
            owner_user_id=owner_user_id,
            contact_email=contact_email,
            expires_at=expires_at,
            quotas=quotas,
            metadata=metadata or {}
        )
        
        # Store tenant
        self.tenants[tenant_id] = tenant
        self.tenant_name_index[name] = tenant_id
        
        # Update statistics
        self.total_tenants_created += 1
        self.active_tenants += 1
        
        logger.info(f"Created tenant: {name} ({tenant_id}) - {tier.value}")
        
        # Record metrics
        metrics_collector.record_counter(
            "tenants_created_total",
            1.0,
            {"tier": tier.value}
        )
        
        return tenant
    
    async def get_tenant(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID."""
        return self.tenants.get(tenant_id)
    
    async def get_tenant_by_name(self, name: str) -> Optional[Tenant]:
        """Get tenant by name."""
        tenant_id = self.tenant_name_index.get(name)
        if tenant_id:
            return self.tenants.get(tenant_id)
        return None
    
    async def update_tenant(
        self,
        tenant_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update tenant configuration."""
        tenant = self.tenants.get(tenant_id)
        if not tenant:
            return False
        
        # Update allowed fields
        allowed_fields = {
            'display_name', 'status', 'tier', 'expires_at',
            'contact_email', 'metadata'
        }
        
        for field, value in updates.items():
            if field in allowed_fields:
                setattr(tenant, field, value)
        
        # Update quotas if tier changed
        if 'tier' in updates:
            new_tier = TenantTier(updates['tier'])
            tenant.quotas = self.tier_configs.get(new_tier, tenant.quotas)
        
        tenant.updated_at = datetime.utcnow()
        
        logger.info(f"Updated tenant: {tenant.name} ({tenant_id})")
        return True
    
    async def suspend_tenant(self, tenant_id: str, reason: str = "") -> bool:
        """Suspend tenant."""
        tenant = self.tenants.get(tenant_id)
        if not tenant:
            return False
        
        tenant.status = TenantStatus.SUSPENDED
        tenant.updated_at = datetime.utcnow()
        tenant.metadata['suspension_reason'] = reason
        tenant.metadata['suspended_at'] = datetime.utcnow().isoformat()
        
        logger.warning(f"Suspended tenant: {tenant.name} ({tenant_id}) - {reason}")
        
        # Record metrics
        metrics_collector.record_counter(
            "tenants_suspended_total",
            1.0,
            {"tenant_id": tenant_id}
        )
        
        return True
    
    async def activate_tenant(self, tenant_id: str) -> bool:
        """Activate suspended tenant."""
        tenant = self.tenants.get(tenant_id)
        if not tenant:
            return False
        
        tenant.status = TenantStatus.ACTIVE
        tenant.updated_at = datetime.utcnow()
        tenant.metadata.pop('suspension_reason', None)
        tenant.metadata.pop('suspended_at', None)
        
        logger.info(f"Activated tenant: {tenant.name} ({tenant_id})")
        return True
    
    async def check_tenant_access(
        self,
        tenant_id: str,
        feature: Optional[str] = None,
        model: Optional[str] = None
    ) -> bool:
        """Check if tenant has access to feature/model."""
        tenant = self.tenants.get(tenant_id)
        if not tenant or not tenant.is_active():
            return False
        
        # Check feature access
        if feature:
            if tenant.configuration.blocked_features and feature in tenant.configuration.blocked_features:
                return False
            
            if tenant.configuration.allowed_features and feature not in tenant.configuration.allowed_features:
                return False
        
        # Check model access
        if model:
            if tenant.configuration.blocked_models and model in tenant.configuration.blocked_models:
                return False
            
            if tenant.configuration.allowed_models and model not in tenant.configuration.allowed_models:
                return False
        
        return True
    
    async def check_quota(
        self,
        tenant_id: str,
        quota_type: str,
        amount: int = 1
    ) -> bool:
        """Check if tenant has quota available."""
        tenant = self.tenants.get(tenant_id)
        if not tenant or not tenant.is_active():
            return False
        
        quota_value = getattr(tenant.quotas, quota_type, 0)
        usage_value = getattr(tenant.usage, quota_type, 0)
        
        return (usage_value + amount) <= quota_value
    
    async def consume_quota(
        self,
        tenant_id: str,
        quota_type: str,
        amount: int = 1
    ) -> bool:
        """Consume tenant quota."""
        tenant = self.tenants.get(tenant_id)
        if not tenant or not tenant.is_active():
            return False
        
        # Check if quota is available
        if not await self.check_quota(tenant_id, quota_type, amount):
            return False
        
        # Consume quota
        current_value = getattr(tenant.usage, quota_type, 0)
        setattr(tenant.usage, quota_type, current_value + amount)
        tenant.usage.last_updated = datetime.utcnow()
        
        # Record metrics
        metrics_collector.record_counter(
            "tenant_quota_consumed_total",
            amount,
            {"tenant_id": tenant_id, "quota_type": quota_type}
        )
        
        return True
    
    async def get_tenant_usage(self, tenant_id: str) -> Optional[TenantUsage]:
        """Get tenant usage statistics."""
        tenant = self.tenants.get(tenant_id)
        if tenant:
            return tenant.usage
        return None
    
    def get_default_tenant_id(self) -> str:
        """Get default tenant ID."""
        return self.default_tenant_id
    
    def list_tenants(
        self,
        status: Optional[TenantStatus] = None,
        tier: Optional[TenantTier] = None
    ) -> List[Tenant]:
        """List tenants with optional filters."""
        tenants = list(self.tenants.values())
        
        if status:
            tenants = [t for t in tenants if t.status == status]
        
        if tier:
            tenants = [t for t in tenants if t.tier == tier]
        
        return tenants
    
    def get_tenant_stats(self) -> Dict[str, Any]:
        """Get tenant management statistics."""
        active_tenants = len([t for t in self.tenants.values() if t.is_active()])
        
        tenants_by_tier = {}
        tenants_by_status = {}
        
        for tenant in self.tenants.values():
            # Count by tier
            tier = tenant.tier.value
            tenants_by_tier[tier] = tenants_by_tier.get(tier, 0) + 1
            
            # Count by status
            status = tenant.status.value
            tenants_by_status[status] = tenants_by_status.get(status, 0) + 1
        
        return {
            "total_tenants": len(self.tenants),
            "active_tenants": active_tenants,
            "tenants_by_tier": tenants_by_tier,
            "tenants_by_status": tenants_by_status,
            "total_tenants_created": self.total_tenants_created
        }
    
    async def _usage_reset_loop(self):
        """Background task to reset usage counters."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                current_time = datetime.utcnow()
                
                for tenant in self.tenants.values():
                    # Reset minute counters
                    if current_time.minute != tenant.usage.last_updated.minute:
                        tenant.usage.requests_this_minute = 0
                        tenant.usage.tokens_this_minute = 0
                    
                    # Reset hour counters
                    if current_time.hour != tenant.usage.last_updated.hour:
                        tenant.usage.requests_this_hour = 0
                        tenant.usage.tokens_this_hour = 0
                    
                    # Reset day counters
                    if current_time.date() != tenant.usage.last_updated.date():
                        tenant.usage.requests_today = 0
                        tenant.usage.tokens_today = 0
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Usage reset loop error: {e}")
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(3600)  # Cleanup every hour
                
                current_time = datetime.utcnow()
                
                # Check for expired tenants
                for tenant in self.tenants.values():
                    if (tenant.expires_at and 
                        current_time > tenant.expires_at and 
                        tenant.status == TenantStatus.ACTIVE):
                        
                        tenant.status = TenantStatus.INACTIVE
                        logger.info(f"Tenant expired: {tenant.name} ({tenant.id})")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Tenant cleanup error: {e}")
    
    async def shutdown(self):
        """Shutdown tenant manager."""
        # Cancel background tasks
        if self.usage_reset_task:
            self.usage_reset_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        logger.info("Tenant manager shutdown completed")


# Global tenant manager instance
tenant_manager = TenantManager()
