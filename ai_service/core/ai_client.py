"""
Core AI client implementation for Google AI Studio.
"""
import base64
import logging
import mimetypes
import os
import json
import asyncio
import time
import inspect
from typing import Dict, List, Optional, Any, Union, AsyncIterator, Iterator, Tuple, BinaryIO, Callable
from concurrent.futures import Thread<PERSON><PERSON>Executor, TimeoutError as FutureTimeoutError

from google import genai
from google.genai import types
import requests

from ai_service.config.settings import settings
from ai_service.core.exceptions import (
    AIServiceException, ValidationError, ModelError, APIError,
    FileError, handle_api_exception, ErrorCode
)
from ai_service.core.retry_handler import (
    default_retry_handler, api_circuit_breaker, with_aretry,
    RetryableError, RateLimitError, TemporaryError
)
from ai_service.core.json_mode import json_mode_handler
from ai_service.core.context_cache import context_cache_manager
from ai_service.core.enhanced_function_executor import enhanced_function_executor
from ai_service.core.cache_manager import cache_manager
from ai_service.core.batch_processor import batch_processor
from ai_service.core.http_client import http_client_pool
from ai_service.core.streaming_manager import streaming_manager, StreamType
from ai_service.core.websocket_manager import websocket_manager
from ai_service.core.sse_manager import sse_manager

logger = logging.getLogger(__name__)


class AIClient:
    """Client for interacting with Google AI Studio API."""

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the AI client.

        Args:
            api_key: Google AI Studio API key. If not provided, uses the one from settings.
        """
        self.api_key = api_key or settings.google_api_key
        self.client = genai.Client(api_key=self.api_key)

        # Function execution engine
        self.function_registry: Dict[str, Callable] = {}
        self.function_executor = ThreadPoolExecutor(max_workers=4)
        self.function_timeout = 30  # seconds

        logger.info("AI client initialized")

    def register_function(self, name: str, func: Callable) -> None:
        """Register a function for execution during function calling.

        Args:
            name: Name of the function
            func: Function to register
        """
        if not callable(func):
            raise ValueError(f"Function {name} must be callable")

        self.function_registry[name] = func
        logger.info(f"Registered function: {name}")

    def unregister_function(self, name: str) -> None:
        """Unregister a function.

        Args:
            name: Name of the function to unregister
        """
        if name in self.function_registry:
            del self.function_registry[name]
            logger.info(f"Unregistered function: {name}")

    def execute_function(self, function_call: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a function call safely with timeout.

        Args:
            function_call: Function call information with name and args

        Returns:
            Dictionary containing function execution result
        """
        function_name = function_call.get("name")
        function_args = function_call.get("args", {})

        if not function_name:
            raise ValueError("Function call must have a name")

        if function_name not in self.function_registry:
            raise ValueError(f"Function {function_name} is not registered")

        func = self.function_registry[function_name]

        logger.info(f"Executing function: {function_name} with args: {function_args}")

        try:
            # Validate function arguments
            sig = inspect.signature(func)
            bound_args = sig.bind(**function_args)
            bound_args.apply_defaults()

            # Execute function with timeout
            start_time = time.time()
            future = self.function_executor.submit(func, **bound_args.arguments)

            try:
                result = future.result(timeout=self.function_timeout)
                execution_time = time.time() - start_time

                logger.info(f"Function {function_name} executed successfully in {execution_time:.2f}s")

                return {
                    "success": True,
                    "result": result,
                    "execution_time": execution_time,
                    "function_name": function_name
                }

            except FutureTimeoutError:
                future.cancel()
                logger.error(f"Function {function_name} timed out after {self.function_timeout}s")
                return {
                    "success": False,
                    "error": f"Function execution timed out after {self.function_timeout} seconds",
                    "function_name": function_name
                }

        except TypeError as e:
            logger.error(f"Invalid arguments for function {function_name}: {str(e)}")
            return {
                "success": False,
                "error": f"Invalid arguments: {str(e)}",
                "function_name": function_name
            }
        except Exception as e:
            logger.error(f"Error executing function {function_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "function_name": function_name
            }

    async def execute_function_async(self, function_call: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a function call asynchronously.

        Args:
            function_call: Function call information with name and args

        Returns:
            Dictionary containing function execution result
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.execute_function, function_call)

    def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
    ) -> Union[Dict[str, Any], Iterator[Dict[str, Any]]]:
        """Generate text using the AI model.

        Args:
            prompt: Text prompt for generation
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation
            stream: Whether to stream the response

        Returns:
            Dictionary containing generated text and usage information,
            or an iterator of dictionaries if streaming
        """
        # Validate inputs
        if not prompt or not prompt.strip():
            raise ValidationError("Prompt cannot be empty", field="prompt")

        if len(prompt) > 100000:  # Reasonable limit
            raise ValidationError("Prompt is too long", field="prompt",
                                details={"max_length": 100000, "actual_length": len(prompt)})

        # Validate parameters
        if temperature is not None and not (0.0 <= temperature <= 2.0):
            raise ValidationError("Temperature must be between 0.0 and 2.0", field="temperature")

        if top_p is not None and not (0.0 <= top_p <= 1.0):
            raise ValidationError("Top-p must be between 0.0 and 1.0", field="top_p")

        if top_k is not None and top_k < 1:
            raise ValidationError("Top-k must be at least 1", field="top_k")

        if max_tokens is not None and max_tokens < 1:
            raise ValidationError("Max tokens must be at least 1", field="max_tokens")

        model_name = model or settings.default_model

        # Validate model
        if model_name not in settings.available_models:
            raise ModelError(f"Model {model_name} is not available", model_name=model_name,
                           details={"available_models": list(settings.available_models.keys())})

        logger.info(f"Generating text with model {model_name}")

        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Create contents with system instruction if provided
        contents = []
        if system_instruction:
            contents.append(types.Content(
                role="user",
                parts=[{"text": system_instruction}]
            ))

        # Add the prompt
        contents.append(types.Content(
            role="user",
            parts=[{"text": prompt}]
        ))

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            if stream:
                # Return a streaming response
                response_stream = self.client.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config,
                    stream=True
                )

                return self._process_text_stream(response_stream, model_name)
            else:
                # Return a complete response
                response = self.client.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config
                )

                # Extract token usage if available
                usage = {}
                if hasattr(response, 'usage_metadata'):
                    usage = {
                        'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                        'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                        'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                    }

                finish_reason = None
                if response.candidates and hasattr(response.candidates[0], 'finish_reason'):
                    finish_reason = response.candidates[0].finish_reason

                return {
                    "text": response.text,
                    "model": model_name,
                    "usage": usage,
                    "finish_reason": finish_reason
                }
        except (ValidationError, ModelError) as e:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Convert other exceptions to APIError
            raise handle_api_exception(e, "generate_text")

    def _process_text_stream(self, response_stream, model_name: str) -> Iterator[Dict[str, Any]]:
        """Process a streaming text response with proper termination.

        Args:
            response_stream: Streaming response from the API
            model_name: Name of the model used

        Yields:
            Dictionary containing chunk of generated text
        """
        chunk_count = 0
        total_text = ""
        finish_reason = None

        try:
            for chunk in response_stream:
                # Check if the chunk has text content
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_count += 1
                    total_text += chunk.text

                    yield {
                        "text": chunk.text,
                        "model": model_name,
                        "is_final": False,
                        "chunk_id": chunk_count,
                        "finish_reason": None
                    }

                # Check for finish reason
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    candidate = chunk.candidates[0]
                    if hasattr(candidate, 'finish_reason'):
                        finish_reason = str(candidate.finish_reason)

        except Exception as e:
            logger.error(f"Error in streaming response: {str(e)}")
            finish_reason = "error"

        # Always send final chunk with completion info
        yield {
            "text": "",
            "model": model_name,
            "is_final": True,
            "chunk_id": chunk_count + 1,
            "finish_reason": finish_reason or "stop",
            "total_chunks": chunk_count,
            "total_text_length": len(total_text)
        }

    async def generate_text_async(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
        json_mode: bool = False,
        json_schema: Optional[Union[Dict[str, Any], Any]] = None,
    ) -> Union[Dict[str, Any], AsyncIterator[Dict[str, Any]]]:
        """Generate text using the AI model asynchronously.

        Args:
            prompt: Text prompt for generation
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation
            stream: Whether to stream the response
            json_mode: Enable JSON mode for structured output
            json_schema: JSON schema for structured output validation

        Returns:
            Dictionary containing generated text and usage information,
            or an async iterator of dictionaries if streaming
        """
        model_name = model or settings.default_model
        logger.info(f"Generating text asynchronously with model {model_name}")

        # Create base config
        config_params = {
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k,
        }

        if max_tokens:
            config_params["max_output_tokens"] = max_tokens

        # Add JSON mode support
        if json_mode:
            json_config = json_mode_handler.create_generation_config(
                json_mode=True,
                json_schema=json_schema,
                **config_params
            )
            config = types.GenerateContentConfig(**json_config)
            logger.info("JSON mode enabled for text generation")
        else:
            config = types.GenerateContentConfig(**config_params)

        # Create contents with system instruction if provided
        contents = []
        if system_instruction:
            contents.append(types.Content(
                role="user",
                parts=[{"text": system_instruction}]
            ))

        # Add the prompt
        contents.append(types.Content(
            role="user",
            parts=[{"text": prompt}]
        ))

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            if stream:
                # Return a streaming response
                response_stream = await self.client.aio.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config,
                    stream=True
                )

                return self._process_text_stream_async(response_stream, model_name)
            else:
                # Return a complete response
                response = await self.client.aio.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config
                )

                # Extract token usage if available
                usage = {}
                if hasattr(response, 'usage_metadata'):
                    usage = {
                        'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                        'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                        'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                    }

                finish_reason = None
                if response.candidates and hasattr(response.candidates[0], 'finish_reason'):
                    finish_reason = response.candidates[0].finish_reason

                # Validate JSON response if JSON mode is enabled
                response_text = response.text
                json_data = None
                if json_mode and response_text:
                    try:
                        json_data = json_mode_handler.validate_json_response(
                            response_text,
                            json_schema if json_schema else None
                        )
                        logger.info("JSON response validated successfully")
                    except ValueError as e:
                        logger.warning(f"JSON validation failed: {e}")
                        # Continue with raw text response

                result = {
                    "text": response_text,
                    "model": model_name,
                    "usage": usage,
                    "finish_reason": finish_reason,
                    "json_mode": json_mode
                }

                if json_data is not None:
                    result["json_data"] = json_data

                return result
        except Exception as e:
            logger.error(f"Error generating text asynchronously: {str(e)}")
            raise

    async def _process_text_stream_async(self, response_stream, model_name: str) -> AsyncIterator[Dict[str, Any]]:
        """Process a streaming text response asynchronously with proper termination.

        Args:
            response_stream: Streaming response from the API
            model_name: Name of the model used

        Yields:
            Dictionary containing chunk of generated text
        """
        chunk_count = 0
        total_text = ""
        finish_reason = None

        try:
            async for chunk in response_stream:
                # Check if the chunk has text content
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_count += 1
                    total_text += chunk.text

                    yield {
                        "text": chunk.text,
                        "model": model_name,
                        "is_final": False,
                        "chunk_id": chunk_count,
                        "finish_reason": None
                    }

                # Check for finish reason
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    candidate = chunk.candidates[0]
                    if hasattr(candidate, 'finish_reason'):
                        finish_reason = str(candidate.finish_reason)

        except Exception as e:
            logger.error(f"Error in async streaming response: {str(e)}")
            finish_reason = "error"

        # Always send final chunk with completion info
        yield {
            "text": "",
            "model": model_name,
            "is_final": True,
            "chunk_id": chunk_count + 1,
            "finish_reason": finish_reason or "stop",
            "total_chunks": chunk_count,
            "total_text_length": len(total_text)
        }

    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
    ) -> Union[Dict[str, Any], Iterator[Dict[str, Any]]]:
        """Generate chat completion using the AI model.

        Args:
            messages: List of chat messages
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation
            stream: Whether to stream the response

        Returns:
            Dictionary containing generated message and usage information,
            or an iterator of dictionaries if streaming
        """
        model_name = model or settings.default_model
        logger.info(f"Generating chat completion with model {model_name}")

        # Create a chat session
        chat = self.client.chats.create(model=model_name)

        # Set system instruction if provided
        if system_instruction:
            chat.system_instruction = system_instruction

        # Process previous messages to build context
        history = []
        for msg in messages[:-1]:  # All messages except the last one
            if msg["role"] == "user":
                history.append({"role": "user", "parts": [msg["content"]]})
            elif msg["role"] == "assistant":
                history.append({"role": "model", "parts": [msg["content"]]})

        # Set chat history if there are previous messages
        if history:
            chat.history = history

        # Get the last user message
        last_message = messages[-1]["content"]

        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            if stream:
                # Return a streaming response
                response_stream = chat.send_message(last_message, config=config, stream=True)
                return self._process_chat_stream(response_stream, model_name)
            else:
                # Send the message and get the response
                response = chat.send_message(last_message, config=config)

                # Extract token usage if available
                usage = {}
                if hasattr(response, 'usage_metadata'):
                    usage = {
                        'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                        'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                        'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                    }

                finish_reason = None
                if response.candidates and hasattr(response.candidates[0], 'finish_reason'):
                    finish_reason = response.candidates[0].finish_reason

                return {
                    "message": {
                        "role": "assistant",
                        "content": response.text
                    },
                    "model": model_name,
                    "usage": usage,
                    "finish_reason": finish_reason
                }
        except Exception as e:
            logger.error(f"Error generating chat completion: {str(e)}")
            raise

    def _process_chat_stream(self, response_stream, model_name: str) -> Iterator[Dict[str, Any]]:
        """Process a streaming chat response with proper termination.

        Args:
            response_stream: Streaming response from the API
            model_name: Name of the model used

        Yields:
            Dictionary containing chunk of generated message
        """
        chunk_count = 0
        total_content = ""
        finish_reason = None

        try:
            for chunk in response_stream:
                # Check if the chunk has text content
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_count += 1
                    total_content += chunk.text

                    yield {
                        "message": {
                            "role": "assistant",
                            "content": chunk.text
                        },
                        "model": model_name,
                        "is_final": False,
                        "chunk_id": chunk_count,
                        "finish_reason": None
                    }

                # Check for finish reason
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    candidate = chunk.candidates[0]
                    if hasattr(candidate, 'finish_reason'):
                        finish_reason = str(candidate.finish_reason)

        except Exception as e:
            logger.error(f"Error in chat streaming response: {str(e)}")
            finish_reason = "error"

        # Always send final chunk with completion info
        yield {
            "message": {
                "role": "assistant",
                "content": ""
            },
            "model": model_name,
            "is_final": True,
            "chunk_id": chunk_count + 1,
            "finish_reason": finish_reason or "stop",
            "total_chunks": chunk_count,
            "total_content_length": len(total_content)
        }

    async def chat_completion_async(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
    ) -> Union[Dict[str, Any], AsyncIterator[Dict[str, Any]]]:
        """Generate chat completion using the AI model asynchronously.

        Args:
            messages: List of chat messages
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation
            stream: Whether to stream the response

        Returns:
            Dictionary containing generated message and usage information,
            or an async iterator of dictionaries if streaming
        """
        model_name = model or settings.default_model
        logger.info(f"Generating chat completion asynchronously with model {model_name}")

        # Create a chat session
        chat = self.client.aio.chats.create(model=model_name)

        # Set system instruction if provided
        if system_instruction:
            chat.system_instruction = system_instruction

        # Process previous messages to build context
        history = []
        for msg in messages[:-1]:  # All messages except the last one
            if msg["role"] == "user":
                history.append({"role": "user", "parts": [msg["content"]]})
            elif msg["role"] == "assistant":
                history.append({"role": "model", "parts": [msg["content"]]})

        # Set chat history if there are previous messages
        if history:
            chat.history = history

        # Get the last user message
        last_message = messages[-1]["content"]

        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            if stream:
                # Return a streaming response
                response_stream = await chat.send_message(last_message, config=config, stream=True)
                return self._process_chat_stream_async(response_stream, model_name)
            else:
                # Send the message and get the response
                response = await chat.send_message(last_message, config=config)

                # Extract token usage if available
                usage = {}
                if hasattr(response, 'usage_metadata'):
                    usage = {
                        'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                        'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                        'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                    }

                finish_reason = None
                if response.candidates and hasattr(response.candidates[0], 'finish_reason'):
                    finish_reason = response.candidates[0].finish_reason

                return {
                    "message": {
                        "role": "assistant",
                        "content": response.text
                    },
                    "model": model_name,
                    "usage": usage,
                    "finish_reason": finish_reason
                }
        except Exception as e:
            logger.error(f"Error generating chat completion asynchronously: {str(e)}")
            raise

    async def _process_chat_stream_async(self, response_stream, model_name: str) -> AsyncIterator[Dict[str, Any]]:
        """Process a streaming chat response asynchronously with proper termination.

        Args:
            response_stream: Streaming response from the API
            model_name: Name of the model used

        Yields:
            Dictionary containing chunk of generated message
        """
        chunk_count = 0
        total_content = ""
        finish_reason = None

        try:
            async for chunk in response_stream:
                # Check if the chunk has text content
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_count += 1
                    total_content += chunk.text

                    yield {
                        "message": {
                            "role": "assistant",
                            "content": chunk.text
                        },
                        "model": model_name,
                        "is_final": False,
                        "chunk_id": chunk_count,
                        "finish_reason": None
                    }

                # Check for finish reason
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    candidate = chunk.candidates[0]
                    if hasattr(candidate, 'finish_reason'):
                        finish_reason = str(candidate.finish_reason)

        except Exception as e:
            logger.error(f"Error in async chat streaming response: {str(e)}")
            finish_reason = "error"

        # Always send final chunk with completion info
        yield {
            "message": {
                "role": "assistant",
                "content": ""
            },
            "model": model_name,
            "is_final": True,
            "chunk_id": chunk_count + 1,
            "finish_reason": finish_reason or "stop",
            "total_chunks": chunk_count,
            "total_content_length": len(total_content)
        }

    def multimodal_generation(
        self,
        text_prompt: Optional[str] = None,
        image_parts: Optional[List[Union[str, bytes, Dict[str, Any]]]] = None,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
    ) -> Union[Dict[str, Any], Iterator[Dict[str, Any]]]:
        """Generate content using multimodal inputs (text + images).

        Args:
            text_prompt: Optional text prompt for generation
            image_parts: List of image inputs (file paths, bytes, or URLs)
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation
            stream: Whether to stream the response

        Returns:
            Dictionary containing generated text and usage information,
            or an iterator of dictionaries if streaming

        Raises:
            ValidationError: If input parameters are invalid
            ModelError: If model is not available
            APIError: If API call fails
        """
        # Validate inputs
        if not text_prompt and not image_parts:
            raise ValidationError("Either text_prompt or image_parts must be provided")

        if text_prompt:
            text_prompt = RequestValidator.validate_prompt(text_prompt)

        model_name = model or settings.default_model
        if model_name not in settings.available_models:
            raise ModelError(f"Model {model_name} is not available", model_name=model_name)

        # Validate generation parameters
        validated_params = RequestValidator.validate_generation_params(
            temperature=temperature, top_p=top_p, top_k=top_k, max_tokens=max_tokens
        )

        logger.info(f"Generating multimodal content with model {model_name}")

        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Process image parts and add them to contents
        parts = []

        # Add text prompt if provided
        if text_prompt:
            parts.append({"text": text_prompt})

        # Process and add image parts if provided
        if image_parts:
            parts.extend(self._process_image_parts(image_parts))

        # Create contents
        contents = []
        if system_instruction:
            contents.append(types.Content(
                role="user",
                parts=[{"text": system_instruction}]
            ))

        # Add main content with text and images
        contents.append(types.Content(role="user", parts=parts))

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            if stream:
                # Return a streaming response
                response_stream = self.client.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config,
                    stream=True
                )

                return self._process_text_stream(response_stream, model_name)
            else:
                # Return a complete response
                response = self.client.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config
                )

                # Extract token usage if available
                usage = {}
                if hasattr(response, 'usage_metadata'):
                    usage = {
                        'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                        'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                        'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                    }

                finish_reason = None
                if response.candidates and hasattr(response.candidates[0], 'finish_reason'):
                    finish_reason = response.candidates[0].finish_reason

                return {
                    "text": response.text,
                    "model": model_name,
                    "usage": usage,
                    "finish_reason": finish_reason
                }
        except (ValidationError, ModelError) as e:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Convert other exceptions to APIError
            raise handle_api_exception(e, "multimodal_generation")

    def _process_image_parts(self, image_parts: List[Union[str, bytes, Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """Process image parts into the correct format for the API.

        Args:
            image_parts: List of image inputs (file paths, bytes, URLs, or dicts)

        Returns:
            List of processed image parts

        Raises:
            ValidationError: If image format is invalid
            FileError: If file cannot be read
        """
        processed_parts = []

        for i, img in enumerate(image_parts):
            try:
                if isinstance(img, dict):
                    # Handle different dict formats
                    if 'type' in img and img['type'] == 'base64':
                        # Base64 encoded image
                        mime_type = img.get('mime_type', 'image/jpeg')
                        data = img.get('data', '')

                        if not data:
                            raise ValidationError(f"Image {i}: Base64 data is empty")

                        processed_parts.append({
                            "inline_data": {
                                "data": data,
                                "mime_type": mime_type
                            }
                        })
                    elif 'file_uri' in img:
                        # File URI from uploaded file
                        processed_parts.append({
                            "file_data": {
                                "file_uri": img['file_uri'],
                                "mime_type": img.get('mime_type', 'image/jpeg')
                            }
                        })
                    else:
                        raise ValidationError(f"Image {i}: Invalid dict format")

                elif isinstance(img, str):
                    # Handle string inputs (URLs or file paths)
                    if img.startswith(('http://', 'https://')):
                        # It's a URL, download the image
                        try:
                            response = requests.get(img, timeout=30)
                            response.raise_for_status()
                            image_bytes = response.content

                            # Detect MIME type from content
                            mime_type = self._detect_image_mime_type(image_bytes, img)

                            processed_parts.append({
                                "inline_data": {
                                    "data": base64.b64encode(image_bytes).decode("utf-8"),
                                    "mime_type": mime_type
                                }
                            })
                        except requests.RequestException as e:
                            raise ValidationError(f"Image {i}: Failed to download from URL: {str(e)}")
                    else:
                        # It's a file path, read the file
                        if not os.path.exists(img):
                            raise FileError(f"Image file not found: {img}", file_path=img)

                        try:
                            with open(img, 'rb') as f:
                                image_bytes = f.read()

                            # Detect MIME type from file extension and content
                            mime_type = self._detect_image_mime_type(image_bytes, img)

                            processed_parts.append({
                                "inline_data": {
                                    "data": base64.b64encode(image_bytes).decode("utf-8"),
                                    "mime_type": mime_type
                                }
                            })
                        except IOError as e:
                            raise FileError(f"Failed to read image file: {str(e)}", file_path=img)

                elif isinstance(img, bytes):
                    # It's already bytes
                    if len(img) == 0:
                        raise ValidationError(f"Image {i}: Empty bytes data")

                    # Detect MIME type from content
                    mime_type = self._detect_image_mime_type(img)

                    processed_parts.append({
                        "inline_data": {
                            "data": base64.b64encode(img).decode("utf-8"),
                            "mime_type": mime_type
                        }
                    })
                else:
                    raise ValidationError(f"Image {i}: Unsupported format {type(img)}")

            except (ValidationError, FileError) as e:
                # Re-raise our custom exceptions
                raise
            except Exception as e:
                raise ValidationError(f"Image {i}: Processing failed: {str(e)}")

        return processed_parts

    def _detect_image_mime_type(self, image_bytes: bytes, filename: str = None) -> str:
        """Detect MIME type of image from bytes and filename.

        Args:
            image_bytes: Image data as bytes
            filename: Optional filename for extension-based detection

        Returns:
            MIME type string
        """
        # Check magic bytes for common image formats
        if image_bytes.startswith(b'\xff\xd8\xff'):
            return 'image/jpeg'
        elif image_bytes.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'image/png'
        elif image_bytes.startswith(b'GIF87a') or image_bytes.startswith(b'GIF89a'):
            return 'image/gif'
        elif image_bytes.startswith(b'RIFF') and b'WEBP' in image_bytes[:12]:
            return 'image/webp'

        # Fallback to filename extension
        if filename:
            ext = os.path.splitext(filename.lower())[1]
            mime_map = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.webp': 'image/webp'
            }
            if ext in mime_map:
                return mime_map[ext]

        # Default fallback
        return 'image/jpeg'

    async def _process_multimodal_stream_async(self, response_stream, model_name: str) -> AsyncIterator[Dict[str, Any]]:
        """Process a streaming multimodal response asynchronously.

        Args:
            response_stream: Async stream from the API
            model_name: Name of the model being used

        Yields:
            Dictionary containing chunk data
        """
        chunk_count = 0
        total_content = ""
        finish_reason = None

        try:
            async for chunk in response_stream:
                chunk_count += 1

                # Extract text from chunk
                chunk_text = ""
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_text = chunk.text
                    total_content += chunk_text

                # Yield chunk data
                if chunk_text:
                    yield {
                        "text": chunk_text,
                        "model": model_name,
                        "is_final": False,
                        "chunk_id": chunk_count,
                        "finish_reason": None
                    }

                # Check for finish reason
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    candidate = chunk.candidates[0]
                    if hasattr(candidate, 'finish_reason'):
                        finish_reason = str(candidate.finish_reason)

        except Exception as e:
            logger.error(f"Error in multimodal streaming response: {str(e)}")
            finish_reason = "error"

        # Always send final chunk with completion info
        yield {
            "text": "",
            "model": model_name,
            "is_final": True,
            "chunk_id": chunk_count + 1,
            "finish_reason": finish_reason or "stop",
            "total_chunks": chunk_count,
            "total_content_length": len(total_content)
        }

    def _process_multimodal_stream_sync(self, response_stream, model_name: str) -> AsyncIterator[Dict[str, Any]]:
        """Process a synchronous streaming multimodal response for async iteration.

        Args:
            response_stream: Synchronous stream from the API
            model_name: Name of the model being used

        Yields:
            Dictionary containing chunk data
        """
        async def async_wrapper():
            chunk_count = 0
            total_content = ""
            finish_reason = None

            try:
                # Process synchronous stream in async context
                for chunk in response_stream:
                    chunk_count += 1

                    # Extract text from chunk
                    chunk_text = ""
                    if hasattr(chunk, 'text') and chunk.text:
                        chunk_text = chunk.text
                        total_content += chunk_text

                    # Yield chunk data
                    if chunk_text:
                        yield {
                            "text": chunk_text,
                            "model": model_name,
                            "is_final": False,
                            "chunk_id": chunk_count,
                            "finish_reason": None
                        }

                    # Check for finish reason
                    if hasattr(chunk, 'candidates') and chunk.candidates:
                        candidate = chunk.candidates[0]
                        if hasattr(candidate, 'finish_reason'):
                            finish_reason = str(candidate.finish_reason)

            except Exception as e:
                logger.error(f"Error in multimodal streaming response: {str(e)}")
                finish_reason = "error"

            # Always send final chunk with completion info
            yield {
                "text": "",
                "model": model_name,
                "is_final": True,
                "chunk_id": chunk_count + 1,
                "finish_reason": finish_reason or "stop",
                "total_chunks": chunk_count,
                "total_content_length": len(total_content)
            }

        return async_wrapper()

    async def _simulate_multimodal_streaming(self, response, model_name: str) -> AsyncIterator[Dict[str, Any]]:
        """Simulate streaming for multimodal content by chunking the complete response.

        Args:
            response: Complete response from the API
            model_name: Name of the model being used

        Yields:
            Dictionary containing chunk data
        """
        import asyncio

        try:
            # Extract the complete text from response
            complete_text = response.text if hasattr(response, 'text') and response.text else ""

            if not complete_text:
                # If no text, send empty final chunk
                yield {
                    "text": "",
                    "model": model_name,
                    "is_final": True,
                    "chunk_id": 1,
                    "finish_reason": "stop",
                    "total_chunks": 1,
                    "total_content_length": 0
                }
                return

            # Split text into chunks (15-20 characters per chunk for smooth streaming)
            chunk_size = 15
            text_chunks = []

            # Split by words to avoid breaking words in the middle
            words = complete_text.split()
            current_chunk = ""

            for word in words:
                if len(current_chunk + " " + word) <= chunk_size:
                    current_chunk += (" " + word) if current_chunk else word
                else:
                    if current_chunk:
                        text_chunks.append(current_chunk)
                    current_chunk = word

            # Add the last chunk
            if current_chunk:
                text_chunks.append(current_chunk)

            # If no chunks created, create one with the complete text
            if not text_chunks:
                text_chunks = [complete_text]

            total_chunks = len(text_chunks)

            # Stream each chunk with a small delay
            for i, chunk_text in enumerate(text_chunks):
                chunk_id = i + 1
                is_final = (chunk_id == total_chunks)

                yield {
                    "text": chunk_text,
                    "model": model_name,
                    "is_final": False,
                    "chunk_id": chunk_id,
                    "finish_reason": None
                }

                # Add small delay to simulate real-time streaming (75ms)
                if not is_final:
                    await asyncio.sleep(0.075)

            # Send final chunk with completion info
            finish_reason = "stop"
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'finish_reason'):
                    finish_reason = str(candidate.finish_reason)

            yield {
                "text": "",
                "model": model_name,
                "is_final": True,
                "chunk_id": total_chunks + 1,
                "finish_reason": finish_reason,
                "total_chunks": total_chunks,
                "total_content_length": len(complete_text)
            }

        except Exception as e:
            logger.error(f"Error in simulated multimodal streaming: {str(e)}", exc_info=True)
            # Send error final chunk with more details
            yield {
                "text": "",
                "model": model_name,
                "is_final": True,
                "chunk_id": 1,
                "finish_reason": "error",
                "total_chunks": 0,
                "total_content_length": 0,
                "error": str(e),
                "error_type": type(e).__name__,
                "progress": 100.0
            }

    async def multimodal_generation_async(
        self,
        text_prompt: Optional[str] = None,
        image_parts: Optional[List[Union[str, bytes, Dict[str, Any]]]] = None,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
    ) -> Union[Dict[str, Any], AsyncIterator[Dict[str, Any]]]:
        """Generate content using multimodal inputs (text + images) asynchronously.

        Args:
            text_prompt: Optional text prompt for generation
            image_parts: List of image inputs (file paths, bytes, or URLs)
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation
            stream: Whether to stream the response

        Returns:
            Dictionary containing generated text and usage information,
            or an async iterator of dictionaries if streaming
        """
        model_name = model or settings.default_model
        logger.info(f"Generating multimodal content asynchronously with model {model_name}")

        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Create contents with system instruction if provided
        contents = []
        if system_instruction:
            contents.append(types.Content(
                role="user",
                parts=[{"text": system_instruction}]
            ))

        # Process image parts and add them to contents
        parts = []

        # Add text prompt if provided
        if text_prompt:
            parts.append({"text": text_prompt})

        # Process and add image parts if provided
        if image_parts:
            parts.extend(self._process_image_parts(image_parts))

        # Add parts to contents
        contents.append(types.Content(role="user", parts=parts))

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            if stream:
                # Google Gemini API doesn't support true streaming for multimodal content
                # Implement simulated streaming by getting complete response and chunking it
                logger.info("Using simulated streaming for multimodal content")

                # Get complete response first with retry mechanism
                @with_aretry(max_retries=3, base_delay=1.0)
                async def _get_response():
                    try:
                        return await api_circuit_breaker.acall(
                            self.client.aio.models.generate_content,
                            model=model_name,
                            contents=contents,
                            config=config
                        )
                    except Exception as e:
                        # Convert known errors to retryable errors
                        error_str = str(e).lower()
                        if any(pattern in error_str for pattern in ["rate limit", "quota", "503", "502", "timeout"]):
                            raise RateLimitError(f"Rate limit or service error: {e}")
                        elif any(pattern in error_str for pattern in ["network", "connection", "temporary"]):
                            raise TemporaryError(f"Temporary error: {e}")
                        else:
                            raise e

                response = await _get_response()
                # Return simulated streaming response
                return self._simulate_multimodal_streaming(response, model_name)
            else:
                # Return a complete response with retry mechanism
                @with_aretry(max_retries=3, base_delay=1.0)
                async def _get_response():
                    try:
                        return await api_circuit_breaker.acall(
                            self.client.aio.models.generate_content,
                            model=model_name,
                            contents=contents,
                            config=config
                        )
                    except Exception as e:
                        # Convert known errors to retryable errors
                        error_str = str(e).lower()
                        if any(pattern in error_str for pattern in ["rate limit", "quota", "503", "502", "timeout"]):
                            raise RateLimitError(f"Rate limit or service error: {e}")
                        elif any(pattern in error_str for pattern in ["network", "connection", "temporary"]):
                            raise TemporaryError(f"Temporary error: {e}")
                        else:
                            raise e

                response = await _get_response()

                # Extract token usage if available
                usage = {}
                if hasattr(response, 'usage_metadata'):
                    usage = {
                        'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                        'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                        'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                    }

                finish_reason = None
                if response.candidates and hasattr(response.candidates[0], 'finish_reason'):
                    finish_reason = response.candidates[0].finish_reason

                return {
                    "text": response.text,
                    "model": model_name,
                    "usage": usage,
                    "finish_reason": finish_reason
                }
        except (RateLimitError, TemporaryError) as e:
            logger.error(f"Retryable error in multimodal generation: {str(e)}")
            raise APIError(f"Service temporarily unavailable: {str(e)}")
        except Exception as e:
            logger.error(f"Error generating multimodal content asynchronously: {str(e)}", exc_info=True)
            raise handle_api_exception(e, "multimodal_generation_async")

    def function_calling(
        self,
        prompt: str,
        functions: List[Dict[str, Any]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
    ) -> Dict[str, Any]:
        """Generate content with function calling capabilities.

        Args:
            prompt: Text prompt for generation
            functions: List of function declarations
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation

        Returns:
            Dictionary containing generated text, function call information, and usage information
        """
        model_name = model or settings.default_model
        logger.info(f"Generating content with function calling using model {model_name}")

        # Create function declarations with proper validation
        function_declarations = []
        for func in functions:
            # Validate function structure
            if not isinstance(func, dict) or "name" not in func:
                logger.error(f"Invalid function definition: {func}")
                raise ValueError(f"Function must be a dictionary with 'name' field")

            # Ensure parameters have correct JSON Schema format
            parameters = func.get("parameters", {})
            if not isinstance(parameters, dict):
                parameters = {"type": "object", "properties": {}, "required": []}

            # Validate and fix required JSON Schema fields
            if "type" not in parameters:
                parameters["type"] = "object"
            if "properties" not in parameters:
                parameters["properties"] = {}
            if "required" not in parameters:
                parameters["required"] = []

            function_declarations.append(types.FunctionDeclaration(
                name=func["name"],
                description=func.get("description", ""),
                parameters=parameters
            ))

            logger.debug(f"Created function declaration: {func['name']} with parameters: {parameters}")

        # Create tools with function declarations
        tools = [types.Tool(function_declarations=function_declarations)]
        logger.info(f"Created {len(function_declarations)} function declarations for function calling")

        # Create generation config
        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            tools=tools
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Create contents with system instruction if provided
        contents = []
        if system_instruction:
            contents.append(types.Content(
                role="user",
                parts=[{"text": system_instruction}]
            ))

        # Add the prompt
        contents.append(types.Content(
            role="user",
            parts=[{"text": prompt}]
        ))

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            # Generate content with function calling
            response = self.client.models.generate_content(
                model=model_name,
                contents=contents,
                config=config
            )

            # Extract token usage if available
            usage = {}
            if hasattr(response, 'usage_metadata'):
                usage = {
                    'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                    'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                    'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                }

            # Check if there's a function call with improved error handling
            function_call = None
            response_text = None

            try:
                # Try to get response text first
                if hasattr(response, 'text') and response.text:
                    response_text = response.text
                    logger.debug(f"Got response text: {response_text[:100]}...")

                # Check for function calls in response candidates
                if hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]
                    logger.debug(f"Processing candidate with content: {hasattr(candidate, 'content')}")

                    if hasattr(candidate, 'content') and candidate.content:
                        if hasattr(candidate.content, 'parts') and candidate.content.parts:
                            for i, part in enumerate(candidate.content.parts):
                                logger.debug(f"Processing part {i}: {type(part)}")

                                # Check for function call in this part
                                if hasattr(part, 'function_call') and part.function_call:
                                    function_call_obj = part.function_call
                                    function_call = {
                                        "name": function_call_obj.name,
                                        "args": dict(function_call_obj.args) if function_call_obj.args else {}
                                    }
                                    logger.info(f"Found function call: {function_call['name']} with args: {function_call['args']}")
                                    break

                                # Check for text content in this part
                                elif hasattr(part, 'text') and part.text and not response_text:
                                    response_text = part.text
                                    logger.debug(f"Found text in part: {response_text[:100]}...")
                        else:
                            logger.debug("No parts found in candidate content")
                    else:
                        logger.debug("No content found in candidate")
                else:
                    logger.debug("No candidates found in response")

            except Exception as e:
                logger.error(f"Error processing function calling response: {str(e)}")
                # Continue with what we have

            # Log the final result
            if function_call:
                logger.info(f"Function calling completed with function call: {function_call['name']}")
            elif response_text:
                logger.info(f"Function calling completed with text response: {response_text[:100]}...")
            else:
                logger.warning("Function calling completed but no function call or text found")

            return {
                "text": response_text if not function_call else None,
                "function_call": function_call,
                "model": model_name,
                "usage": usage
            }
        except Exception as e:
            logger.error(f"Error generating content with function calling: {str(e)}")
            raise

    async def function_calling_async(
        self,
        prompt: str,
        functions: List[Dict[str, Any]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
    ) -> Dict[str, Any]:
        """Generate content with function calling capabilities asynchronously.

        Args:
            prompt: Text prompt for generation
            functions: List of function declarations
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation

        Returns:
            Dictionary containing generated text, function call information, and usage information
        """
        model_name = model or settings.default_model
        logger.info(f"Generating content with function calling asynchronously using model {model_name}")

        # Create function declarations with proper validation
        function_declarations = []
        for func in functions:
            # Validate function structure
            if not isinstance(func, dict) or "name" not in func:
                logger.error(f"Invalid function definition: {func}")
                raise ValueError(f"Function must be a dictionary with 'name' field")

            # Ensure parameters have correct JSON Schema format
            parameters = func.get("parameters", {})
            if not isinstance(parameters, dict):
                parameters = {"type": "object", "properties": {}, "required": []}

            # Validate and fix required JSON Schema fields
            if "type" not in parameters:
                parameters["type"] = "object"
            if "properties" not in parameters:
                parameters["properties"] = {}
            if "required" not in parameters:
                parameters["required"] = []

            function_declarations.append(types.FunctionDeclaration(
                name=func["name"],
                description=func.get("description", ""),
                parameters=parameters
            ))

            logger.debug(f"Created function declaration: {func['name']} with parameters: {parameters}")

        # Create tools with function declarations
        tools = [types.Tool(function_declarations=function_declarations)]
        logger.info(f"Created {len(function_declarations)} function declarations for function calling")

        # Create generation config
        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            tools=tools
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Create contents with system instruction if provided
        contents = []
        if system_instruction:
            contents.append(types.Content(
                role="user",
                parts=[{"text": system_instruction}]
            ))

        # Add the prompt
        contents.append(types.Content(
            role="user",
            parts=[{"text": prompt}]
        ))

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            # Generate content with function calling asynchronously
            response = await self.client.aio.models.generate_content(
                model=model_name,
                contents=contents,
                config=config
            )

            # Extract token usage if available
            usage = {}
            if hasattr(response, 'usage_metadata'):
                usage = {
                    'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                    'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                    'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                }

            # Check if there's a function call with improved error handling
            function_call = None
            response_text = None

            try:
                # Try to get response text first
                if hasattr(response, 'text') and response.text:
                    response_text = response.text
                    logger.debug(f"Got response text: {response_text[:100]}...")

                # Check for function calls in response candidates
                if hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]
                    logger.debug(f"Processing candidate with content: {hasattr(candidate, 'content')}")

                    if hasattr(candidate, 'content') and candidate.content:
                        if hasattr(candidate.content, 'parts') and candidate.content.parts:
                            for i, part in enumerate(candidate.content.parts):
                                logger.debug(f"Processing part {i}: {type(part)}")

                                # Check for function call in this part
                                if hasattr(part, 'function_call') and part.function_call:
                                    function_call_obj = part.function_call
                                    function_call = {
                                        "name": function_call_obj.name,
                                        "args": dict(function_call_obj.args) if function_call_obj.args else {}
                                    }
                                    logger.info(f"Found function call: {function_call['name']} with args: {function_call['args']}")
                                    break

                                # Check for text content in this part
                                elif hasattr(part, 'text') and part.text and not response_text:
                                    response_text = part.text
                                    logger.debug(f"Found text in part: {response_text[:100]}...")
                        else:
                            logger.debug("No parts found in candidate content")
                    else:
                        logger.debug("No content found in candidate")
                else:
                    logger.debug("No candidates found in response")

            except Exception as e:
                logger.error(f"Error processing function calling response: {str(e)}")
                # Continue with what we have

            # Log the final result
            if function_call:
                logger.info(f"Function calling completed with function call: {function_call['name']}")
            elif response_text:
                logger.info(f"Function calling completed with text response: {response_text[:100]}...")
            else:
                logger.warning("Function calling completed but no function call or text found")

            return {
                "text": response_text if not function_call else None,
                "function_call": function_call,
                "model": model_name,
                "usage": usage
            }
        except Exception as e:
            logger.error(f"Error generating content with function calling asynchronously: {str(e)}")
            raise

    def function_response(
        self,
        function_call: Dict[str, Any],
        function_response: Dict[str, Any],
        chat_history: List[Dict[str, Any]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
    ) -> Dict[str, Any]:
        """Process function response and continue the conversation.

        Args:
            function_call: Function call information
            function_response: Function response data
            chat_history: Previous chat history
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation

        Returns:
            Dictionary containing generated text and usage information
        """
        model_name = model or settings.default_model
        logger.info(f"Processing function response with model {model_name}")

        # Create a chat session
        chat = self.client.chats.create(model=model_name)

        # Set system instruction if provided
        if system_instruction:
            chat.system_instruction = system_instruction

        # Process chat history to build context
        history = []
        for msg in chat_history:
            if msg["role"] == "user":
                history.append({"role": "user", "parts": [msg["content"]]})
            elif msg["role"] == "assistant":
                if "content" in msg and msg["content"]:
                    history.append({"role": "model", "parts": [msg["content"]]})
                elif "function_call" in msg:
                    # Add function call to history
                    function_call_part = types.Part(
                        function_call=types.FunctionCall(
                            name=msg["function_call"]["name"],
                            args=msg["function_call"]["args"]
                        )
                    )
                    history.append({"role": "model", "parts": [function_call_part]})
            elif msg["role"] == "function":
                # Add function response to history
                function_response_part = types.Part.from_function_response(
                    name=msg["name"],
                    response=msg["content"]
                )
                history.append({"role": "user", "parts": [function_response_part]})

        # Set chat history
        chat.history = history

        # Create function response part
        function_response_part = types.Part.from_function_response(
            name=function_call["name"],
            response=function_response
        )

        # Create generation config
        config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
        )

        if max_tokens:
            config.max_output_tokens = max_tokens

        # Add safety settings if provided
        if safety_settings:
            config.safety_settings = [
                types.SafetySetting(
                    category=setting["category"],
                    threshold=setting["threshold"]
                )
                for setting in safety_settings
            ]

        try:
            # Send function response and get model response
            response = chat.send_message(function_response_part, config=config)

            # Extract token usage if available
            usage = {}
            if hasattr(response, 'usage_metadata'):
                usage = {
                    'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                    'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                    'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0)
                }

            return {
                "text": response.text,
                "model": model_name,
                "usage": usage
            }
        except Exception as e:
            logger.error(f"Error processing function response: {str(e)}")
            raise

    def generate_embeddings(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None,
        task_type: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate embeddings for text.

        Args:
            text: Text or list of texts to generate embeddings for
            model: Model to use for embedding generation
            task_type: Task type for the embedding

        Returns:
            Dictionary containing generated embeddings
        """
        model_name = model or "gemini-embedding-exp-03-07"
        logger.info(f"Generating embeddings with model {model_name}")

        config = {}
        if task_type:
            config["task_type"] = task_type

        try:
            # Generate embeddings
            if isinstance(text, list):
                # Batch embedding
                response = self.client.models.embed_content(
                    model=model_name,
                    contents=text,
                    config=config
                )

                return {
                    "embeddings": response.embeddings,
                    "model": model_name
                }
            else:
                # Single text embedding
                response = self.client.models.embed_content(
                    model=model_name,
                    contents=text,
                    config=config
                )

                # Extract the embedding values from the response
                embedding_data = response.embeddings[0]
                if hasattr(embedding_data, 'values'):
                    embedding_values = embedding_data.values
                else:
                    embedding_values = embedding_data

                return {
                    "embedding": embedding_values,
                    "model": model_name
                }
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise

    async def generate_embeddings_async(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None,
        task_type: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate embeddings for text asynchronously.

        Args:
            text: Text or list of texts to generate embeddings for
            model: Model to use for embedding generation
            task_type: Task type for the embedding

        Returns:
            Dictionary containing generated embeddings
        """
        model_name = model or "gemini-embedding-exp-03-07"
        logger.info(f"Generating embeddings asynchronously with model {model_name}")

        config = {}
        if task_type:
            config["task_type"] = task_type

        try:
            # Generate embeddings
            if isinstance(text, list):
                # Batch embedding
                response = await self.client.aio.models.embed_content(
                    model=model_name,
                    contents=text,
                    config=config
                )

                return {
                    "embeddings": response.embeddings,
                    "model": model_name
                }
            else:
                # Single text embedding
                response = await self.client.aio.models.embed_content(
                    model=model_name,
                    contents=text,
                    config=config
                )

                # Extract the embedding values from the response
                embedding_data = response.embeddings[0]
                if hasattr(embedding_data, 'values'):
                    embedding_values = embedding_data.values
                else:
                    embedding_values = embedding_data

                return {
                    "embedding": embedding_values,
                    "model": model_name
                }
        except Exception as e:
            logger.error(f"Error generating embeddings asynchronously: {str(e)}")
            raise

    def count_tokens(
        self,
        text: str,
        model: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Count tokens in text.

        Args:
            text: Text to count tokens for
            model: Model to use for token counting

        Returns:
            Dictionary containing token count
        """
        model_name = model or settings.default_model
        logger.info(f"Counting tokens with model {model_name}")

        try:
            # Count tokens
            response = self.client.models.count_tokens(
                model=model_name,
                contents=text
            )

            return {
                "token_count": response.total_tokens,
                "model": model_name
            }
        except Exception as e:
            logger.error(f"Error counting tokens: {str(e)}")
            raise

    def upload_file(
        self,
        file_path: str,
        mime_type: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Upload a file to a local storage and return file information.

        Note: Google Gemini API doesn't have a direct file upload API.
        This method stores the file locally and returns information about it.

        Args:
            file_path: Path to the file to upload
            mime_type: MIME type of the file

        Returns:
            Dictionary containing file information
        """
        logger.info(f"Uploading file {file_path}")

        # Determine MIME type if not provided
        if not mime_type:
            mime_type = mimetypes.guess_type(file_path)[0]
            if not mime_type:
                if file_path.endswith(('.jpg', '.jpeg')):
                    mime_type = "image/jpeg"
                elif file_path.endswith('.png'):
                    mime_type = "image/png"
                elif file_path.endswith('.gif'):
                    mime_type = "image/gif"
                elif file_path.endswith('.pdf'):
                    mime_type = "application/pdf"
                else:
                    mime_type = "application/octet-stream"

        try:
            # Generate a unique file ID
            import uuid
            file_id = str(uuid.uuid4())

            # Create storage directory if it doesn't exist
            storage_dir = os.path.join(settings.temp_file_dir, "uploads")
            os.makedirs(storage_dir, exist_ok=True)

            # Copy the file to storage
            import shutil
            file_name = os.path.basename(file_path)
            stored_path = os.path.join(storage_dir, f"{file_id}_{file_name}")
            shutil.copy2(file_path, stored_path)

            # Get file size
            file_size = os.path.getsize(stored_path)

            # Create file metadata
            file_info = {
                "file_id": file_id,
                "file_path": stored_path,
                "file_name": file_name,
                "mime_type": mime_type,
                "size_bytes": file_size
            }

            # Store file metadata
            metadata_path = os.path.join(storage_dir, f"{file_id}.json")
            with open(metadata_path, 'w') as f:
                json.dump(file_info, f)

            return file_info
        except Exception as e:
            logger.error(f"Error uploading file: {str(e)}")
            raise

    def get_file(
        self,
        file_id: str,
    ) -> Dict[str, Any]:
        """Get information about a file.

        Args:
            file_id: ID of the file to get information about

        Returns:
            Dictionary containing file information
        """
        logger.info(f"Getting file information for {file_id}")

        try:
            # Get file metadata
            storage_dir = os.path.join(settings.temp_file_dir, "uploads")
            metadata_path = os.path.join(storage_dir, f"{file_id}.json")

            if not os.path.exists(metadata_path):
                raise FileNotFoundError(f"File with ID {file_id} not found")

            with open(metadata_path, 'r') as f:
                file_info = json.load(f)

            return file_info
        except Exception as e:
            logger.error(f"Error getting file information: {str(e)}")
            raise

    def list_files(self) -> Dict[str, Any]:
        """List all files.

        Returns:
            Dictionary containing list of files
        """
        logger.info("Listing files")

        try:
            # Get all metadata files
            storage_dir = os.path.join(settings.temp_file_dir, "uploads")

            # Create directory if it doesn't exist
            os.makedirs(storage_dir, exist_ok=True)

            files_info = []

            # List all JSON files in the directory
            for filename in os.listdir(storage_dir):
                if filename.endswith('.json'):
                    metadata_path = os.path.join(storage_dir, filename)
                    try:
                        with open(metadata_path, 'r') as f:
                            file_info = json.load(f)
                            files_info.append(file_info)
                    except Exception as e:
                        logger.warning(f"Error reading metadata file {filename}: {str(e)}")
                        continue

            return {
                "files": files_info
            }
        except Exception as e:
            logger.error(f"Error listing files: {str(e)}")
            raise

    def delete_file(
        self,
        file_id: str,
    ) -> Dict[str, Any]:
        """Delete a file.

        Args:
            file_id: ID of the file to delete

        Returns:
            Dictionary containing deletion status
        """
        logger.info(f"Deleting file {file_id}")

        try:
            # Get file metadata first
            storage_dir = os.path.join(settings.temp_file_dir, "uploads")
            metadata_path = os.path.join(storage_dir, f"{file_id}.json")

            if not os.path.exists(metadata_path):
                raise FileNotFoundError(f"File with ID {file_id} not found")

            # Load metadata to get file path
            with open(metadata_path, 'r') as f:
                file_info = json.load(f)

            # Delete the actual file
            if os.path.exists(file_info["file_path"]):
                os.remove(file_info["file_path"])

            # Delete the metadata file
            os.remove(metadata_path)

            return {
                "success": True,
                "file_id": file_id
            }
        except Exception as e:
            logger.error(f"Error deleting file: {str(e)}")
            raise

    def text_to_speech(
        self,
        text: str,
        voice: str = "en-US-Standard-A",
        language_code: str = "en-US",
        audio_format: str = "mp3",
        speaking_rate: float = 1.0,
        pitch: float = 0.0,
        volume_gain_db: float = 0.0,
        sample_rate: int = 22050,
        effects_profile_id: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Convert text to speech using Google Cloud Text-to-Speech API.

        Args:
            text: Text to convert to speech
            voice: Voice to use for synthesis
            language_code: Language code (e.g., en-US, vi-VN)
            audio_format: Audio format (mp3, wav, ogg)
            speaking_rate: Speaking rate (0.25 to 4.0)
            pitch: Pitch adjustment (-20.0 to 20.0)
            volume_gain_db: Volume gain in dB (-96.0 to 16.0)
            sample_rate: Audio sample rate in Hz
            effects_profile_id: Audio effects profile IDs

        Returns:
            Dictionary containing audio content and metadata
        """
        # Validate inputs
        if not text or not text.strip():
            raise ValidationError("Text cannot be empty", field="text")

        if len(text) > 5000:
            raise ValidationError("Text is too long for TTS", field="text",
                                details={"max_length": 5000, "actual_length": len(text)})

        if not (0.25 <= speaking_rate <= 4.0):
            raise ValidationError("Speaking rate must be between 0.25 and 4.0", field="speaking_rate")

        if not (-20.0 <= pitch <= 20.0):
            raise ValidationError("Pitch must be between -20.0 and 20.0", field="pitch")

        if not (-96.0 <= volume_gain_db <= 16.0):
            raise ValidationError("Volume gain must be between -96.0 and 16.0", field="volume_gain_db")

        logger.info(f"Converting text to speech: {text[:50]}... (voice: {voice}, language: {language_code})")

        try:
            # Try to use Google Cloud Text-to-Speech API if available
            audio_content = self._generate_real_tts_audio(
                text, voice, language_code, audio_format,
                speaking_rate, pitch, volume_gain_db, sample_rate, effects_profile_id
            )

            # Calculate approximate duration (rough estimate: 150 words per minute)
            words = len(text.split())
            duration_seconds = (words / 150) * 60 / speaking_rate

            return {
                "audio_content": audio_content,
                "audio_format": audio_format,
                "sample_rate": sample_rate,
                "duration_seconds": duration_seconds,
                "voice_used": voice,
                "language_code": language_code,
                "text_length": len(text)
            }

        except Exception as e:
            logger.warning(f"Real TTS failed, falling back to mock: {str(e)}")
            # Fallback to mock audio if real TTS fails
            audio_content = self._generate_mock_audio(text, audio_format, sample_rate)

            # Calculate approximate duration (rough estimate: 150 words per minute)
            words = len(text.split())
            duration_seconds = (words / 150) * 60 / speaking_rate

            return {
                "audio_content": audio_content,
                "audio_format": audio_format,
                "sample_rate": sample_rate,
                "duration_seconds": duration_seconds,
                "voice_used": voice,
                "language_code": language_code,
                "text_length": len(text),
                "is_mock": True  # Indicate this is mock audio
            }

    def _generate_real_tts_audio(
        self,
        text: str,
        voice: str,
        language_code: str,
        audio_format: str,
        speaking_rate: float,
        pitch: float,
        volume_gain_db: float,
        sample_rate: int,
        effects_profile_id: Optional[List[str]] = None
    ) -> bytes:
        """Generate real TTS audio using available TTS engines.

        Tries multiple TTS engines in order of preference:
        1. Google Cloud Text-to-Speech API (if available)
        2. pyttsx3 (offline TTS engine)
        3. gTTS (Google Translate TTS - online)

        Args:
            text: Text to convert to speech
            voice: Voice to use for synthesis
            language_code: Language code (e.g., en-US, vi-VN)
            audio_format: Audio format (mp3, wav, ogg)
            speaking_rate: Speaking rate (0.25 to 4.0)
            pitch: Pitch adjustment (-20.0 to 20.0)
            volume_gain_db: Volume gain in dB (-96.0 to 16.0)
            sample_rate: Audio sample rate in Hz
            effects_profile_id: Audio effects profile IDs

        Returns:
            Real audio content as bytes

        Raises:
            Exception: If all TTS engines fail
        """
        # Try Gemini Native TTS first (highest quality)
        try:
            return self._generate_gemini_native_tts(
                text, voice, language_code, audio_format,
                speaking_rate, pitch, volume_gain_db, sample_rate
            )
        except Exception as e:
            logger.debug(f"Gemini Native TTS failed: {e}")

        # Try Google Cloud TTS second
        try:
            return self._generate_google_cloud_tts(
                text, voice, language_code, audio_format,
                speaking_rate, pitch, volume_gain_db, sample_rate, effects_profile_id
            )
        except Exception as e:
            logger.debug(f"Google Cloud TTS failed: {e}")

        # Try gTTS (Google Translate TTS) as fallback
        try:
            return self._generate_gtts_audio(
                text, language_code, audio_format
            )
        except Exception as e:
            logger.debug(f"gTTS failed: {e}")

        # All TTS engines failed
        raise Exception("All TTS engines failed. Falling back to mock audio.")

    def _generate_gemini_native_tts(
        self,
        text: str,
        voice: str,
        language_code: str,
        audio_format: str,
        speaking_rate: float,
        pitch: float,
        volume_gain_db: float,
        sample_rate: int
    ) -> bytes:
        """Generate TTS audio using Gemini Native TTS API with new google-genai library.

        Uses Gemini 2.5 Flash Preview TTS for high-quality speech synthesis
        with streaming support and multi-speaker capabilities.

        Args:
            text: Text to convert to speech
            voice: Voice name (maps to Gemini prebuilt voices)
            language_code: Language code (auto-detected by Gemini)
            audio_format: Audio format (mp3, wav, ogg)
            speaking_rate: Speaking rate (converted to natural language prompt)
            pitch: Pitch adjustment (converted to natural language prompt)
            volume_gain_db: Volume gain (converted to natural language prompt)
            sample_rate: Sample rate (Gemini uses 24kHz by default)

        Returns:
            Real audio content as bytes from Gemini TTS

        Raises:
            Exception: If Gemini TTS API fails
        """
        try:
            import base64
            import mimetypes
            import struct
            from google import genai
            from google.genai import types

            # Configure Gemini API
            if not hasattr(self, '_gemini_api_key') or not self._gemini_api_key:
                raise Exception("Gemini API key not configured for TTS")

            # Initialize client with new google-genai library
            client = genai.Client(api_key=self._gemini_api_key)

            # Get optimal voice based on characteristics or direct mapping
            gemini_voice = self._select_optimal_voice(
                voice, language_code,
                getattr(self, '_current_voice_characteristics', None),
                getattr(self, '_current_emotional_tone', None)
            )

            # Create style prompt based on parameters
            style_prompt = self._create_style_prompt(speaking_rate, pitch, volume_gain_db, language_code)

            # Check for multi-speaker configuration
            speakers = getattr(self, '_current_speakers', None)

            if speakers and len(speakers) > 1:
                # Multi-speaker TTS
                return self._generate_multi_speaker_tts(
                    client, text, speakers, style_prompt, audio_format
                )
            else:
                # Single speaker TTS
                return self._generate_single_speaker_tts(
                    client, text, gemini_voice, style_prompt, audio_format
                )

        except ImportError:
            raise Exception("google-genai library not installed. Install with: pip install google-genai")
        except Exception as e:
            raise Exception(f"Gemini Native TTS error: {str(e)}")

    def _generate_single_speaker_tts(
        self,
        client,
        text: str,
        voice: str,
        style_prompt: str,
        audio_format: str
    ) -> bytes:
        """Generate single speaker TTS using new google-genai library."""
        from google.genai import types

        # Combine style with text
        full_prompt = f"{style_prompt}: {text}"

        # Create content
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=full_prompt),
                ],
            ),
        ]

        # Configure generation
        generate_content_config = types.GenerateContentConfig(
            temperature=1,
            response_modalities=["audio"],
            speech_config=types.SpeechConfig(
                voice_config=types.VoiceConfig(
                    prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name=voice
                    )
                ),
            ),
        )

        # Generate audio with streaming
        model = "gemini-2.5-flash-preview-tts"
        audio_chunks = []

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            if (chunk.candidates[0].content.parts[0].inline_data and
                chunk.candidates[0].content.parts[0].inline_data.data):

                inline_data = chunk.candidates[0].content.parts[0].inline_data
                audio_chunks.append(inline_data.data)

        if not audio_chunks:
            raise Exception("No audio data received from Gemini TTS")

        # Combine all audio chunks
        combined_audio = b''.join(audio_chunks)

        # Convert to desired format - default to MP3 for better compatibility
        if audio_format.lower() == 'wav':
            # Convert to WAV format
            combined_audio = self._convert_to_wav(combined_audio, "audio/L16;rate=24000")
        else:
            # Default to MP3 for better compatibility and smaller file size
            combined_audio = self._convert_pcm_to_mp3(combined_audio)

        logger.info(f"Successfully generated Gemini Native TTS audio: {len(combined_audio)} bytes")
        return combined_audio

    def _generate_multi_speaker_tts(
        self,
        client,
        text: str,
        speakers: list,
        style_prompt: str,
        audio_format: str
    ) -> bytes:
        """Generate multi-speaker TTS using new google-genai library."""
        from google.genai import types

        # Create speaker voice configurations
        speaker_voice_configs = []
        for speaker in speakers:
            speaker_name = speaker.get('speaker_name', f'Speaker {len(speaker_voice_configs) + 1}')
            voice_name = speaker.get('voice', 'Aoede')  # Default voice

            # Map voice characteristics to optimal voice if provided
            if speaker.get('voice_characteristics'):
                voice_name = self._select_optimal_voice(
                    voice_name, 'vi-VN',  # Default to Vietnamese
                    speaker['voice_characteristics'],
                    speaker.get('emotional_tone')
                )

            speaker_config = types.SpeakerVoiceConfig(
                speaker=speaker_name,
                voice_config=types.VoiceConfig(
                    prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name=voice_name
                    )
                ),
            )
            speaker_voice_configs.append(speaker_config)

        # Create enhanced prompt for multi-speaker
        enhanced_prompt = f"{style_prompt}. Đây là đoạn hội thoại nhiều người: {text}"

        # Create content
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=enhanced_prompt),
                ],
            ),
        ]

        # Configure generation with multi-speaker
        generate_content_config = types.GenerateContentConfig(
            temperature=1,
            response_modalities=["audio"],
            speech_config=types.SpeechConfig(
                multi_speaker_voice_config=types.MultiSpeakerVoiceConfig(
                    speaker_voice_configs=speaker_voice_configs
                ),
            ),
        )

        # Generate audio with streaming
        model = "gemini-2.5-flash-preview-tts"
        audio_chunks = []

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            if (chunk.candidates[0].content.parts[0].inline_data and
                chunk.candidates[0].content.parts[0].inline_data.data):

                inline_data = chunk.candidates[0].content.parts[0].inline_data
                audio_chunks.append(inline_data.data)

        if not audio_chunks:
            raise Exception("No audio data received from multi-speaker Gemini TTS")

        # Combine all audio chunks
        combined_audio = b''.join(audio_chunks)

        # Convert to desired format - default to MP3 for better compatibility
        if audio_format.lower() == 'wav':
            combined_audio = self._convert_to_wav(combined_audio, "audio/L16;rate=24000")
        else:
            # Default to MP3 for better compatibility and smaller file size
            combined_audio = self._convert_pcm_to_mp3(combined_audio)

        logger.info(f"Successfully generated multi-speaker Gemini Native TTS audio: {len(combined_audio)} bytes")
        return combined_audio

    def _convert_to_wav(self, audio_data: bytes, mime_type: str) -> bytes:
        """Convert audio data to WAV format following Google's example."""
        import struct

        parameters = self._parse_audio_mime_type(mime_type)
        bits_per_sample = parameters["bits_per_sample"]
        sample_rate = parameters["rate"]
        num_channels = 1
        data_size = len(audio_data)
        bytes_per_sample = bits_per_sample // 8
        block_align = num_channels * bytes_per_sample
        byte_rate = sample_rate * block_align
        chunk_size = 36 + data_size  # 36 bytes for header fields before data chunk size

        # WAV file header following Google's example
        header = struct.pack(
            "<4sI4s4sIHHIIHH4sI",
            b"RIFF",          # ChunkID
            chunk_size,       # ChunkSize (total file size - 8 bytes)
            b"WAVE",          # Format
            b"fmt ",          # Subchunk1ID
            16,               # Subchunk1Size (16 for PCM)
            1,                # AudioFormat (1 for PCM)
            num_channels,     # NumChannels
            sample_rate,      # SampleRate
            byte_rate,        # ByteRate
            block_align,      # BlockAlign
            bits_per_sample,  # BitsPerSample
            b"data",          # Subchunk2ID
            data_size         # Subchunk2Size (size of audio data)
        )
        return header + audio_data

    def _parse_audio_mime_type(self, mime_type: str) -> dict:
        """Parse bits per sample and rate from audio MIME type following Google's example."""
        bits_per_sample = 16
        rate = 24000

        # Extract rate from parameters
        parts = mime_type.split(";")
        for param in parts:
            param = param.strip()
            if param.lower().startswith("rate="):
                try:
                    rate_str = param.split("=", 1)[1]
                    rate = int(rate_str)
                except (ValueError, IndexError):
                    pass  # Keep rate as default
            elif param.startswith("audio/L"):
                try:
                    bits_per_sample = int(param.split("L", 1)[1])
                except (ValueError, IndexError):
                    pass  # Keep bits_per_sample as default

        return {"bits_per_sample": bits_per_sample, "rate": rate}

    def _convert_pcm_to_mp3(self, pcm_data: bytes) -> bytes:
        """Convert PCM audio data to MP3 format."""
        try:
            from pydub import AudioSegment
            import io

            # Create AudioSegment from raw PCM data
            # Assuming 16-bit, 24kHz, mono PCM
            audio = AudioSegment(
                data=pcm_data,
                sample_width=2,  # 16-bit = 2 bytes
                frame_rate=24000,
                channels=1
            )

            # Export to MP3
            mp3_buffer = io.BytesIO()
            audio.export(mp3_buffer, format="mp3", bitrate="128k")
            return mp3_buffer.getvalue()

        except ImportError:
            logger.warning("pydub not available for MP3 conversion, returning raw audio")
            return pcm_data
        except Exception as e:
            logger.warning(f"MP3 conversion failed: {e}, returning raw audio")
            return pcm_data

    def _create_style_prompt(
        self,
        speaking_rate: float,
        pitch: float,
        volume_gain_db: float,
        language_code: str
    ) -> str:
        """Create enhanced natural language style prompt for Gemini TTS with Vietnamese optimization."""

        # Use enhanced Vietnamese style prompt if Vietnamese language
        if language_code.startswith("vi"):
            return self._create_enhanced_vietnamese_style_prompt(
                speaking_rate, pitch, volume_gain_db, language_code,
                getattr(self, '_current_emotional_tone', None),
                getattr(self, '_current_narrative_style', None)
            )

        # Original English style prompt for non-Vietnamese
        style_parts = []

        # Speaking rate to natural language
        if speaking_rate < 0.8:
            style_parts.append("slowly and deliberately")
        elif speaking_rate > 1.2:
            style_parts.append("quickly and energetically")
        else:
            style_parts.append("at a natural pace")

        # Pitch to natural language
        if pitch < -5:
            style_parts.append("in a low, deep voice")
        elif pitch > 5:
            style_parts.append("in a high, bright voice")

        # Volume to natural language
        if volume_gain_db > 3:
            style_parts.append("loudly and clearly")
        elif volume_gain_db < -3:
            style_parts.append("softly and gently")

        if style_parts:
            return f"Say {', '.join(style_parts)}"
        else:
            return "Say clearly"

    def _create_enhanced_vietnamese_style_prompt(
        self,
        speaking_rate: float,
        pitch: float,
        volume_gain_db: float,
        language_code: str,
        emotional_tone: Optional[str] = None,
        narrative_style: Optional[str] = None
    ) -> str:
        """Create enhanced style prompt optimized for Vietnamese TTS with natural expression."""

        style_parts = []

        # Vietnamese-specific pronunciation and rhythm
        style_parts.append("với phát âm tiếng Việt chuẩn, rõ ràng từng thanh điệu")
        style_parts.append("chú ý đặc biệt đến các dấu thanh và âm cuối")
        style_parts.append("nhịp điệu tự nhiên như người Việt bản xứ")
        style_parts.append("tạm dừng phù hợp tại dấu phẩy và dấu chấm")

        # Enhanced speaking rate with Vietnamese characteristics
        if speaking_rate < 0.7:
            style_parts.append("nói chậm rãi, trang trọng như đọc thơ cổ điển")
        elif speaking_rate < 0.9:
            style_parts.append("nói từ tốn, có trọng âm rõ ràng")
        elif speaking_rate > 1.1:
            style_parts.append("nói nhanh nhưng vẫn rõ từng âm tiết")
        else:
            style_parts.append("tốc độ nói tự nhiên, dễ nghe")

        # Enhanced pitch control for Vietnamese tones
        if pitch > 3.0:
            style_parts.append("giọng điệu cao hơn, biểu cảm phong phú")
        elif pitch < -3.0:
            style_parts.append("giọng điệu trầm ấm, uy nghiêm")
        else:
            style_parts.append("giọng điệu cân bằng, tự nhiên")

        # Volume with emotional context
        if volume_gain_db > 3.0:
            style_parts.append("giọng nói rõ ràng, có sức thuyết phục")
        elif volume_gain_db < -3.0:
            style_parts.append("giọng nói nhẹ nhàng, ấm áp")

        # Enhanced emotional tone for Vietnamese content
        if emotional_tone:
            emotional_guidance = self._get_vietnamese_emotional_guidance(emotional_tone)
            if emotional_guidance:
                style_parts.append(emotional_guidance)

        # Enhanced narrative style for Vietnamese literature
        if narrative_style:
            narrative_guidance = self._get_vietnamese_narrative_guidance(narrative_style)
            if narrative_guidance:
                style_parts.append(narrative_guidance)

        # Combine all style elements
        if style_parts:
            return f"Hãy đọc {', '.join(style_parts)}, tạo cảm giác như đang nghe một người kể chuyện thực sự"
        else:
            return "Hãy đọc rõ ràng với giọng điệu tự nhiên"

    def _get_vietnamese_emotional_guidance(self, emotional_tone: str) -> str:
        """Get Vietnamese-specific emotional guidance for TTS."""

        vietnamese_emotions = {
            # Wuxia-specific emotions
            "wise_master": "với thái độ của một bậc thầy uyên thâm, giọng nói chứa đựng kinh nghiệm và trí tuệ",
            "young_disciple": "với sự kính trọng và háo hức của một đệ tử trẻ, giọng nói chân thành",
            "mysterious_villain": "với giọng điệu bí ẩn, đầy quyền lực và đe dọa",
            "confident_hero": "với sự tự tin và quyết đoán của một anh hùng, giọng nói mạnh mẽ",
            "gentle_healer": "với lòng từ bi và sự dịu dàng của một thầy thuốc, giọng nói ấm áp",

            # General emotions with Vietnamese cultural context
            "happy": "với niềm vui sướng, giọng nói tươi sáng và phấn khích",
            "sad": "với nỗi buồn sâu lắng, giọng nói trầm buồn và cảm động",
            "excited": "với sự hào hứng và năng động, giọng nói sôi nổi",
            "calm": "với sự bình tĩnh và thanh thản, giọng nói êm dịu",
            "wise": "với sự khôn ngoan và sâu sắc, giọng nói chứa đựng triết lý",
            "mysterious": "với vẻ bí ẩn và huyền bí, giọng nói đầy ẩn ý",
            "confident": "với sự tự tin và kiên định, giọng nói chắc chắn",
            "gentle": "với sự dịu dàng và nhân ái, giọng nói mềm mại",
            "authoritative": "với uy quyền và nghiêm túc, giọng nói có sức thuyết phục",
            "playful": "với sự vui tươi và tinh nghịch, giọng nói sinh động"
        }

        return vietnamese_emotions.get(emotional_tone, "")

    def _get_vietnamese_narrative_guidance(self, narrative_style: str) -> str:
        """Get Vietnamese-specific narrative guidance for TTS."""

        vietnamese_narratives = {
            "epic_storytelling": "như đang kể một câu chuyện sử thi hùng tráng, giọng điệu trang trọng và uy nghiêm",
            "dramatic_action": "như đang miêu tả một trận chiến kịch tính, giọng nói đầy căng thẳng và hồi hộp",
            "peaceful_meditation": "như đang hướng dẫn thiền định, giọng nói yên bình và thư thái",
            "conversational": "như đang trò chuyện thân mật, giọng nói gần gũi và tự nhiên",
            "educational": "như đang giảng dạy, giọng nói rõ ràng và dễ hiểu",
            "storytelling": "như một nghệ sĩ kể chuyện dân gian, giọng điệu sinh động và cuốn hút",
            "literary": "như đang đọc văn học cổ điển, giọng nói trang nhã và có văn hóa",
            "martial_arts": "như đang thuật lại võ công tuyệt học, giọng nói hùng hồn và khí phách"
        }

        return vietnamese_narratives.get(narrative_style, "")

    def _select_optimal_voice(
        self,
        voice: str,
        language_code: str,
        voice_characteristics: Optional[dict] = None,
        emotional_tone: Optional[str] = None
    ) -> str:
        """Select optimal Gemini voice based on characteristics and language."""

        # Complete Gemini voice database with characteristics
        GEMINI_VOICES = {
            # Bright/Energetic voices
            "Zephyr": {"characteristic": "bright", "personality": "energetic", "age": "youthful"},
            "Puck": {"characteristic": "upbeat", "personality": "playful", "age": "youthful"},
            "Fenrir": {"characteristic": "excitable", "personality": "enthusiastic", "age": "youthful"},
            "Autonoe": {"characteristic": "bright", "personality": "cheerful", "age": "youthful"},
            "Leda": {"characteristic": "youthful", "personality": "lively", "age": "young"},

            # Firm/Authoritative voices
            "Kore": {"characteristic": "firm", "personality": "confident", "age": "mature"},
            "Orus": {"characteristic": "firm", "personality": "authoritative", "age": "mature"},
            "Alnilam": {"characteristic": "firm", "personality": "strong", "age": "mature"},

            # Smooth/Gentle voices
            "Algieba": {"characteristic": "smooth", "personality": "gentle", "age": "mature"},
            "Despina": {"characteristic": "smooth", "personality": "elegant", "age": "mature"},
            "Achernar": {"characteristic": "soft", "personality": "gentle", "age": "mature"},
            "Vindemiatrix": {"characteristic": "gentle", "personality": "kind", "age": "mature"},

            # Clear/Informative voices
            "Charon": {"characteristic": "informative", "personality": "knowledgeable", "age": "mature"},
            "Iapetus": {"characteristic": "clear", "personality": "articulate", "age": "mature"},
            "Erinome": {"characteristic": "clear", "personality": "precise", "age": "mature"},
            "Rasalgethi": {"characteristic": "informative", "personality": "educational", "age": "mature"},
            "Sadaltager": {"characteristic": "knowledgeable", "personality": "wise", "age": "elderly"},

            # Breathy/Soft voices
            "Enceladus": {"characteristic": "breathy", "personality": "mysterious", "age": "mature"},
            "Aoede": {"characteristic": "breezy", "personality": "natural", "age": "mature"},

            # Easy-going/Casual voices
            "Callirhoe": {"characteristic": "easy-going", "personality": "relaxed", "age": "mature"},
            "Umbriel": {"characteristic": "easy-going", "personality": "casual", "age": "mature"},
            "Zubenelgenubi": {"characteristic": "casual", "personality": "friendly", "age": "mature"},
            "Achird": {"characteristic": "friendly", "personality": "approachable", "age": "mature"},

            # Mature/Wise voices
            "Gacrux": {"characteristic": "mature", "personality": "wise", "age": "elderly"},
            "Schedar": {"characteristic": "even", "personality": "balanced", "age": "mature"},
            "Pulcherrima": {"characteristic": "forward", "personality": "confident", "age": "mature"},

            # Unique characteristics
            "Algenib": {"characteristic": "gravelly", "personality": "rugged", "age": "mature"},
            "Laomedeia": {"characteristic": "upbeat", "personality": "optimistic", "age": "youthful"},
            "Sadachbia": {"characteristic": "lively", "personality": "animated", "age": "youthful"},
            "Sulafar": {"characteristic": "warm", "personality": "caring", "age": "mature"},
        }

        # If specific voice provided, map it or use directly
        if voice and voice != "en-US-Standard-A":
            traditional_mapping = {
                "en-US-Standard-A": "Kore",
                "en-US-Standard-B": "Puck",
                "en-US-Standard-C": "Charon",
                "vi-VN-Standard-A": "Aoede",
                "vi-VN-Standard-B": "Leda",
                "vi-VN-Standard-C": "Callirhoe",
            }
            if voice in traditional_mapping:
                return traditional_mapping[voice]
            elif voice in GEMINI_VOICES:
                return voice

        # Select based on voice characteristics
        if voice_characteristics:
            return self._find_voice_by_characteristics(GEMINI_VOICES, voice_characteristics, emotional_tone, language_code)

        # Select based on emotional tone only
        if emotional_tone:
            return self._find_voice_by_emotion(GEMINI_VOICES, emotional_tone, language_code)

        # Default voices by language
        language_defaults = {
            "vi-VN": "Aoede",  # Breezy, good for Vietnamese
            "vi": "Aoede",
            "en-US": "Kore",   # Firm, clear English
            "en": "Kore",
        }

        return language_defaults.get(language_code, "Kore")

    def _find_voice_by_characteristics(
        self,
        voices: dict,
        characteristics: dict,
        emotional_tone: Optional[str],
        language_code: str
    ) -> str:
        """Find best voice match based on characteristics."""

        # Score each voice based on characteristic matches
        voice_scores = {}

        for voice_name, voice_attrs in voices.items():
            score = 0

            # Match characteristic
            if characteristics.get("characteristic"):
                if voice_attrs["characteristic"] == characteristics["characteristic"]:
                    score += 10
                elif characteristics["characteristic"] in voice_attrs["characteristic"]:
                    score += 5

            # Match personality
            if characteristics.get("personality"):
                if voice_attrs["personality"] == characteristics["personality"]:
                    score += 8
                elif characteristics["personality"] in voice_attrs["personality"]:
                    score += 4

            # Match age
            if characteristics.get("age"):
                if voice_attrs["age"] == characteristics["age"]:
                    score += 6
                elif characteristics["age"] in voice_attrs["age"]:
                    score += 3

            # Emotional tone bonus
            if emotional_tone:
                emotion_voice_map = {
                    "happy": ["upbeat", "bright", "cheerful", "lively"],
                    "sad": ["soft", "gentle", "breathy"],
                    "excited": ["excitable", "enthusiastic", "energetic"],
                    "calm": ["gentle", "soft", "even", "breezy"],
                    "wise": ["mature", "knowledgeable", "wise"],
                    "mysterious": ["breathy", "mysterious", "gravelly"],
                    "confident": ["firm", "strong", "confident"],
                    "playful": ["upbeat", "playful", "lively"],
                    "authoritative": ["firm", "authoritative", "strong"],
                    "gentle": ["soft", "gentle", "kind", "caring"]
                }

                if emotional_tone in emotion_voice_map:
                    for trait in emotion_voice_map[emotional_tone]:
                        if trait in voice_attrs.values():
                            score += 5

            voice_scores[voice_name] = score

        # Return highest scoring voice, with language-specific fallbacks
        if voice_scores:
            best_voice = max(voice_scores, key=voice_scores.get)
            if voice_scores[best_voice] > 0:
                return best_voice

        # Fallback to language defaults
        return "Aoede" if language_code.startswith("vi") else "Kore"

    def _find_voice_by_emotion(self, voices: dict, emotional_tone: str, language_code: str) -> str:
        """Find best voice match based on emotional tone only."""

        emotion_voice_preferences = {
            # Vietnamese wuxia-specific emotions
            "wise_master": "Gacrux",        # Mature, wise - perfect for sư phụ
            "young_disciple": "Leda",       # Youthful - perfect for đệ tử
            "mysterious": "Enceladus",      # Breathy, mysterious
            "authoritative": "Kore",        # Firm, confident
            "gentle_teacher": "Vindemiatrix", # Gentle, kind
            "excited_student": "Fenrir",    # Excitable, enthusiastic

            # General emotions
            "happy": "Puck",               # Upbeat, playful
            "sad": "Achernar",             # Soft, gentle
            "excited": "Fenrir",           # Excitable
            "calm": "Aoede",               # Breezy, natural
            "wise": "Sadaltager",          # Knowledgeable, wise
            "confident": "Kore",           # Firm, confident
            "playful": "Puck",             # Upbeat, playful
            "mysterious": "Enceladus",     # Breathy, mysterious
            "gentle": "Vindemiatrix",      # Gentle, kind
            "authoritative": "Orus",       # Firm, authoritative
            "warm": "Sulafar",             # Warm, caring
            "clear": "Charon",             # Informative, clear
        }

        # Direct emotion mapping
        if emotional_tone in emotion_voice_preferences:
            return emotion_voice_preferences[emotional_tone]

        # Fallback based on language
        return "Aoede" if language_code.startswith("vi") else "Kore"

    def _generate_google_cloud_tts(
        self,
        text: str,
        voice: str,
        language_code: str,
        audio_format: str,
        speaking_rate: float,
        pitch: float,
        volume_gain_db: float,
        sample_rate: int,
        effects_profile_id: Optional[List[str]] = None
    ) -> bytes:
        """Generate TTS audio using Google Cloud Text-to-Speech API."""
        from google.cloud import texttospeech

        # Initialize the Text-to-Speech client
        client = texttospeech.TextToSpeechClient()

        # Set the text input to be synthesized
        synthesis_input = texttospeech.SynthesisInput(text=text)

        # Build the voice request
        voice_params = texttospeech.VoiceSelectionParams(
            language_code=language_code,
            name=voice
        )

        # Select the type of audio file to return
        audio_encoding_map = {
            "mp3": texttospeech.AudioEncoding.MP3,
            "wav": texttospeech.AudioEncoding.LINEAR16,
            "ogg": texttospeech.AudioEncoding.OGG_OPUS
        }

        audio_encoding = audio_encoding_map.get(audio_format.lower(), texttospeech.AudioEncoding.MP3)

        # Configure audio settings
        audio_config = texttospeech.AudioConfig(
            audio_encoding=audio_encoding,
            speaking_rate=speaking_rate,
            pitch=pitch,
            volume_gain_db=volume_gain_db,
            sample_rate_hertz=sample_rate,
            effects_profile_id=effects_profile_id or []
        )

        # Perform the text-to-speech request
        response = client.synthesize_speech(
            input=synthesis_input,
            voice=voice_params,
            audio_config=audio_config
        )

        logger.info(f"Successfully generated Google Cloud TTS audio: {len(response.audio_content)} bytes")
        return response.audio_content

    def _generate_gtts_audio(
        self,
        text: str,
        language_code: str,
        audio_format: str
    ) -> bytes:
        """Generate TTS audio using Google Translate TTS (gTTS)."""
        import tempfile
        import os
        from gtts import gTTS

        # Map language codes
        lang_map = {
            "en-US": "en",
            "vi-VN": "vi",
            "en": "en",
            "vi": "vi"
        }

        lang = lang_map.get(language_code, "en")

        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            # Generate TTS
            tts = gTTS(text=text, lang=lang, slow=False)
            tts.save(temp_path)

            # Read the generated audio file
            with open(temp_path, 'rb') as f:
                audio_content = f.read()

            # Convert format if needed
            if audio_format.lower() != "mp3":
                audio_content = self._convert_audio_format(audio_content, "mp3", audio_format)

            logger.info(f"Successfully generated gTTS audio: {len(audio_content)} bytes")
            return audio_content

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def _convert_audio_format(self, audio_data: bytes, from_format: str, to_format: str) -> bytes:
        """Convert audio from one format to another using pydub."""
        try:
            from pydub import AudioSegment
            import tempfile
            import os

            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix=f".{from_format}", delete=False) as temp_input:
                temp_input.write(audio_data)
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(suffix=f".{to_format}", delete=False) as temp_output:
                temp_output_path = temp_output.name

            try:
                # Load and convert audio
                audio = AudioSegment.from_file(temp_input_path, format=from_format)
                audio.export(temp_output_path, format=to_format)

                # Read converted audio
                with open(temp_output_path, 'rb') as f:
                    converted_audio = f.read()

                return converted_audio

            finally:
                # Clean up
                for path in [temp_input_path, temp_output_path]:
                    if os.path.exists(path):
                        os.unlink(path)

        except ImportError:
            logger.warning("pydub not available for audio conversion")
            return audio_data  # Return original if conversion fails

    def _generate_mock_audio(self, text: str, audio_format: str, sample_rate: int) -> bytes:
        """Generate mock audio content for testing purposes.

        In production, this would be replaced with actual Google Cloud TTS API calls.

        Args:
            text: Text to convert
            audio_format: Audio format
            sample_rate: Sample rate

        Returns:
            Mock audio content as bytes
        """
        # Generate a simple mock audio file header based on format
        if audio_format.lower() == "wav":
            # Simple WAV header (44 bytes) + minimal audio data
            header = b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x22\x56\x00\x00\x44\xac\x00\x00\x02\x00\x10\x00data\x00\x08\x00\x00'
            # Add some mock audio data (silence)
            audio_data = b'\x00' * (sample_rate * 2)  # 1 second of silence
            return header + audio_data
        elif audio_format.lower() == "mp3":
            # Mock MP3 header
            mp3_header = b'\xff\xfb\x90\x00' + b'\x00' * 100  # Simplified MP3 header
            return mp3_header + b'\x00' * 1000  # Mock MP3 data
        else:  # ogg or other
            # Mock OGG header
            ogg_header = b'OggS\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00'
            return ogg_header + b'\x00' * 1000  # Mock OGG data

    async def text_to_speech_async(
        self,
        text: str,
        voice: str = "en-US-Standard-A",
        language_code: str = "en-US",
        audio_format: str = "mp3",
        speaking_rate: float = 1.0,
        pitch: float = 0.0,
        volume_gain_db: float = 0.0,
        sample_rate: int = 22050,
        effects_profile_id: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Convert text to speech asynchronously.

        Args:
            text: Text to convert to speech
            voice: Voice to use for synthesis
            language_code: Language code (e.g., en-US, vi-VN)
            audio_format: Audio format (mp3, wav, ogg)
            speaking_rate: Speaking rate (0.25 to 4.0)
            pitch: Pitch adjustment (-20.0 to 20.0)
            volume_gain_db: Volume gain in dB (-96.0 to 16.0)
            sample_rate: Audio sample rate in Hz
            effects_profile_id: Audio effects profile IDs

        Returns:
            Dictionary containing audio content and metadata
        """
        logger.info(f"Converting text to speech asynchronously: {text[:50]}...")

        # Run the synchronous TTS function in a thread pool
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.text_to_speech,
            text, voice, language_code, audio_format,
            speaking_rate, pitch, volume_gain_db, sample_rate, effects_profile_id
        )

    # Context Caching Methods
    async def generate_with_cache_async(
        self,
        cache_name: str,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
        json_mode: bool = False,
        json_schema: Optional[Union[Dict[str, Any], Any]] = None,
    ) -> Union[Dict[str, Any], AsyncIterator[Dict[str, Any]]]:
        """Generate content using cached context.

        Args:
            cache_name: Name of the cache to use
            prompt: Additional prompt text
            model: Model to use (overrides cache model if provided)
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            safety_settings: Safety settings for content generation
            stream: Whether to stream the response
            json_mode: Enable JSON mode for structured output
            json_schema: JSON schema for structured output validation

        Returns:
            Dictionary containing generated text and usage information,
            or an async iterator of dictionaries if streaming
        """
        try:
            logger.info(f"Generating content with cache: {cache_name}")

            # Get cached model
            cached_model = context_cache_manager.get_cached_model(cache_name)

            # Create generation config
            config_params = {
                "temperature": temperature,
                "top_p": top_p,
                "top_k": top_k,
            }

            if max_tokens:
                config_params["max_output_tokens"] = max_tokens

            # Add JSON mode support
            if json_mode:
                json_config = json_mode_handler.create_generation_config(
                    json_mode=True,
                    json_schema=json_schema,
                    **config_params
                )
                config = types.GenerateContentConfig(**json_config)
                logger.info("JSON mode enabled for cached generation")
            else:
                config = types.GenerateContentConfig(**config_params)

            # Add safety settings if provided
            if safety_settings:
                config.safety_settings = [
                    types.SafetySetting(
                        category=setting["category"],
                        threshold=setting["threshold"]
                    )
                    for setting in safety_settings
                ]

            if stream:
                # Return a streaming response
                response_stream = await cached_model.generate_content_async(
                    prompt,
                    generation_config=config,
                    stream=True
                )

                return self._process_cached_stream_async(response_stream, cache_name, json_mode, json_schema)
            else:
                # Return a complete response
                response = await cached_model.generate_content_async(
                    prompt,
                    generation_config=config
                )

                # Extract token usage if available
                usage = {}
                if hasattr(response, 'usage_metadata'):
                    usage = {
                        'prompt_tokens': getattr(response.usage_metadata, 'prompt_token_count', 0),
                        'completion_tokens': getattr(response.usage_metadata, 'candidates_token_count', 0),
                        'total_tokens': getattr(response.usage_metadata, 'total_token_count', 0),
                        'cached_content_token_count': getattr(response.usage_metadata, 'cached_content_token_count', 0)
                    }

                finish_reason = None
                if response.candidates and hasattr(response.candidates[0], 'finish_reason'):
                    finish_reason = response.candidates[0].finish_reason

                # Validate JSON response if JSON mode is enabled
                response_text = response.text
                json_data = None
                if json_mode and response_text:
                    try:
                        json_data = json_mode_handler.validate_json_response(
                            response_text,
                            json_schema if json_schema else None
                        )
                        logger.info("JSON response validated successfully")
                    except ValueError as e:
                        logger.warning(f"JSON validation failed: {e}")
                        # Continue with raw text response

                result = {
                    "text": response_text,
                    "model": model or "cached_model",
                    "usage": usage,
                    "finish_reason": finish_reason,
                    "cache_name": cache_name,
                    "json_mode": json_mode
                }

                if json_data is not None:
                    result["json_data"] = json_data

                return result

        except Exception as e:
            logger.error(f"Error generating content with cache {cache_name}: {str(e)}")
            raise

    async def _process_cached_stream_async(
        self,
        response_stream,
        cache_name: str,
        json_mode: bool = False,
        json_schema: Optional[Dict[str, Any]] = None
    ) -> AsyncIterator[Dict[str, Any]]:
        """Process a streaming response from cached content.

        Args:
            response_stream: Streaming response from the API
            cache_name: Name of the cache used
            json_mode: Whether JSON mode is enabled
            json_schema: JSON schema for validation

        Yields:
            Dictionary containing chunk of generated text
        """
        chunk_count = 0
        total_text = ""
        finish_reason = None

        try:
            async for chunk in response_stream:
                # Check if the chunk has text content
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_count += 1
                    total_text += chunk.text

                    chunk_data = {
                        "text": chunk.text,
                        "model": "cached_model",
                        "is_final": False,
                        "chunk_id": chunk_count,
                        "finish_reason": None,
                        "cache_name": cache_name,
                        "json_mode": json_mode
                    }

                    yield chunk_data

                # Check for finish reason
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    candidate = chunk.candidates[0]
                    if hasattr(candidate, 'finish_reason'):
                        finish_reason = str(candidate.finish_reason)

        except Exception as e:
            logger.error(f"Error in cached streaming response: {str(e)}")
            finish_reason = "error"

        # Process final chunk with JSON validation if needed
        final_chunk_data = {
            "text": "",
            "model": "cached_model",
            "is_final": True,
            "chunk_id": chunk_count + 1,
            "finish_reason": finish_reason or "stop",
            "total_chunks": chunk_count,
            "total_text_length": len(total_text),
            "cache_name": cache_name,
            "json_mode": json_mode
        }

        # Validate JSON if enabled and we have complete text
        if json_mode and total_text:
            try:
                json_data = json_mode_handler.validate_json_response(
                    total_text,
                    json_schema if json_schema else None
                )
                final_chunk_data["json_data"] = json_data
                logger.info("JSON response validated successfully in streaming")
            except ValueError as e:
                logger.warning(f"JSON validation failed in streaming: {e}")
                final_chunk_data["json_validation_error"] = str(e)

        yield final_chunk_data

    # Performance Optimization Methods
    async def generate_with_cache(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        cache_ttl: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text with response caching."""
        # Generate cache key
        cache_key = cache_manager.generate_cache_key(
            "generate",
            prompt=prompt,
            model=model or settings.default_model,
            max_tokens=max_tokens,
            temperature=temperature,
            **kwargs
        )

        # Try to get from cache
        cached_response = await cache_manager.get_cached_response(cache_key)
        if cached_response:
            logger.info(f"Cache hit for generation request")
            return cached_response

        # Generate new response
        response = await self.generate_async(
            prompt=prompt,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            **kwargs
        )

        # Cache the response
        await cache_manager.cache_response(
            cache_key,
            response,
            ttl=cache_ttl,
            tags=["generation", model or settings.default_model]
        )

        return response

    async def embeddings_with_cache(
        self,
        texts: List[str],
        model: Optional[str] = None,
        cache_ttl: Optional[int] = None
    ) -> Dict[str, Any]:
        """Generate embeddings with caching."""
        model = model or settings.default_embedding_model

        # Check cache for each text
        cached_embeddings = {}
        texts_to_process = []

        for i, text in enumerate(texts):
            cache_key = cache_manager.generate_cache_key(
                "embedding",
                text=text,
                model=model
            )

            cached_result = await cache_manager.get_cached_response(cache_key, "embedding")
            if cached_result:
                cached_embeddings[i] = cached_result
            else:
                texts_to_process.append((i, text))

        # Process uncached texts
        new_embeddings = {}
        if texts_to_process:
            texts_only = [text for _, text in texts_to_process]
            result = await self.embeddings_async(texts_only, model)

            # Cache new embeddings
            for j, (original_index, text) in enumerate(texts_to_process):
                if j < len(result["embeddings"]):
                    embedding = result["embeddings"][j]
                    new_embeddings[original_index] = embedding

                    # Cache individual embedding
                    cache_key = cache_manager.generate_cache_key(
                        "embedding",
                        text=text,
                        model=model
                    )
                    await cache_manager.cache_response(
                        cache_key,
                        embedding,
                        ttl=cache_ttl,
                        cache_type="embedding",
                        tags=["embedding", model]
                    )

        # Combine cached and new embeddings
        all_embeddings = []
        for i in range(len(texts)):
            if i in cached_embeddings:
                all_embeddings.append(cached_embeddings[i])
            elif i in new_embeddings:
                all_embeddings.append(new_embeddings[i])
            else:
                # Fallback to zero vector
                all_embeddings.append([0.0] * 768)  # Default embedding size

        return {
            "embeddings": all_embeddings,
            "model": model,
            "cached_count": len(cached_embeddings),
            "new_count": len(new_embeddings)
        }

    async def batch_generate(
        self,
        prompts: List[str],
        model: Optional[str] = None,
        batch_size: int = 5,
        **kwargs
    ) -> str:
        """Generate text for multiple prompts using batch processing."""

        async def process_batch(prompt_batch: List[str]) -> List[Dict[str, Any]]:
            """Process a batch of prompts."""
            tasks = []
            for prompt in prompt_batch:
                task = self.generate_with_cache(
                    prompt=prompt,
                    model=model,
                    **kwargs
                )
                tasks.append(task)

            return await asyncio.gather(*tasks, return_exceptions=True)

        # Submit to batch processor
        batch_id = await batch_processor.process_batch(
            items=prompts,
            processor_func=process_batch
        )

        return batch_id

    async def batch_embeddings(
        self,
        texts: List[str],
        model: Optional[str] = None,
        batch_size: int = 10
    ) -> str:
        """Generate embeddings for multiple texts using batch processing."""

        async def process_batch(text_batch: List[str]) -> List[Dict[str, Any]]:
            """Process a batch of texts."""
            return await self.embeddings_with_cache(
                texts=text_batch,
                model=model
            )

        # Submit to batch processor
        batch_id = await batch_processor.process_batch(
            items=texts,
            processor_func=process_batch
        )

        return batch_id

    # Advanced Streaming Methods
    async def create_universal_stream(
        self,
        stream_type: StreamType,
        client_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create universal stream for any AI operation."""
        stream_id = await streaming_manager.create_stream(
            stream_type=stream_type,
            client_id=client_id,
            metadata=metadata
        )

        await streaming_manager.start_stream(stream_id)

        logger.info(f"Created universal stream {stream_id} of type {stream_type.value}")
        return stream_id

    async def stream_generate_universal(
        self,
        prompt: str,
        stream_id: str,
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncIterator[Dict[str, Any]]:
        """Universal streaming text generation."""
        try:
            # Start streaming generation
            await streaming_manager.send_chunk(
                stream_id,
                "generation_start",
                {
                    "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                    "model": model or settings.default_model,
                    "parameters": kwargs
                }
            )

            # Use existing streaming method
            async for chunk in self.generate_stream_async(
                prompt=prompt,
                model=model,
                **kwargs
            ):
                # Send chunk to stream
                await streaming_manager.send_chunk(
                    stream_id,
                    "generation_chunk",
                    chunk,
                    is_final=chunk.get("finish_reason") is not None
                )

                yield chunk

                # Break if final chunk
                if chunk.get("finish_reason"):
                    break

        except Exception as e:
            # Send error to stream
            await streaming_manager.error_stream(stream_id, str(e))
            raise

    async def stream_multimodal_universal(
        self,
        prompt: str,
        media_files: List[Dict[str, Any]],
        stream_id: str,
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncIterator[Dict[str, Any]]:
        """Universal streaming multimodal generation."""
        try:
            # Start streaming
            await streaming_manager.send_chunk(
                stream_id,
                "multimodal_start",
                {
                    "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                    "media_count": len(media_files),
                    "model": model or settings.default_model
                }
            )

            # Use existing multimodal streaming
            async for chunk in self.multimodal_stream_async(
                prompt=prompt,
                media_files=media_files,
                model=model,
                **kwargs
            ):
                # Send chunk to stream
                await streaming_manager.send_chunk(
                    stream_id,
                    "multimodal_chunk",
                    chunk,
                    is_final=chunk.get("finish_reason") is not None
                )

                yield chunk

                if chunk.get("finish_reason"):
                    break

        except Exception as e:
            await streaming_manager.error_stream(stream_id, str(e))
            raise

    async def stream_function_calling_universal(
        self,
        prompt: str,
        functions: List[Dict[str, Any]],
        stream_id: str,
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncIterator[Dict[str, Any]]:
        """Universal streaming function calling."""
        try:
            # Start streaming
            await streaming_manager.send_chunk(
                stream_id,
                "function_calling_start",
                {
                    "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                    "function_count": len(functions),
                    "model": model or settings.default_model
                }
            )

            # Use existing function calling streaming
            async for chunk in self.enhanced_function_calling_stream_async(
                prompt=prompt,
                functions=functions,
                model=model,
                **kwargs
            ):
                # Send chunk to stream
                await streaming_manager.send_chunk(
                    stream_id,
                    "function_calling_chunk",
                    chunk,
                    is_final=chunk.get("finish_reason") is not None
                )

                yield chunk

                if chunk.get("finish_reason"):
                    break

        except Exception as e:
            await streaming_manager.error_stream(stream_id, str(e))
            raise

    async def get_stream_status(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """Get stream status information."""
        session = streaming_manager.get_stream_session(stream_id)
        if not session:
            return None

        return {
            "stream_id": session.id,
            "stream_type": session.stream_type.value,
            "status": session.status.value,
            "created_at": session.created_at.isoformat(),
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "client_id": session.client_id,
            "chunks_sent": session.chunks_sent,
            "bytes_sent": session.bytes_sent,
            "metadata": session.metadata,
            "error_message": session.error_message
        }

    async def cancel_stream(self, stream_id: str) -> bool:
        """Cancel an active stream."""
        return await streaming_manager.cancel_stream(stream_id)

    async def pause_stream(self, stream_id: str) -> bool:
        """Pause an active stream."""
        return await streaming_manager.pause_stream(stream_id)

    async def resume_stream(self, stream_id: str) -> bool:
        """Resume a paused stream."""
        return await streaming_manager.resume_stream(stream_id)

    # Real-time Collaboration Methods
    async def create_collaboration_session(
        self,
        session_name: str,
        creator_client_id: str,
        session_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create real-time collaboration session."""
        from ai_service.core.streaming_manager import collaboration_manager

        return await collaboration_manager.create_collaboration_session(
            session_name=session_name,
            creator_client_id=creator_client_id,
            session_config=session_config
        )

    async def join_collaboration_session(
        self,
        session_id: str,
        client_id: str
    ) -> bool:
        """Join collaboration session."""
        from ai_service.core.streaming_manager import collaboration_manager

        return await collaboration_manager.join_collaboration_session(
            session_id=session_id,
            client_id=client_id
        )

    async def leave_collaboration_session(self, client_id: str) -> bool:
        """Leave collaboration session."""
        from ai_service.core.streaming_manager import collaboration_manager

        return await collaboration_manager.leave_collaboration_session(client_id)

    async def update_shared_context(
        self,
        session_id: str,
        client_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update shared context in collaboration session."""
        from ai_service.core.streaming_manager import collaboration_manager

        return await collaboration_manager.update_shared_context(
            session_id=session_id,
            client_id=client_id,
            updates=updates
        )

    async def get_shared_context(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get shared context from collaboration session."""
        from ai_service.core.streaming_manager import collaboration_manager

        return await collaboration_manager.get_shared_context(session_id)

    async def start_collaborative_stream(
        self,
        session_id: str,
        stream_type: StreamType,
        initiator_client_id: str,
        stream_config: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Start collaborative streaming session."""
        from ai_service.core.streaming_manager import collaboration_manager

        return await collaboration_manager.start_collaborative_stream(
            session_id=session_id,
            stream_type=stream_type,
            initiator_client_id=initiator_client_id,
            stream_config=stream_config
        )

    # Enhanced Function Calling Methods
    async def enhanced_function_calling_async(
        self,
        prompt: str,
        functions: List[Dict[str, Any]],
        function_chains: Optional[List[Dict[str, Any]]] = None,
        execution_config: Optional[Dict[str, Any]] = None,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7,
        top_p: Optional[float] = 0.95,
        top_k: Optional[int] = 40,
        system_instruction: Optional[str] = None,
        safety_settings: Optional[List[Dict[str, str]]] = None,
        json_mode: bool = False,
        json_schema: Optional[Union[Dict[str, Any], Any]] = None,
    ) -> Dict[str, Any]:
        """Enhanced function calling with chaining and parallel execution.

        Args:
            prompt: Text prompt for generation
            functions: Available functions
            function_chains: Predefined function chains
            execution_config: Function execution configuration
            model: Model to use for generation
            max_tokens: Maximum tokens to generate
            temperature: Temperature for generation
            top_p: Top p for nucleus sampling
            top_k: Top k for sampling
            system_instruction: System instruction for the model
            safety_settings: Safety settings for content generation
            json_mode: Enable JSON mode for structured output
            json_schema: JSON schema for structured output validation

        Returns:
            Dictionary containing generated text and function execution results
        """
        try:
            model_name = model or settings.default_model
            logger.info(f"Enhanced function calling with model {model_name}")

            # Register functions with enhanced executor
            for func in functions:
                if func["name"] not in enhanced_function_executor.function_registry:
                    # Create a mock function for demonstration
                    def mock_function(**kwargs):
                        return f"Mock result for {func['name']} with args: {kwargs}"

                    enhanced_function_executor.register_function(func["name"], mock_function)

            # First, get function calls from the model
            result = await self.function_calling_async(
                prompt=prompt,
                functions=functions,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                system_instruction=system_instruction,
                safety_settings=safety_settings
            )

            # Enhanced execution results
            function_results = []
            chain_results = []
            execution_summary = {
                "total_functions": 0,
                "successful_functions": 0,
                "failed_functions": 0,
                "total_chains": 0,
                "successful_chains": 0,
                "failed_chains": 0,
                "total_execution_time": 0.0
            }

            # Execute individual function calls
            if result.get("function_call"):
                function_call = result["function_call"]
                logger.info(f"Executing enhanced function call: {function_call['name']}")

                # Parse execution config
                from ai_service.models.schemas import FunctionExecutionConfig
                exec_config = None
                if execution_config:
                    exec_config = FunctionExecutionConfig(**execution_config)

                # Execute function with enhanced executor
                execution_result = await enhanced_function_executor.execute_function_with_retry(
                    function_call["name"],
                    function_call.get("args", {}),
                    exec_config
                )

                function_results.append(execution_result)
                execution_summary["total_functions"] += 1
                execution_summary["total_execution_time"] += execution_result.execution_time

                if execution_result.success:
                    execution_summary["successful_functions"] += 1
                else:
                    execution_summary["failed_functions"] += 1

            # Execute function chains if provided
            if function_chains:
                from ai_service.models.schemas import FunctionChain, FunctionChainStep

                for chain_data in function_chains:
                    try:
                        # Convert to FunctionChain object
                        steps = []
                        for step_data in chain_data.get("steps", []):
                            step = FunctionChainStep(**step_data)
                            steps.append(step)

                        chain = FunctionChain(
                            name=chain_data["name"],
                            description=chain_data.get("description"),
                            steps=steps,
                            execution_config=FunctionExecutionConfig(**execution_config) if execution_config else None
                        )

                        # Execute chain
                        chain_result = await enhanced_function_executor.execute_function_chain(chain)
                        chain_results.append(chain_result)

                        execution_summary["total_chains"] += 1
                        execution_summary["total_execution_time"] += chain_result.execution_time

                        if chain_result.success:
                            execution_summary["successful_chains"] += 1
                        else:
                            execution_summary["failed_chains"] += 1

                    except Exception as e:
                        logger.error(f"Failed to execute chain {chain_data.get('name', 'unknown')}: {e}")
                        execution_summary["total_chains"] += 1
                        execution_summary["failed_chains"] += 1

            # Validate JSON response if JSON mode is enabled
            response_text = result.get("text", "")
            json_data = None
            if json_mode and response_text:
                try:
                    json_data = json_mode_handler.validate_json_response(
                        response_text,
                        json_schema if json_schema else None
                    )
                    logger.info("JSON response validated successfully")
                except ValueError as e:
                    logger.warning(f"JSON validation failed: {e}")
                    # Continue with raw text response

            # Prepare enhanced response
            enhanced_result = {
                "text": response_text,
                "model": model_name,
                "usage": result.get("usage", {}),
                "finish_reason": result.get("finish_reason"),
                "function_calls": [result.get("function_call")] if result.get("function_call") else [],
                "function_results": [r.dict() for r in function_results],
                "chain_results": [r.dict() for r in chain_results],
                "execution_summary": execution_summary,
                "json_mode": json_mode
            }

            if json_data is not None:
                enhanced_result["json_data"] = json_data

            return enhanced_result

        except Exception as e:
            logger.error(f"Error in enhanced function calling: {str(e)}")
            raise
