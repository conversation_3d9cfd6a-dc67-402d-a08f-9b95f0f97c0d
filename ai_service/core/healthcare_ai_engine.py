"""
Healthcare AI Engine for Phase 9.
Provides HIPAA-compliant medical AI capabilities with specialized models and workflows.
"""

import asyncio
import logging
import time
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import uuid
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)


class MedicalSpecialty(Enum):
    """Medical specialties supported."""
    GENERAL_MEDICINE = "general_medicine"
    CARDIOLOGY = "cardiology"
    NEUROLOGY = "neurology"
    ONCOLOGY = "oncology"
    RADIOLOGY = "radiology"
    PATHOLOGY = "pathology"
    PSYCHIATRY = "psychiatry"
    PEDIATRICS = "pediatrics"
    SURGERY = "surgery"
    EMERGENCY_MEDICINE = "emergency_medicine"
    DERMATOLOGY = "dermatology"
    OPHTHALMOLOGY = "ophthalmology"


class MedicalDataType(Enum):
    """Types of medical data."""
    CLINICAL_NOTES = "clinical_notes"
    DIAGNOSTIC_REPORTS = "diagnostic_reports"
    LAB_RESULTS = "lab_results"
    IMAGING_REPORTS = "imaging_reports"
    MEDICATION_RECORDS = "medication_records"
    PATIENT_HISTORY = "patient_history"
    TREATMENT_PLANS = "treatment_plans"
    DISCHARGE_SUMMARIES = "discharge_summaries"
    RESEARCH_DATA = "research_data"
    ANONYMIZED_DATA = "anonymized_data"


class HIPAACompliance(Enum):
    """HIPAA compliance levels."""
    STRICT = "strict"           # Full PHI protection
    STANDARD = "standard"       # Standard HIPAA compliance
    RESEARCH = "research"       # De-identified research data
    ANONYMIZED = "anonymized"   # Fully anonymized data


class MedicalAICapability(Enum):
    """Medical AI capabilities."""
    CLINICAL_DECISION_SUPPORT = "clinical_decision_support"
    DIAGNOSTIC_ASSISTANCE = "diagnostic_assistance"
    TREATMENT_RECOMMENDATION = "treatment_recommendation"
    DRUG_INTERACTION_CHECK = "drug_interaction_check"
    MEDICAL_CODING = "medical_coding"
    CLINICAL_DOCUMENTATION = "clinical_documentation"
    PATIENT_RISK_ASSESSMENT = "patient_risk_assessment"
    MEDICAL_RESEARCH = "medical_research"
    SYMPTOM_ANALYSIS = "symptom_analysis"
    RADIOLOGY_ANALYSIS = "radiology_analysis"


@dataclass
class MedicalContext:
    """Medical context for AI processing."""
    specialty: MedicalSpecialty
    data_type: MedicalDataType
    compliance_level: HIPAACompliance
    
    # Patient information (anonymized)
    patient_id: Optional[str] = None
    age_group: Optional[str] = None  # "pediatric", "adult", "geriatric"
    gender: Optional[str] = None
    
    # Clinical context
    chief_complaint: Optional[str] = None
    medical_history: List[str] = field(default_factory=list)
    current_medications: List[str] = field(default_factory=list)
    allergies: List[str] = field(default_factory=list)
    
    # Institutional context
    institution_id: str = "default"
    provider_id: Optional[str] = None
    encounter_type: str = "outpatient"  # "inpatient", "outpatient", "emergency"


@dataclass
class MedicalAIRequest:
    """Medical AI processing request."""
    request_id: str
    capability: MedicalAICapability
    medical_context: MedicalContext
    
    # Input data
    input_text: str
    input_data: Dict[str, Any] = field(default_factory=dict)
    
    # Processing requirements
    require_explanation: bool = True
    confidence_threshold: float = 0.8
    max_response_time: float = 30.0
    
    # Compliance requirements
    audit_required: bool = True
    peer_review_required: bool = False
    
    timestamp: float = field(default_factory=time.time)


@dataclass
class MedicalAIResponse:
    """Medical AI processing response."""
    request_id: str
    capability: MedicalAICapability
    
    # Results
    primary_result: Any
    confidence_score: float
    explanation: str
    
    # Additional results
    alternative_results: List[Any] = field(default_factory=list)
    risk_factors: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    # Clinical decision support
    differential_diagnosis: List[str] = field(default_factory=list)
    suggested_tests: List[str] = field(default_factory=list)
    contraindications: List[str] = field(default_factory=list)
    
    # Compliance and safety
    safety_warnings: List[str] = field(default_factory=list)
    compliance_notes: List[str] = field(default_factory=list)
    audit_trail: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    processing_time: float = 0.0
    model_version: str = "1.0.0"
    timestamp: float = field(default_factory=time.time)


@dataclass
class MedicalKnowledgeBase:
    """Medical knowledge base entry."""
    entry_id: str
    specialty: MedicalSpecialty
    category: str
    
    # Content
    title: str
    description: str
    content: Dict[str, Any]
    
    # Medical information
    icd_codes: List[str] = field(default_factory=list)
    cpt_codes: List[str] = field(default_factory=list)
    drug_names: List[str] = field(default_factory=list)
    
    # Evidence and sources
    evidence_level: str = "C"  # A, B, C, D
    sources: List[str] = field(default_factory=list)
    last_updated: float = field(default_factory=time.time)


class PHIDetector:
    """Protected Health Information detector."""
    
    def __init__(self):
        # PHI patterns (simplified for demo)
        self.phi_patterns = {
            'ssn': r'\b\d{3}-\d{2}-\d{4}\b',
            'phone': r'\b\d{3}-\d{3}-\d{4}\b',
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'mrn': r'\bMRN:?\s*\d{6,10}\b',
            'date_of_birth': r'\b\d{1,2}/\d{1,2}/\d{4}\b',
            'address': r'\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln)\b'
        }
    
    def detect_phi(self, text: str) -> Dict[str, List[str]]:
        """Detect PHI in text."""
        detected_phi = {}
        
        for phi_type, pattern in self.phi_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                detected_phi[phi_type] = matches
        
        return detected_phi
    
    def anonymize_text(self, text: str) -> str:
        """Anonymize PHI in text."""
        anonymized_text = text
        
        for phi_type, pattern in self.phi_patterns.items():
            if phi_type == 'ssn':
                anonymized_text = re.sub(pattern, 'XXX-XX-XXXX', anonymized_text)
            elif phi_type == 'phone':
                anonymized_text = re.sub(pattern, 'XXX-XXX-XXXX', anonymized_text)
            elif phi_type == 'email':
                anonymized_text = re.sub(pattern, '[EMAIL_REDACTED]', anonymized_text)
            elif phi_type == 'mrn':
                anonymized_text = re.sub(pattern, 'MRN: [REDACTED]', anonymized_text)
            elif phi_type == 'date_of_birth':
                anonymized_text = re.sub(pattern, '[DOB_REDACTED]', anonymized_text)
            elif phi_type == 'address':
                anonymized_text = re.sub(pattern, '[ADDRESS_REDACTED]', anonymized_text)
        
        return anonymized_text


class MedicalTerminologyProcessor:
    """Medical terminology and coding processor."""
    
    def __init__(self):
        # Simplified medical terminology database
        self.icd10_codes = {
            "hypertension": "I10",
            "diabetes": "E11.9",
            "pneumonia": "J18.9",
            "myocardial infarction": "I21.9",
            "stroke": "I63.9",
            "depression": "F32.9",
            "anxiety": "F41.9",
            "asthma": "J45.9"
        }
        
        self.cpt_codes = {
            "office visit": "99213",
            "chest x-ray": "71020",
            "ecg": "93000",
            "blood test": "80053",
            "mri brain": "70553",
            "ct scan": "74150"
        }
        
        self.drug_interactions = {
            "warfarin": ["aspirin", "ibuprofen", "amiodarone"],
            "metformin": ["contrast dye", "alcohol"],
            "digoxin": ["amiodarone", "quinidine", "verapamil"]
        }
    
    def extract_medical_terms(self, text: str) -> Dict[str, List[str]]:
        """Extract medical terms from text."""
        text_lower = text.lower()
        
        found_conditions = []
        found_procedures = []
        found_medications = []
        
        # Find ICD codes
        for condition, code in self.icd10_codes.items():
            if condition in text_lower:
                found_conditions.append({"term": condition, "icd10": code})
        
        # Find CPT codes
        for procedure, code in self.cpt_codes.items():
            if procedure in text_lower:
                found_procedures.append({"term": procedure, "cpt": code})
        
        # Find medications
        for drug in self.drug_interactions.keys():
            if drug in text_lower:
                found_medications.append(drug)
        
        return {
            "conditions": found_conditions,
            "procedures": found_procedures,
            "medications": found_medications
        }
    
    def check_drug_interactions(self, medications: List[str]) -> List[Dict[str, Any]]:
        """Check for drug interactions."""
        interactions = []
        
        for i, drug1 in enumerate(medications):
            for j, drug2 in enumerate(medications[i+1:], i+1):
                if drug1 in self.drug_interactions:
                    if drug2 in self.drug_interactions[drug1]:
                        interactions.append({
                            "drug1": drug1,
                            "drug2": drug2,
                            "severity": "moderate",
                            "description": f"Potential interaction between {drug1} and {drug2}"
                        })
        
        return interactions


class HealthcareAIEngine:
    """Healthcare AI engine with HIPAA compliance and medical specialization."""
    
    def __init__(self):
        self.phi_detector = PHIDetector()
        self.terminology_processor = MedicalTerminologyProcessor()
        self.knowledge_base: Dict[str, MedicalKnowledgeBase] = {}
        
        # Processing history for audit
        self.processing_history: List[MedicalAIResponse] = []
        self.audit_logs: List[Dict[str, Any]] = []
        
        # Compliance settings
        self.hipaa_compliance_enabled = True
        self.audit_all_requests = True
        self.phi_detection_enabled = True
        
        # Specialty models (mock configuration)
        self.specialty_models = {
            MedicalSpecialty.CARDIOLOGY: {
                "model_id": "medical-cardiology-v1",
                "confidence_threshold": 0.85,
                "requires_peer_review": True
            },
            MedicalSpecialty.ONCOLOGY: {
                "model_id": "medical-oncology-v1",
                "confidence_threshold": 0.90,
                "requires_peer_review": True
            },
            MedicalSpecialty.RADIOLOGY: {
                "model_id": "medical-radiology-v1",
                "confidence_threshold": 0.88,
                "requires_peer_review": False
            }
        }
        
        # Initialize knowledge base
        self._initialize_knowledge_base()
        
        logger.info("Healthcare AI Engine initialized with HIPAA compliance")
    
    def _initialize_knowledge_base(self):
        """Initialize medical knowledge base."""
        # Sample knowledge base entries
        entries = [
            MedicalKnowledgeBase(
                entry_id="kb_001",
                specialty=MedicalSpecialty.CARDIOLOGY,
                category="diagnosis",
                title="Acute Myocardial Infarction",
                description="Heart attack diagnosis and treatment guidelines",
                content={
                    "symptoms": ["chest pain", "shortness of breath", "nausea"],
                    "tests": ["ECG", "troponin", "CK-MB"],
                    "treatment": ["aspirin", "clopidogrel", "statin", "beta-blocker"]
                },
                icd_codes=["I21.9"],
                evidence_level="A"
            ),
            MedicalKnowledgeBase(
                entry_id="kb_002",
                specialty=MedicalSpecialty.GENERAL_MEDICINE,
                category="diagnosis",
                title="Type 2 Diabetes Mellitus",
                description="Diabetes diagnosis and management",
                content={
                    "symptoms": ["polyuria", "polydipsia", "weight loss"],
                    "tests": ["HbA1c", "fasting glucose", "OGTT"],
                    "treatment": ["metformin", "lifestyle modification", "monitoring"]
                },
                icd_codes=["E11.9"],
                evidence_level="A"
            )
        ]
        
        for entry in entries:
            self.knowledge_base[entry.entry_id] = entry
    
    async def process_medical_request(self, request: MedicalAIRequest) -> MedicalAIResponse:
        """Process medical AI request with HIPAA compliance."""
        start_time = time.time()
        
        try:
            # Audit logging
            if self.audit_all_requests:
                await self._log_audit_event("request_received", request)
            
            # PHI detection and handling
            if self.phi_detection_enabled:
                phi_detected = self.phi_detector.detect_phi(request.input_text)
                if phi_detected and request.medical_context.compliance_level == HIPAACompliance.STRICT:
                    # Anonymize PHI
                    request.input_text = self.phi_detector.anonymize_text(request.input_text)
                    await self._log_audit_event("phi_anonymized", {"phi_types": list(phi_detected.keys())})
            
            # Process based on capability
            if request.capability == MedicalAICapability.CLINICAL_DECISION_SUPPORT:
                response = await self._process_clinical_decision_support(request)
            elif request.capability == MedicalAICapability.DIAGNOSTIC_ASSISTANCE:
                response = await self._process_diagnostic_assistance(request)
            elif request.capability == MedicalAICapability.DRUG_INTERACTION_CHECK:
                response = await self._process_drug_interaction_check(request)
            elif request.capability == MedicalAICapability.MEDICAL_CODING:
                response = await self._process_medical_coding(request)
            elif request.capability == MedicalAICapability.SYMPTOM_ANALYSIS:
                response = await self._process_symptom_analysis(request)
            else:
                response = await self._process_general_medical_query(request)
            
            # Calculate processing time
            response.processing_time = time.time() - start_time
            
            # Compliance checks
            await self._perform_compliance_checks(request, response)
            
            # Store for audit
            self.processing_history.append(response)
            
            # Audit logging
            if self.audit_all_requests:
                await self._log_audit_event("request_completed", response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing medical request: {e}")
            
            # Create error response
            error_response = MedicalAIResponse(
                request_id=request.request_id,
                capability=request.capability,
                primary_result=None,
                confidence_score=0.0,
                explanation=f"Processing error: {str(e)}",
                safety_warnings=["Processing failed - manual review required"],
                processing_time=time.time() - start_time
            )
            
            await self._log_audit_event("request_failed", {"error": str(e)})
            return error_response
    
    async def _process_clinical_decision_support(self, request: MedicalAIRequest) -> MedicalAIResponse:
        """Process clinical decision support request."""
        # Extract medical terms
        medical_terms = self.terminology_processor.extract_medical_terms(request.input_text)
        
        # Find relevant knowledge base entries
        relevant_entries = []
        for entry in self.knowledge_base.values():
            if entry.specialty == request.medical_context.specialty:
                relevant_entries.append(entry)
        
        # Generate recommendations
        recommendations = []
        differential_diagnosis = []
        suggested_tests = []
        
        if medical_terms["conditions"]:
            for condition in medical_terms["conditions"]:
                # Find matching knowledge base entry
                for entry in relevant_entries:
                    if condition["term"] in entry.title.lower():
                        recommendations.extend(entry.content.get("treatment", []))
                        suggested_tests.extend(entry.content.get("tests", []))
        
        # Mock differential diagnosis
        if "chest pain" in request.input_text.lower():
            differential_diagnosis = [
                "Acute myocardial infarction",
                "Unstable angina",
                "Pulmonary embolism",
                "Aortic dissection",
                "Pneumothorax"
            ]
        
        return MedicalAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result="Clinical decision support provided",
            confidence_score=0.85,
            explanation="Based on clinical presentation and medical history",
            recommendations=recommendations[:5],  # Limit to top 5
            differential_diagnosis=differential_diagnosis,
            suggested_tests=suggested_tests[:3],  # Limit to top 3
            compliance_notes=["HIPAA compliant processing", "Requires physician review"]
        )
    
    async def _process_diagnostic_assistance(self, request: MedicalAIRequest) -> MedicalAIResponse:
        """Process diagnostic assistance request."""
        # Extract symptoms and findings
        symptoms = []
        if "symptoms:" in request.input_text.lower():
            symptoms_text = request.input_text.lower().split("symptoms:")[1]
            symptoms = [s.strip() for s in symptoms_text.split(",")]
        
        # Generate diagnostic suggestions
        diagnostic_suggestions = []
        confidence = 0.7
        
        # Simple rule-based diagnostic assistance
        if any(symptom in ["chest pain", "shortness of breath"] for symptom in symptoms):
            diagnostic_suggestions.append("Consider cardiac evaluation")
            confidence = 0.8
        
        if any(symptom in ["fever", "cough", "fatigue"] for symptom in symptoms):
            diagnostic_suggestions.append("Consider infectious etiology")
            confidence = 0.75
        
        return MedicalAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result=diagnostic_suggestions[0] if diagnostic_suggestions else "Further evaluation needed",
            confidence_score=confidence,
            explanation="Based on symptom analysis and clinical patterns",
            alternative_results=diagnostic_suggestions[1:],
            safety_warnings=["Diagnostic assistance only - not a substitute for clinical judgment"],
            compliance_notes=["Requires physician interpretation"]
        )
    
    async def _process_drug_interaction_check(self, request: MedicalAIRequest) -> MedicalAIResponse:
        """Process drug interaction check."""
        # Extract medications
        medications = request.medical_context.current_medications
        if not medications:
            # Try to extract from input text
            medical_terms = self.terminology_processor.extract_medical_terms(request.input_text)
            medications = medical_terms.get("medications", [])
        
        # Check interactions
        interactions = self.terminology_processor.check_drug_interactions(medications)
        
        # Generate response
        if interactions:
            primary_result = f"Found {len(interactions)} potential drug interactions"
            safety_warnings = [f"Interaction: {i['drug1']} and {i['drug2']}" for i in interactions]
            confidence = 0.9
        else:
            primary_result = "No significant drug interactions detected"
            safety_warnings = []
            confidence = 0.85
        
        return MedicalAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result=primary_result,
            confidence_score=confidence,
            explanation="Drug interaction analysis based on known interaction database",
            safety_warnings=safety_warnings,
            recommendations=["Consult pharmacist for detailed review"] if interactions else [],
            compliance_notes=["Drug interaction check completed"]
        )
    
    async def _process_medical_coding(self, request: MedicalAIRequest) -> MedicalAIResponse:
        """Process medical coding request."""
        # Extract medical terms and suggest codes
        medical_terms = self.terminology_processor.extract_medical_terms(request.input_text)
        
        coding_results = {
            "icd10_codes": [],
            "cpt_codes": []
        }
        
        # Add ICD-10 codes
        for condition in medical_terms["conditions"]:
            coding_results["icd10_codes"].append({
                "condition": condition["term"],
                "code": condition["icd10"],
                "description": f"ICD-10 code for {condition['term']}"
            })
        
        # Add CPT codes
        for procedure in medical_terms["procedures"]:
            coding_results["cpt_codes"].append({
                "procedure": procedure["term"],
                "code": procedure["cpt"],
                "description": f"CPT code for {procedure['term']}"
            })
        
        return MedicalAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result=coding_results,
            confidence_score=0.8,
            explanation="Medical coding based on terminology extraction",
            compliance_notes=["Coding suggestions require verification", "Billing compliance review needed"]
        )
    
    async def _process_symptom_analysis(self, request: MedicalAIRequest) -> MedicalAIResponse:
        """Process symptom analysis request."""
        # Simple symptom analysis
        symptoms_text = request.input_text.lower()
        
        # Symptom severity assessment
        severity_indicators = {
            "severe": ["severe", "excruciating", "unbearable", "10/10"],
            "moderate": ["moderate", "significant", "bothersome"],
            "mild": ["mild", "slight", "minimal"]
        }
        
        severity = "unknown"
        for level, indicators in severity_indicators.items():
            if any(indicator in symptoms_text for indicator in indicators):
                severity = level
                break
        
        # Generate analysis
        analysis_result = {
            "severity": severity,
            "urgency": "routine" if severity == "mild" else "urgent" if severity == "severe" else "semi-urgent",
            "triage_recommendation": "Emergency evaluation" if severity == "severe" else "Routine appointment"
        }
        
        return MedicalAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result=analysis_result,
            confidence_score=0.75,
            explanation="Symptom analysis based on severity indicators and clinical patterns",
            safety_warnings=["Symptom analysis is preliminary - clinical evaluation required"],
            compliance_notes=["Triage assistance only"]
        )
    
    async def _process_general_medical_query(self, request: MedicalAIRequest) -> MedicalAIResponse:
        """Process general medical query."""
        return MedicalAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result="General medical information provided",
            confidence_score=0.7,
            explanation="General medical query processed",
            safety_warnings=["For informational purposes only - not medical advice"],
            compliance_notes=["General information - not patient-specific advice"]
        )
    
    async def _perform_compliance_checks(self, request: MedicalAIRequest, response: MedicalAIResponse):
        """Perform HIPAA compliance checks."""
        compliance_issues = []
        
        # Check if PHI was properly handled
        if self.phi_detection_enabled:
            phi_in_response = self.phi_detector.detect_phi(str(response.primary_result))
            if phi_in_response:
                compliance_issues.append("PHI detected in response")
        
        # Check confidence threshold
        specialty_config = self.specialty_models.get(request.medical_context.specialty)
        if specialty_config:
            min_confidence = specialty_config.get("confidence_threshold", 0.8)
            if response.confidence_score < min_confidence:
                compliance_issues.append(f"Confidence below threshold ({min_confidence})")
        
        # Add compliance notes
        if compliance_issues:
            response.compliance_notes.extend(compliance_issues)
            response.safety_warnings.append("Manual review required due to compliance issues")
    
    async def _log_audit_event(self, event_type: str, data: Any):
        """Log audit event for HIPAA compliance."""
        audit_entry = {
            "event_id": str(uuid.uuid4()),
            "event_type": event_type,
            "timestamp": time.time(),
            "data": data if isinstance(data, dict) else {"info": str(data)},
            "user_id": "system",
            "ip_address": "127.0.0.1"
        }
        
        self.audit_logs.append(audit_entry)
        
        # Keep only last 10000 audit entries
        if len(self.audit_logs) > 10000:
            self.audit_logs = self.audit_logs[-10000:]
    
    def get_healthcare_status(self) -> Dict[str, Any]:
        """Get healthcare AI engine status."""
        return {
            "hipaa_compliance_enabled": self.hipaa_compliance_enabled,
            "phi_detection_enabled": self.phi_detection_enabled,
            "audit_all_requests": self.audit_all_requests,
            "total_requests_processed": len(self.processing_history),
            "knowledge_base_entries": len(self.knowledge_base),
            "audit_log_entries": len(self.audit_logs),
            "supported_specialties": [s.value for s in MedicalSpecialty],
            "supported_capabilities": [c.value for c in MedicalAICapability],
            "recent_processing_stats": {
                "last_hour": len([
                    r for r in self.processing_history 
                    if time.time() - r.timestamp < 3600
                ]),
                "average_confidence": sum(r.confidence_score for r in self.processing_history[-100:]) / min(100, len(self.processing_history)) if self.processing_history else 0.0,
                "average_processing_time": sum(r.processing_time for r in self.processing_history[-100:]) / min(100, len(self.processing_history)) if self.processing_history else 0.0
            }
        }


# Global healthcare AI engine instance
healthcare_ai_engine = HealthcareAIEngine()
