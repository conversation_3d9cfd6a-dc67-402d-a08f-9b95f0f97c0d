"""
Advanced rate limiting and throttling system.
"""
import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import redis.asyncio as redis

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class RateLimitType(Enum):
    """Rate limit types."""
    REQUESTS_PER_MINUTE = "requests_per_minute"
    REQUESTS_PER_HOUR = "requests_per_hour"
    REQUESTS_PER_DAY = "requests_per_day"
    TOKENS_PER_MINUTE = "tokens_per_minute"
    TOKENS_PER_HOUR = "tokens_per_hour"
    CONCURRENT_REQUESTS = "concurrent_requests"
    BANDWIDTH_PER_SECOND = "bandwidth_per_second"


class ThrottleStrategy(Enum):
    """Throttling strategies."""
    REJECT = "reject"           # Reject requests when limit exceeded
    QUEUE = "queue"             # Queue requests when limit exceeded
    DELAY = "delay"             # Add delay when limit exceeded
    ADAPTIVE = "adaptive"       # Adaptive throttling based on load


@dataclass
class RateLimit:
    """Rate limit configuration."""
    limit_type: RateLimitType
    limit: int
    window_seconds: int
    strategy: ThrottleStrategy = ThrottleStrategy.REJECT
    burst_limit: Optional[int] = None
    queue_size: Optional[int] = None
    delay_seconds: Optional[float] = None


@dataclass
class RateLimitRule:
    """Rate limiting rule."""
    name: str
    limits: List[RateLimit]
    applies_to: str  # user_id, api_key, ip_address, tenant_id, global
    priority: int = 0
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RateLimitStatus:
    """Current rate limit status."""
    identifier: str
    limit_type: RateLimitType
    current_count: int
    limit: int
    window_start: datetime
    window_end: datetime
    remaining: int
    reset_time: datetime
    is_exceeded: bool


class TokenBucket:
    """Token bucket algorithm implementation."""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.refill_rate = refill_rate  # tokens per second
        self.tokens = capacity
        self.last_refill = time.time()
        self.lock = asyncio.Lock()
    
    async def consume(self, tokens: int = 1) -> bool:
        """Try to consume tokens from bucket."""
        async with self.lock:
            now = time.time()
            
            # Refill tokens based on time elapsed
            time_elapsed = now - self.last_refill
            tokens_to_add = time_elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = now
            
            # Check if we have enough tokens
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get bucket status."""
        return {
            "capacity": self.capacity,
            "current_tokens": self.tokens,
            "refill_rate": self.refill_rate,
            "last_refill": self.last_refill
        }


class SlidingWindowCounter:
    """Sliding window counter implementation."""
    
    def __init__(self, window_seconds: int, max_requests: int):
        self.window_seconds = window_seconds
        self.max_requests = max_requests
        self.requests = deque()
        self.lock = asyncio.Lock()
    
    async def is_allowed(self) -> bool:
        """Check if request is allowed."""
        async with self.lock:
            now = time.time()
            
            # Remove old requests outside the window
            while self.requests and self.requests[0] <= now - self.window_seconds:
                self.requests.popleft()
            
            # Check if we're under the limit
            if len(self.requests) < self.max_requests:
                self.requests.append(now)
                return True
            
            return False
    
    def get_count(self) -> int:
        """Get current request count."""
        now = time.time()
        
        # Count requests in current window
        count = 0
        for request_time in self.requests:
            if request_time > now - self.window_seconds:
                count += 1
        
        return count
    
    def get_reset_time(self) -> float:
        """Get time when oldest request will expire."""
        if not self.requests:
            return time.time()
        
        return self.requests[0] + self.window_seconds


class RateLimiter:
    """Advanced rate limiting and throttling system."""
    
    def __init__(self, redis_url: Optional[str] = None):
        # Rate limiting rules
        self.rules: Dict[str, RateLimitRule] = {}
        self.default_rules: List[RateLimitRule] = []
        
        # In-memory rate limiters
        self.token_buckets: Dict[str, TokenBucket] = {}
        self.sliding_windows: Dict[str, SlidingWindowCounter] = {}
        self.concurrent_requests: Dict[str, int] = defaultdict(int)
        
        # Redis for distributed rate limiting
        self.redis_client: Optional[redis.Redis] = None
        if redis_url:
            self.redis_client = redis.from_url(redis_url)
        
        # Request queues for throttling
        self.request_queues: Dict[str, asyncio.Queue] = {}
        self.queue_processors: Dict[str, asyncio.Task] = {}
        
        # Statistics
        self.total_requests = 0
        self.blocked_requests = 0
        self.queued_requests = 0
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self._start_background_tasks()
        
        # Initialize default rules
        self._initialize_default_rules()
        
        logger.info("Rate limiter initialized")
    
    def _start_background_tasks(self):
        """Start background tasks."""
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    def _initialize_default_rules(self):
        """Initialize default rate limiting rules."""
        # Global rate limits
        global_rule = RateLimitRule(
            name="global_limits",
            limits=[
                RateLimit(RateLimitType.REQUESTS_PER_MINUTE, 1000, 60),
                RateLimit(RateLimitType.REQUESTS_PER_HOUR, 10000, 3600),
                RateLimit(RateLimitType.CONCURRENT_REQUESTS, 100, 0)
            ],
            applies_to="global",
            priority=0
        )
        
        # User rate limits
        user_rule = RateLimitRule(
            name="user_limits",
            limits=[
                RateLimit(RateLimitType.REQUESTS_PER_MINUTE, 100, 60),
                RateLimit(RateLimitType.REQUESTS_PER_HOUR, 1000, 3600),
                RateLimit(RateLimitType.TOKENS_PER_MINUTE, 10000, 60),
                RateLimit(RateLimitType.CONCURRENT_REQUESTS, 10, 0)
            ],
            applies_to="user_id",
            priority=1
        )
        
        # API key rate limits
        api_key_rule = RateLimitRule(
            name="api_key_limits",
            limits=[
                RateLimit(RateLimitType.REQUESTS_PER_MINUTE, 200, 60),
                RateLimit(RateLimitType.REQUESTS_PER_HOUR, 2000, 3600),
                RateLimit(RateLimitType.TOKENS_PER_MINUTE, 20000, 60)
            ],
            applies_to="api_key",
            priority=2
        )
        
        # IP-based rate limits
        ip_rule = RateLimitRule(
            name="ip_limits",
            limits=[
                RateLimit(RateLimitType.REQUESTS_PER_MINUTE, 50, 60),
                RateLimit(RateLimitType.REQUESTS_PER_HOUR, 500, 3600)
            ],
            applies_to="ip_address",
            priority=3
        )
        
        self.default_rules = [global_rule, user_rule, api_key_rule, ip_rule]
        
        # Add to rules dictionary
        for rule in self.default_rules:
            self.rules[rule.name] = rule
    
    async def check_rate_limit(
        self,
        identifier: str,
        identifier_type: str,
        request_size: int = 1,
        token_count: Optional[int] = None
    ) -> Tuple[bool, List[RateLimitStatus]]:
        """Check if request is within rate limits."""
        self.total_requests += 1
        
        # Get applicable rules
        applicable_rules = self._get_applicable_rules(identifier_type)
        
        statuses = []
        is_allowed = True
        
        for rule in applicable_rules:
            if not rule.is_active:
                continue
            
            for limit in rule.limits:
                status = await self._check_single_limit(
                    identifier, limit, request_size, token_count
                )
                statuses.append(status)
                
                if status.is_exceeded:
                    is_allowed = False
                    
                    # Handle throttling strategy
                    if limit.strategy == ThrottleStrategy.REJECT:
                        break
                    elif limit.strategy == ThrottleStrategy.QUEUE:
                        await self._queue_request(identifier, limit)
                    elif limit.strategy == ThrottleStrategy.DELAY:
                        await self._delay_request(limit)
        
        if not is_allowed:
            self.blocked_requests += 1
            
            # Record metrics
            metrics_collector.record_counter(
                "rate_limit_exceeded_total",
                1.0,
                {"identifier_type": identifier_type, "identifier": identifier}
            )
        
        return is_allowed, statuses
    
    async def _check_single_limit(
        self,
        identifier: str,
        limit: RateLimit,
        request_size: int,
        token_count: Optional[int]
    ) -> RateLimitStatus:
        """Check a single rate limit."""
        key = f"{identifier}:{limit.limit_type.value}"
        
        if limit.limit_type == RateLimitType.CONCURRENT_REQUESTS:
            return await self._check_concurrent_limit(identifier, limit)
        elif limit.limit_type in [RateLimitType.TOKENS_PER_MINUTE, RateLimitType.TOKENS_PER_HOUR]:
            return await self._check_token_limit(identifier, limit, token_count or 0)
        else:
            return await self._check_request_limit(identifier, limit, request_size)
    
    async def _check_concurrent_limit(
        self,
        identifier: str,
        limit: RateLimit
    ) -> RateLimitStatus:
        """Check concurrent request limit."""
        current_count = self.concurrent_requests[identifier]
        is_exceeded = current_count >= limit.limit
        
        return RateLimitStatus(
            identifier=identifier,
            limit_type=limit.limit_type,
            current_count=current_count,
            limit=limit.limit,
            window_start=datetime.utcnow(),
            window_end=datetime.utcnow(),
            remaining=max(0, limit.limit - current_count),
            reset_time=datetime.utcnow(),
            is_exceeded=is_exceeded
        )
    
    async def _check_token_limit(
        self,
        identifier: str,
        limit: RateLimit,
        token_count: int
    ) -> RateLimitStatus:
        """Check token-based rate limit."""
        key = f"{identifier}:{limit.limit_type.value}"
        
        # Use token bucket algorithm
        if key not in self.token_buckets:
            refill_rate = limit.limit / limit.window_seconds
            self.token_buckets[key] = TokenBucket(limit.limit, refill_rate)
        
        bucket = self.token_buckets[key]
        is_allowed = await bucket.consume(token_count)
        
        return RateLimitStatus(
            identifier=identifier,
            limit_type=limit.limit_type,
            current_count=limit.limit - int(bucket.tokens),
            limit=limit.limit,
            window_start=datetime.utcnow(),
            window_end=datetime.utcnow() + timedelta(seconds=limit.window_seconds),
            remaining=int(bucket.tokens),
            reset_time=datetime.utcnow() + timedelta(seconds=limit.window_seconds),
            is_exceeded=not is_allowed
        )
    
    async def _check_request_limit(
        self,
        identifier: str,
        limit: RateLimit,
        request_size: int
    ) -> RateLimitStatus:
        """Check request-based rate limit."""
        key = f"{identifier}:{limit.limit_type.value}"
        
        # Use sliding window counter
        if key not in self.sliding_windows:
            self.sliding_windows[key] = SlidingWindowCounter(
                limit.window_seconds, limit.limit
            )
        
        window = self.sliding_windows[key]
        is_allowed = await window.is_allowed()
        current_count = window.get_count()
        
        return RateLimitStatus(
            identifier=identifier,
            limit_type=limit.limit_type,
            current_count=current_count,
            limit=limit.limit,
            window_start=datetime.utcnow() - timedelta(seconds=limit.window_seconds),
            window_end=datetime.utcnow(),
            remaining=max(0, limit.limit - current_count),
            reset_time=datetime.fromtimestamp(window.get_reset_time()),
            is_exceeded=not is_allowed
        )
    
    def _get_applicable_rules(self, identifier_type: str) -> List[RateLimitRule]:
        """Get applicable rate limiting rules."""
        applicable_rules = []
        
        # Add global rules
        for rule in self.rules.values():
            if rule.applies_to == "global" or rule.applies_to == identifier_type:
                applicable_rules.append(rule)
        
        # Sort by priority
        applicable_rules.sort(key=lambda r: r.priority)
        
        return applicable_rules
    
    async def _queue_request(self, identifier: str, limit: RateLimit):
        """Queue request for later processing."""
        if not limit.queue_size:
            return
        
        queue_key = f"{identifier}:{limit.limit_type.value}"
        
        if queue_key not in self.request_queues:
            self.request_queues[queue_key] = asyncio.Queue(maxsize=limit.queue_size)
            self.queue_processors[queue_key] = asyncio.create_task(
                self._process_queue(queue_key, limit)
            )
        
        try:
            await self.request_queues[queue_key].put_nowait(time.time())
            self.queued_requests += 1
        except asyncio.QueueFull:
            logger.warning(f"Request queue full for {identifier}")
    
    async def _process_queue(self, queue_key: str, limit: RateLimit):
        """Process queued requests."""
        queue = self.request_queues[queue_key]
        
        while True:
            try:
                # Wait for request in queue
                await queue.get()
                
                # Wait for rate limit window
                await asyncio.sleep(limit.window_seconds / limit.limit)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Queue processing error: {e}")
    
    async def _delay_request(self, limit: RateLimit):
        """Add delay to request."""
        if limit.delay_seconds:
            await asyncio.sleep(limit.delay_seconds)
    
    async def increment_concurrent(self, identifier: str):
        """Increment concurrent request count."""
        self.concurrent_requests[identifier] += 1
    
    async def decrement_concurrent(self, identifier: str):
        """Decrement concurrent request count."""
        if self.concurrent_requests[identifier] > 0:
            self.concurrent_requests[identifier] -= 1
    
    def add_rule(self, rule: RateLimitRule):
        """Add custom rate limiting rule."""
        self.rules[rule.name] = rule
        logger.info(f"Added rate limiting rule: {rule.name}")
    
    def remove_rule(self, rule_name: str) -> bool:
        """Remove rate limiting rule."""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed rate limiting rule: {rule_name}")
            return True
        return False
    
    def get_rate_limit_stats(self) -> Dict[str, Any]:
        """Get rate limiting statistics."""
        return {
            "total_requests": self.total_requests,
            "blocked_requests": self.blocked_requests,
            "queued_requests": self.queued_requests,
            "block_rate": self.blocked_requests / max(1, self.total_requests),
            "active_rules": len([r for r in self.rules.values() if r.is_active]),
            "total_rules": len(self.rules),
            "active_buckets": len(self.token_buckets),
            "active_windows": len(self.sliding_windows),
            "active_queues": len(self.request_queues),
            "concurrent_requests": dict(self.concurrent_requests)
        }
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
                # Clean up old token buckets
                current_time = time.time()
                buckets_to_remove = []
                
                for key, bucket in self.token_buckets.items():
                    if current_time - bucket.last_refill > 3600:  # 1 hour
                        buckets_to_remove.append(key)
                
                for key in buckets_to_remove:
                    del self.token_buckets[key]
                    logger.debug(f"Cleaned up old token bucket: {key}")
                
                # Clean up empty concurrent request counters
                empty_counters = [
                    key for key, count in self.concurrent_requests.items()
                    if count == 0
                ]
                
                for key in empty_counters:
                    del self.concurrent_requests[key]
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Rate limiter cleanup error: {e}")
    
    async def shutdown(self):
        """Shutdown rate limiter."""
        # Cancel background tasks
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Cancel queue processors
        for task in self.queue_processors.values():
            task.cancel()
        
        # Close Redis connection
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("Rate limiter shutdown completed")


# Global rate limiter instance
rate_limiter = RateLimiter()
