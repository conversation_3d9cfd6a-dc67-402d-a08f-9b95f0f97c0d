"""
JSON Mode utilities for structured output generation.
"""
import json
import logging
from typing import Any, Dict, List, Optional, Union, Type
from pydantic import BaseModel, ValidationError

from ai_service.models.schemas import JSONSchema

logger = logging.getLogger(__name__)


class JSONModeHandler:
    """Handler for JSON mode operations."""
    
    def __init__(self):
        self.schema_cache: Dict[str, Dict[str, Any]] = {}
    
    def validate_json_schema(self, schema: Union[JSONSchema, Dict[str, Any]]) -> Dict[str, Any]:
        """Validate and convert JSON schema to Gemini API format.
        
        Args:
            schema: JSON schema definition
            
        Returns:
            Validated schema in Gemini API format
            
        Raises:
            ValueError: If schema is invalid
        """
        try:
            if isinstance(schema, JSONSchema):
                schema_dict = schema.model_dump(exclude_none=True)
            else:
                schema_dict = schema
            
            # Validate required fields
            if "type" not in schema_dict:
                raise ValueError("Schema must have a 'type' field")
            
            # Convert to Gemini API format
            gemini_schema = self._convert_to_gemini_format(schema_dict)
            
            # Cache the schema
            schema_key = json.dumps(schema_dict, sort_keys=True)
            self.schema_cache[schema_key] = gemini_schema
            
            return gemini_schema
            
        except Exception as e:
            logger.error(f"Schema validation failed: {e}")
            raise ValueError(f"Invalid JSON schema: {e}")
    
    def _convert_to_gemini_format(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Convert schema to Gemini API format.
        
        Args:
            schema: Schema dictionary
            
        Returns:
            Schema in Gemini API format
        """
        gemini_schema = {}
        
        # Map type
        schema_type = schema.get("type", "").lower()
        type_mapping = {
            "string": "STRING",
            "integer": "INTEGER", 
            "number": "NUMBER",
            "boolean": "BOOLEAN",
            "array": "ARRAY",
            "object": "OBJECT"
        }
        
        if schema_type in type_mapping:
            gemini_schema["type"] = type_mapping[schema_type]
        else:
            raise ValueError(f"Unsupported schema type: {schema_type}")
        
        # Add description
        if "description" in schema:
            gemini_schema["description"] = schema["description"]
        
        # Handle object properties
        if schema_type == "object":
            if "properties" in schema:
                gemini_schema["properties"] = {}
                for prop_name, prop_schema in schema["properties"].items():
                    gemini_schema["properties"][prop_name] = self._convert_to_gemini_format(prop_schema)
            
            if "required" in schema:
                gemini_schema["required"] = schema["required"]
            
            if "property_ordering" in schema:
                gemini_schema["propertyOrdering"] = schema["property_ordering"]
        
        # Handle array items
        if schema_type == "array":
            if "items" in schema:
                gemini_schema["items"] = self._convert_to_gemini_format(schema["items"])
            
            if "min_items" in schema:
                gemini_schema["minItems"] = schema["min_items"]
            
            if "max_items" in schema:
                gemini_schema["maxItems"] = schema["max_items"]
        
        # Handle string constraints
        if schema_type == "string":
            if "enum" in schema:
                gemini_schema["enum"] = schema["enum"]
            
            if "format" in schema:
                gemini_schema["format"] = schema["format"]
        
        # Handle number constraints
        if schema_type in ["integer", "number"]:
            if "minimum" in schema:
                gemini_schema["minimum"] = schema["minimum"]
            
            if "maximum" in schema:
                gemini_schema["maximum"] = schema["maximum"]
            
            if "enum" in schema:
                gemini_schema["enum"] = schema["enum"]
        
        # Handle nullable
        if "nullable" in schema:
            gemini_schema["nullable"] = schema["nullable"]
        
        return gemini_schema
    
    def create_generation_config(
        self, 
        json_mode: bool = False, 
        json_schema: Optional[Union[JSONSchema, Dict[str, Any]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Create generation config with JSON mode support.
        
        Args:
            json_mode: Enable JSON mode
            json_schema: JSON schema for structured output
            **kwargs: Additional generation config parameters
            
        Returns:
            Generation config dictionary
        """
        config = kwargs.copy()
        
        if json_mode:
            config["response_mime_type"] = "application/json"
            
            if json_schema:
                validated_schema = self.validate_json_schema(json_schema)
                config["response_schema"] = validated_schema
        
        return config
    
    def validate_json_response(self, response_text: str, schema: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Validate JSON response against schema.
        
        Args:
            response_text: JSON response text
            schema: Optional schema for validation
            
        Returns:
            Parsed JSON data
            
        Raises:
            ValueError: If JSON is invalid
        """
        try:
            # Parse JSON
            json_data = json.loads(response_text)
            
            # Basic validation against schema if provided
            if schema:
                self._validate_against_schema(json_data, schema)
            
            return json_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON response: {e}")
            raise ValueError(f"Invalid JSON response: {e}")
        except Exception as e:
            logger.error(f"JSON validation failed: {e}")
            raise ValueError(f"JSON validation failed: {e}")
    
    def _validate_against_schema(self, data: Any, schema: Dict[str, Any]) -> None:
        """Basic validation against schema.
        
        Args:
            data: Data to validate
            schema: Schema to validate against
            
        Raises:
            ValueError: If validation fails
        """
        schema_type = schema.get("type", "").upper()
        
        # Type validation
        if schema_type == "OBJECT" and not isinstance(data, dict):
            raise ValueError(f"Expected object, got {type(data).__name__}")
        elif schema_type == "ARRAY" and not isinstance(data, list):
            raise ValueError(f"Expected array, got {type(data).__name__}")
        elif schema_type == "STRING" and not isinstance(data, str):
            raise ValueError(f"Expected string, got {type(data).__name__}")
        elif schema_type == "INTEGER" and not isinstance(data, int):
            raise ValueError(f"Expected integer, got {type(data).__name__}")
        elif schema_type == "NUMBER" and not isinstance(data, (int, float)):
            raise ValueError(f"Expected number, got {type(data).__name__}")
        elif schema_type == "BOOLEAN" and not isinstance(data, bool):
            raise ValueError(f"Expected boolean, got {type(data).__name__}")
        
        # Object property validation
        if schema_type == "OBJECT" and isinstance(data, dict):
            required_props = schema.get("required", [])
            for prop in required_props:
                if prop not in data:
                    raise ValueError(f"Required property '{prop}' is missing")
            
            # Validate nested properties
            properties = schema.get("properties", {})
            for prop_name, prop_value in data.items():
                if prop_name in properties:
                    self._validate_against_schema(prop_value, properties[prop_name])
        
        # Array validation
        if schema_type == "ARRAY" and isinstance(data, list):
            min_items = schema.get("minItems")
            max_items = schema.get("maxItems")
            
            if min_items is not None and len(data) < min_items:
                raise ValueError(f"Array has {len(data)} items, minimum is {min_items}")
            
            if max_items is not None and len(data) > max_items:
                raise ValueError(f"Array has {len(data)} items, maximum is {max_items}")
            
            # Validate array items
            items_schema = schema.get("items")
            if items_schema:
                for item in data:
                    self._validate_against_schema(item, items_schema)
        
        # Enum validation
        if "enum" in schema and data not in schema["enum"]:
            raise ValueError(f"Value '{data}' is not in allowed enum values: {schema['enum']}")


def create_simple_schema(
    schema_type: str,
    properties: Optional[Dict[str, Any]] = None,
    required: Optional[List[str]] = None,
    description: Optional[str] = None,
    **kwargs
) -> JSONSchema:
    """Create a simple JSON schema.
    
    Args:
        schema_type: Schema type (object, array, string, etc.)
        properties: Object properties
        required: Required properties
        description: Schema description
        **kwargs: Additional schema parameters
        
    Returns:
        JSONSchema instance
    """
    return JSONSchema(
        type=schema_type,
        properties=properties,
        required=required,
        description=description,
        **kwargs
    )


def create_object_schema(
    properties: Dict[str, Any],
    required: Optional[List[str]] = None,
    description: Optional[str] = None,
    property_ordering: Optional[List[str]] = None
) -> JSONSchema:
    """Create an object schema.
    
    Args:
        properties: Object properties
        required: Required properties
        description: Schema description
        property_ordering: Property ordering
        
    Returns:
        JSONSchema instance
    """
    return JSONSchema(
        type="object",
        properties=properties,
        required=required,
        description=description,
        property_ordering=property_ordering
    )


def create_array_schema(
    items: Dict[str, Any],
    min_items: Optional[int] = None,
    max_items: Optional[int] = None,
    description: Optional[str] = None
) -> JSONSchema:
    """Create an array schema.
    
    Args:
        items: Array item schema
        min_items: Minimum array length
        max_items: Maximum array length
        description: Schema description
        
    Returns:
        JSONSchema instance
    """
    return JSONSchema(
        type="array",
        items=items,
        min_items=min_items,
        max_items=max_items,
        description=description
    )


# Global JSON mode handler
json_mode_handler = JSONModeHandler()
