"""
Health checking system for production readiness.
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import psutil
import google.generativeai as genai

from ai_service.core.config import settings
from ai_service.core.exceptions import AIServiceException

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    name: str
    status: HealthStatus
    message: str
    duration_ms: float
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None


@dataclass
class ServiceHealth:
    """Overall service health status."""
    status: HealthStatus
    timestamp: datetime
    version: str
    uptime_seconds: float
    checks: List[HealthCheckResult]
    summary: Dict[str, Any]


class HealthChecker:
    """Comprehensive health checking system."""
    
    def __init__(self):
        self.start_time = time.time()
        self.check_registry: Dict[str, callable] = {}
        self.last_check_results: Dict[str, HealthCheckResult] = {}
        
        # Register default health checks
        self._register_default_checks()
        
        logger.info("Health checker initialized")
    
    def _register_default_checks(self):
        """Register default health checks."""
        self.register_check("system_resources", self._check_system_resources)
        self.register_check("gemini_api", self._check_gemini_api)
        self.register_check("memory_usage", self._check_memory_usage)
        self.register_check("disk_space", self._check_disk_space)
        self.register_check("service_dependencies", self._check_service_dependencies)
    
    def register_check(self, name: str, check_func: callable):
        """Register a health check function.
        
        Args:
            name: Name of the health check
            check_func: Async function that returns HealthCheckResult
        """
        self.check_registry[name] = check_func
        logger.info(f"Registered health check: {name}")
    
    async def run_check(self, check_name: str) -> HealthCheckResult:
        """Run a specific health check.
        
        Args:
            check_name: Name of the check to run
            
        Returns:
            Health check result
        """
        if check_name not in self.check_registry:
            return HealthCheckResult(
                name=check_name,
                status=HealthStatus.UNKNOWN,
                message=f"Health check '{check_name}' not found",
                duration_ms=0.0,
                timestamp=datetime.utcnow()
            )
        
        start_time = time.time()
        
        try:
            check_func = self.check_registry[check_name]
            result = await check_func()
            
            # Ensure result has correct timing
            result.duration_ms = (time.time() - start_time) * 1000
            result.timestamp = datetime.utcnow()
            
            # Cache result
            self.last_check_results[check_name] = result
            
            return result
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(f"Health check '{check_name}' failed: {e}")
            
            result = HealthCheckResult(
                name=check_name,
                status=HealthStatus.UNHEALTHY,
                message=f"Check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={"error": str(e), "error_type": type(e).__name__}
            )
            
            self.last_check_results[check_name] = result
            return result
    
    async def run_all_checks(self) -> List[HealthCheckResult]:
        """Run all registered health checks.
        
        Returns:
            List of health check results
        """
        tasks = []
        for check_name in self.check_registry.keys():
            tasks.append(self.run_check(check_name))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions in gather
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                check_name = list(self.check_registry.keys())[i]
                final_results.append(HealthCheckResult(
                    name=check_name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Check execution failed: {str(result)}",
                    duration_ms=0.0,
                    timestamp=datetime.utcnow()
                ))
            else:
                final_results.append(result)
        
        return final_results
    
    async def get_service_health(self) -> ServiceHealth:
        """Get overall service health status.
        
        Returns:
            Service health summary
        """
        # Run all health checks
        check_results = await self.run_all_checks()
        
        # Determine overall status
        overall_status = self._determine_overall_status(check_results)
        
        # Calculate uptime
        uptime_seconds = time.time() - self.start_time
        
        # Create summary
        summary = {
            "total_checks": len(check_results),
            "healthy_checks": sum(1 for r in check_results if r.status == HealthStatus.HEALTHY),
            "degraded_checks": sum(1 for r in check_results if r.status == HealthStatus.DEGRADED),
            "unhealthy_checks": sum(1 for r in check_results if r.status == HealthStatus.UNHEALTHY),
            "average_response_time_ms": sum(r.duration_ms for r in check_results) / len(check_results) if check_results else 0,
            "uptime_hours": uptime_seconds / 3600
        }
        
        return ServiceHealth(
            status=overall_status,
            timestamp=datetime.utcnow(),
            version=getattr(settings, 'app_version', '1.0.0'),
            uptime_seconds=uptime_seconds,
            checks=check_results,
            summary=summary
        )
    
    def _determine_overall_status(self, check_results: List[HealthCheckResult]) -> HealthStatus:
        """Determine overall service status from check results.
        
        Args:
            check_results: List of health check results
            
        Returns:
            Overall health status
        """
        if not check_results:
            return HealthStatus.UNKNOWN
        
        # Count status types
        unhealthy_count = sum(1 for r in check_results if r.status == HealthStatus.UNHEALTHY)
        degraded_count = sum(1 for r in check_results if r.status == HealthStatus.DEGRADED)
        
        # Determine overall status
        if unhealthy_count > 0:
            return HealthStatus.UNHEALTHY
        elif degraded_count > 0:
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.HEALTHY
    
    # Default Health Checks
    async def _check_system_resources(self) -> HealthCheckResult:
        """Check system resource usage."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Determine status based on thresholds
            if cpu_percent > 90 or memory.percent > 90:
                status = HealthStatus.UNHEALTHY
                message = f"High resource usage: CPU {cpu_percent}%, Memory {memory.percent}%"
            elif cpu_percent > 70 or memory.percent > 70:
                status = HealthStatus.DEGRADED
                message = f"Moderate resource usage: CPU {cpu_percent}%, Memory {memory.percent}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Normal resource usage: CPU {cpu_percent}%, Memory {memory.percent}%"
            
            return HealthCheckResult(
                name="system_resources",
                status=status,
                message=message,
                duration_ms=0.0,
                timestamp=datetime.utcnow(),
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "memory_total_gb": memory.total / (1024**3)
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                name="system_resources",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check system resources: {str(e)}",
                duration_ms=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _check_gemini_api(self) -> HealthCheckResult:
        """Check Gemini API connectivity."""
        try:
            # Simple API connectivity test
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            # Test with a simple prompt
            response = await model.generate_content_async("Hello")
            
            if response and response.text:
                return HealthCheckResult(
                    name="gemini_api",
                    status=HealthStatus.HEALTHY,
                    message="Gemini API is accessible and responding",
                    duration_ms=0.0,
                    timestamp=datetime.utcnow(),
                    details={"api_response_length": len(response.text)}
                )
            else:
                return HealthCheckResult(
                    name="gemini_api",
                    status=HealthStatus.DEGRADED,
                    message="Gemini API responded but with empty content",
                    duration_ms=0.0,
                    timestamp=datetime.utcnow()
                )
                
        except Exception as e:
            return HealthCheckResult(
                name="gemini_api",
                status=HealthStatus.UNHEALTHY,
                message=f"Gemini API check failed: {str(e)}",
                duration_ms=0.0,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def _check_memory_usage(self) -> HealthCheckResult:
        """Check application memory usage."""
        try:
            import gc
            import sys
            
            # Force garbage collection
            gc.collect()
            
            # Get memory info
            memory_info = psutil.Process().memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)
            
            # Check thresholds
            if memory_mb > 1000:  # 1GB
                status = HealthStatus.UNHEALTHY
                message = f"High memory usage: {memory_mb:.1f}MB"
            elif memory_mb > 500:  # 500MB
                status = HealthStatus.DEGRADED
                message = f"Moderate memory usage: {memory_mb:.1f}MB"
            else:
                status = HealthStatus.HEALTHY
                message = f"Normal memory usage: {memory_mb:.1f}MB"
            
            return HealthCheckResult(
                name="memory_usage",
                status=status,
                message=message,
                duration_ms=0.0,
                timestamp=datetime.utcnow(),
                details={
                    "memory_mb": memory_mb,
                    "memory_vms_mb": memory_info.vms / (1024 * 1024),
                    "gc_objects": len(gc.get_objects())
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                name="memory_usage",
                status=HealthStatus.UNHEALTHY,
                message=f"Memory check failed: {str(e)}",
                duration_ms=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _check_disk_space(self) -> HealthCheckResult:
        """Check disk space availability."""
        try:
            disk_usage = psutil.disk_usage('/')
            free_percent = (disk_usage.free / disk_usage.total) * 100
            
            if free_percent < 10:
                status = HealthStatus.UNHEALTHY
                message = f"Low disk space: {free_percent:.1f}% free"
            elif free_percent < 20:
                status = HealthStatus.DEGRADED
                message = f"Moderate disk space: {free_percent:.1f}% free"
            else:
                status = HealthStatus.HEALTHY
                message = f"Sufficient disk space: {free_percent:.1f}% free"
            
            return HealthCheckResult(
                name="disk_space",
                status=status,
                message=message,
                duration_ms=0.0,
                timestamp=datetime.utcnow(),
                details={
                    "free_percent": free_percent,
                    "free_gb": disk_usage.free / (1024**3),
                    "total_gb": disk_usage.total / (1024**3)
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                name="disk_space",
                status=HealthStatus.UNHEALTHY,
                message=f"Disk space check failed: {str(e)}",
                duration_ms=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _check_service_dependencies(self) -> HealthCheckResult:
        """Check service dependencies."""
        try:
            # Check if all core modules are importable
            dependencies = [
                "ai_service.core.ai_client",
                "ai_service.services.ai_service",
                "ai_service.core.json_mode",
                "ai_service.core.context_cache",
                "ai_service.core.enhanced_function_executor"
            ]
            
            failed_imports = []
            for dep in dependencies:
                try:
                    __import__(dep)
                except ImportError as e:
                    failed_imports.append(f"{dep}: {str(e)}")
            
            if failed_imports:
                return HealthCheckResult(
                    name="service_dependencies",
                    status=HealthStatus.UNHEALTHY,
                    message=f"Failed to import dependencies: {', '.join(failed_imports)}",
                    duration_ms=0.0,
                    timestamp=datetime.utcnow(),
                    details={"failed_imports": failed_imports}
                )
            else:
                return HealthCheckResult(
                    name="service_dependencies",
                    status=HealthStatus.HEALTHY,
                    message="All service dependencies are available",
                    duration_ms=0.0,
                    timestamp=datetime.utcnow(),
                    details={"checked_dependencies": len(dependencies)}
                )
                
        except Exception as e:
            return HealthCheckResult(
                name="service_dependencies",
                status=HealthStatus.UNHEALTHY,
                message=f"Dependency check failed: {str(e)}",
                duration_ms=0.0,
                timestamp=datetime.utcnow()
            )


# Global health checker instance
health_checker = HealthChecker()
