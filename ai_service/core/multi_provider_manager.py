"""
Multi-Provider AI Manager for Phase 6.
Supports multiple AI providers with intelligent routing and fallback strategies.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import json

logger = logging.getLogger(__name__)


class AIProvider(Enum):
    """Supported AI providers."""
    GOOGLE_GEMINI = "google_gemini"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    COHERE = "cohere"
    AZURE_OPENAI = "azure_openai"
    HUGGINGFACE = "huggingface"


class ModelCapability(Enum):
    """AI model capabilities."""
    TEXT_GENERATION = "text_generation"
    CHAT_COMPLETION = "chat_completion"
    IMAGE_GENERATION = "image_generation"
    IMAGE_ANALYSIS = "image_analysis"
    EMBEDDINGS = "embeddings"
    FUNCTION_CALLING = "function_calling"
    CODE_GENERATION = "code_generation"
    MULTIMODAL = "multimodal"
    AUDIO_GENERATION = "audio_generation"
    AUDIO_TRANSCRIPTION = "audio_transcription"


class RoutingStrategy(Enum):
    """Model routing strategies."""
    ROUND_ROBIN = "round_robin"
    PERFORMANCE_BASED = "performance_based"
    COST_OPTIMIZED = "cost_optimized"
    CAPABILITY_BASED = "capability_based"
    LOAD_BALANCED = "load_balanced"
    QUALITY_OPTIMIZED = "quality_optimized"


@dataclass
class ModelInfo:
    """Information about an AI model."""
    provider: AIProvider
    model_id: str
    name: str
    capabilities: List[ModelCapability]
    max_tokens: int
    cost_per_1k_tokens: float
    quality_score: float = 0.8
    speed_score: float = 0.8
    reliability_score: float = 0.9
    is_available: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ProviderConfig:
    """Configuration for an AI provider."""
    provider: AIProvider
    api_key: str
    base_url: Optional[str] = None
    organization: Optional[str] = None
    project_id: Optional[str] = None
    region: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    rate_limit_per_minute: int = 1000
    is_enabled: bool = True
    priority: int = 1  # Higher number = higher priority
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RequestMetrics:
    """Metrics for AI requests."""
    provider: AIProvider
    model_id: str
    capability: ModelCapability
    request_time: float
    response_time: float
    tokens_used: int
    cost: float
    success: bool
    error_message: Optional[str] = None
    quality_rating: Optional[float] = None


class BaseAIProvider(ABC):
    """Abstract base class for AI providers."""
    
    def __init__(self, config: ProviderConfig):
        self.config = config
        self.provider = config.provider
        self.models: Dict[str, ModelInfo] = {}
        self.request_history: List[RequestMetrics] = []
        
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the provider."""
        pass
    
    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text using the provider."""
        pass
    
    @abstractmethod
    async def generate_chat(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate chat completion using the provider."""
        pass
    
    @abstractmethod
    async def generate_embeddings(
        self,
        texts: List[str],
        model_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate embeddings using the provider."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check provider health."""
        pass
    
    def get_available_models(self) -> List[ModelInfo]:
        """Get list of available models."""
        return [model for model in self.models.values() if model.is_available]
    
    def get_models_by_capability(self, capability: ModelCapability) -> List[ModelInfo]:
        """Get models that support specific capability."""
        return [
            model for model in self.models.values()
            if capability in model.capabilities and model.is_available
        ]
    
    def record_request_metrics(self, metrics: RequestMetrics):
        """Record request metrics for analytics."""
        self.request_history.append(metrics)
        
        # Keep only last 1000 requests
        if len(self.request_history) > 1000:
            self.request_history = self.request_history[-1000:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get provider performance statistics."""
        if not self.request_history:
            return {
                "total_requests": 0,
                "success_rate": 0.0,
                "avg_response_time": 0.0,
                "avg_cost": 0.0
            }
        
        total_requests = len(self.request_history)
        successful_requests = sum(1 for r in self.request_history if r.success)
        success_rate = successful_requests / total_requests
        
        successful_metrics = [r for r in self.request_history if r.success]
        avg_response_time = sum(r.response_time for r in successful_metrics) / len(successful_metrics) if successful_metrics else 0
        avg_cost = sum(r.cost for r in successful_metrics) / len(successful_metrics) if successful_metrics else 0
        
        return {
            "total_requests": total_requests,
            "success_rate": success_rate,
            "avg_response_time": avg_response_time,
            "avg_cost": avg_cost,
            "last_24h_requests": len([
                r for r in self.request_history 
                if time.time() - r.request_time < 86400
            ])
        }


class MultiProviderManager:
    """Manager for multiple AI providers with intelligent routing."""
    
    def __init__(self):
        self.providers: Dict[AIProvider, BaseAIProvider] = {}
        self.routing_strategy = RoutingStrategy.PERFORMANCE_BASED
        self.fallback_enabled = True
        self.analytics_enabled = True
        
        # Routing state
        self.round_robin_index = 0
        self.provider_weights: Dict[AIProvider, float] = {}
        
        # Analytics
        self.global_metrics: List[RequestMetrics] = []
        self.provider_rankings: Dict[AIProvider, float] = {}
        
        logger.info("Multi-Provider Manager initialized")
    
    async def add_provider(self, provider: BaseAIProvider) -> bool:
        """Add an AI provider to the manager."""
        try:
            # Initialize provider
            success = await provider.initialize()
            if not success:
                logger.error(f"Failed to initialize provider: {provider.provider.value}")
                return False
            
            # Add to providers
            self.providers[provider.provider] = provider
            self.provider_weights[provider.provider] = 1.0
            
            logger.info(f"Added provider: {provider.provider.value}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding provider {provider.provider.value}: {e}")
            return False
    
    async def remove_provider(self, provider_type: AIProvider) -> bool:
        """Remove an AI provider from the manager."""
        if provider_type in self.providers:
            del self.providers[provider_type]
            if provider_type in self.provider_weights:
                del self.provider_weights[provider_type]
            logger.info(f"Removed provider: {provider_type.value}")
            return True
        return False
    
    def set_routing_strategy(self, strategy: RoutingStrategy):
        """Set the routing strategy for model selection."""
        self.routing_strategy = strategy
        logger.info(f"Routing strategy set to: {strategy.value}")
    
    async def select_provider_and_model(
        self,
        capability: ModelCapability,
        requirements: Optional[Dict[str, Any]] = None
    ) -> Tuple[Optional[BaseAIProvider], Optional[str]]:
        """Select the best provider and model for a request."""
        requirements = requirements or {}
        
        # Get available providers for capability
        available_options = []
        
        for provider in self.providers.values():
            if not provider.config.is_enabled:
                continue
                
            models = provider.get_models_by_capability(capability)
            for model in models:
                available_options.append((provider, model))
        
        if not available_options:
            logger.warning(f"No available providers for capability: {capability.value}")
            return None, None
        
        # Apply routing strategy
        if self.routing_strategy == RoutingStrategy.ROUND_ROBIN:
            return self._round_robin_selection(available_options)
        elif self.routing_strategy == RoutingStrategy.PERFORMANCE_BASED:
            return self._performance_based_selection(available_options)
        elif self.routing_strategy == RoutingStrategy.COST_OPTIMIZED:
            return self._cost_optimized_selection(available_options)
        elif self.routing_strategy == RoutingStrategy.QUALITY_OPTIMIZED:
            return self._quality_optimized_selection(available_options)
        else:
            # Default to first available
            provider, model = available_options[0]
            return provider, model.model_id
    
    def _round_robin_selection(self, options: List[Tuple[BaseAIProvider, ModelInfo]]) -> Tuple[BaseAIProvider, str]:
        """Round-robin provider selection."""
        if not options:
            return None, None
        
        selected = options[self.round_robin_index % len(options)]
        self.round_robin_index += 1
        
        provider, model = selected
        return provider, model.model_id
    
    def _performance_based_selection(self, options: List[Tuple[BaseAIProvider, ModelInfo]]) -> Tuple[BaseAIProvider, str]:
        """Performance-based provider selection."""
        best_score = -1
        best_option = None
        
        for provider, model in options:
            stats = provider.get_performance_stats()
            
            # Calculate composite score
            success_weight = 0.4
            speed_weight = 0.3
            reliability_weight = 0.3
            
            score = (
                stats["success_rate"] * success_weight +
                (1.0 / (stats["avg_response_time"] + 0.1)) * speed_weight +
                model.reliability_score * reliability_weight
            )
            
            if score > best_score:
                best_score = score
                best_option = (provider, model.model_id)
        
        return best_option if best_option else (None, None)
    
    def _cost_optimized_selection(self, options: List[Tuple[BaseAIProvider, ModelInfo]]) -> Tuple[BaseAIProvider, str]:
        """Cost-optimized provider selection."""
        best_cost = float('inf')
        best_option = None
        
        for provider, model in options:
            if model.cost_per_1k_tokens < best_cost:
                best_cost = model.cost_per_1k_tokens
                best_option = (provider, model.model_id)
        
        return best_option if best_option else (None, None)
    
    def _quality_optimized_selection(self, options: List[Tuple[BaseAIProvider, ModelInfo]]) -> Tuple[BaseAIProvider, str]:
        """Quality-optimized provider selection."""
        best_quality = -1
        best_option = None
        
        for provider, model in options:
            if model.quality_score > best_quality:
                best_quality = model.quality_score
                best_option = (provider, model.model_id)
        
        return best_option if best_option else (None, None)
    
    async def execute_with_fallback(
        self,
        capability: ModelCapability,
        operation_func,
        requirements: Optional[Dict[str, Any]] = None,
        max_fallbacks: int = 3
    ) -> Dict[str, Any]:
        """Execute operation with automatic fallback."""
        attempts = 0
        last_error = None
        
        while attempts < max_fallbacks:
            try:
                # Select provider and model
                provider, model_id = await self.select_provider_and_model(capability, requirements)
                
                if not provider or not model_id:
                    raise Exception("No available providers for capability")
                
                # Execute operation
                start_time = time.time()
                result = await operation_func(provider, model_id)
                response_time = time.time() - start_time
                
                # Record success metrics
                if self.analytics_enabled:
                    metrics = RequestMetrics(
                        provider=provider.provider,
                        model_id=model_id,
                        capability=capability,
                        request_time=start_time,
                        response_time=response_time,
                        tokens_used=result.get("usage", {}).get("total_tokens", 0),
                        cost=self._calculate_cost(provider.provider, model_id, result),
                        success=True
                    )
                    provider.record_request_metrics(metrics)
                    self.global_metrics.append(metrics)
                
                return result
                
            except Exception as e:
                attempts += 1
                last_error = e
                
                # Record failure metrics
                if self.analytics_enabled and 'provider' in locals():
                    metrics = RequestMetrics(
                        provider=provider.provider,
                        model_id=model_id if 'model_id' in locals() else "unknown",
                        capability=capability,
                        request_time=time.time(),
                        response_time=0,
                        tokens_used=0,
                        cost=0,
                        success=False,
                        error_message=str(e)
                    )
                    if 'provider' in locals():
                        provider.record_request_metrics(metrics)
                    self.global_metrics.append(metrics)
                
                logger.warning(f"Attempt {attempts} failed: {e}")
                
                if attempts < max_fallbacks:
                    await asyncio.sleep(0.5 * attempts)  # Exponential backoff
        
        raise Exception(f"All fallback attempts failed. Last error: {last_error}")
    
    def _calculate_cost(self, provider: AIProvider, model_id: str, result: Dict[str, Any]) -> float:
        """Calculate cost for a request."""
        # This would implement actual cost calculation based on provider pricing
        tokens = result.get("usage", {}).get("total_tokens", 0)
        # Simplified cost calculation
        return tokens * 0.001  # $0.001 per 1K tokens as example
    
    def get_global_analytics(self) -> Dict[str, Any]:
        """Get global analytics across all providers."""
        if not self.global_metrics:
            return {"total_requests": 0}
        
        total_requests = len(self.global_metrics)
        successful_requests = sum(1 for m in self.global_metrics if m.success)
        
        # Provider breakdown
        provider_stats = {}
        for provider in AIProvider:
            provider_metrics = [m for m in self.global_metrics if m.provider == provider]
            if provider_metrics:
                provider_stats[provider.value] = {
                    "requests": len(provider_metrics),
                    "success_rate": sum(1 for m in provider_metrics if m.success) / len(provider_metrics),
                    "avg_response_time": sum(m.response_time for m in provider_metrics if m.success) / max(1, sum(1 for m in provider_metrics if m.success))
                }
        
        return {
            "total_requests": total_requests,
            "global_success_rate": successful_requests / total_requests,
            "provider_breakdown": provider_stats,
            "capabilities_used": list(set(m.capability.value for m in self.global_metrics)),
            "total_cost": sum(m.cost for m in self.global_metrics),
            "avg_response_time": sum(m.response_time for m in self.global_metrics if m.success) / max(1, successful_requests)
        }


# Global multi-provider manager instance
multi_provider_manager = MultiProviderManager()
