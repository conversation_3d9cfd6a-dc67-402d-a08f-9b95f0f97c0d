"""
Advanced caching system for performance optimization.
"""
import asyncio
import hashlib
import json
import logging
import time
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import OrderedDict
import threading
from contextlib import asynccontextmanager

from ai_service.config.settings import settings

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.utcnow)
    size_bytes: int = 0
    tags: List[str] = field(default_factory=list)


@dataclass
class CacheStats:
    """Cache statistics."""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    total_entries: int = 0
    total_size_bytes: int = 0
    hit_rate: float = 0.0


class LRUCache:
    """Thread-safe LRU cache implementation."""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.lock = threading.RLock()
        self.stats = CacheStats()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self.lock:
            if key not in self.cache:
                self.stats.misses += 1
                self._update_hit_rate()
                return None
            
            entry = self.cache[key]
            
            # Check expiration
            if entry.expires_at and datetime.utcnow() > entry.expires_at:
                del self.cache[key]
                self.stats.misses += 1
                self.stats.total_entries -= 1
                self.stats.total_size_bytes -= entry.size_bytes
                self._update_hit_rate()
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            entry.access_count += 1
            entry.last_accessed = datetime.utcnow()
            
            self.stats.hits += 1
            self._update_hit_rate()
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None) -> bool:
        """Set value in cache."""
        with self.lock:
            # Calculate size
            size_bytes = self._calculate_size(value)
            
            # Create cache entry
            expires_at = None
            if ttl:
                expires_at = datetime.utcnow() + timedelta(seconds=ttl)
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                expires_at=expires_at,
                size_bytes=size_bytes,
                tags=tags or []
            )
            
            # Remove existing entry if present
            if key in self.cache:
                old_entry = self.cache[key]
                self.stats.total_size_bytes -= old_entry.size_bytes
                del self.cache[key]
                self.stats.total_entries -= 1
            
            # Evict if necessary
            while len(self.cache) >= self.max_size:
                self._evict_lru()
            
            # Add new entry
            self.cache[key] = entry
            self.stats.total_entries += 1
            self.stats.total_size_bytes += size_bytes
            
            return True
    
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                del self.cache[key]
                self.stats.total_entries -= 1
                self.stats.total_size_bytes -= entry.size_bytes
                return True
            return False
    
    def clear(self):
        """Clear all cache entries."""
        with self.lock:
            self.cache.clear()
            self.stats.total_entries = 0
            self.stats.total_size_bytes = 0
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        with self.lock:
            return CacheStats(
                hits=self.stats.hits,
                misses=self.stats.misses,
                evictions=self.stats.evictions,
                total_entries=self.stats.total_entries,
                total_size_bytes=self.stats.total_size_bytes,
                hit_rate=self.stats.hit_rate
            )
    
    def _evict_lru(self):
        """Evict least recently used entry."""
        if self.cache:
            key, entry = self.cache.popitem(last=False)
            self.stats.evictions += 1
            self.stats.total_entries -= 1
            self.stats.total_size_bytes -= entry.size_bytes
    
    def _update_hit_rate(self):
        """Update hit rate statistics."""
        total_requests = self.stats.hits + self.stats.misses
        if total_requests > 0:
            self.stats.hit_rate = self.stats.hits / total_requests
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value in bytes."""
        try:
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (dict, list)):
                return len(json.dumps(value, default=str).encode('utf-8'))
            else:
                return len(str(value).encode('utf-8'))
        except:
            return 100  # Default size estimate


class ResponseCacheManager:
    """Advanced response caching manager."""
    
    def __init__(self, max_size: int = None, default_ttl: int = None):
        self.max_size = max_size or settings.max_cache_size
        self.default_ttl = default_ttl or settings.cache_ttl
        
        # Multiple cache layers
        self.response_cache = LRUCache(max_size=self.max_size)
        self.embedding_cache = LRUCache(max_size=self.max_size // 2)
        self.model_cache = LRUCache(max_size=self.max_size // 4)
        
        # Cache warming
        self.warming_enabled = True
        self.warming_tasks: Dict[str, asyncio.Task] = {}
        
        logger.info(f"Response cache manager initialized with max_size={self.max_size}")
    
    def generate_cache_key(self, prefix: str, **kwargs) -> str:
        """Generate cache key from parameters."""
        # Sort kwargs for consistent key generation
        sorted_params = sorted(kwargs.items())
        params_str = json.dumps(sorted_params, sort_keys=True, default=str)
        
        # Create hash
        hash_obj = hashlib.sha256(params_str.encode('utf-8'))
        hash_str = hash_obj.hexdigest()[:16]  # Use first 16 chars
        
        return f"{prefix}:{hash_str}"
    
    async def get_cached_response(
        self, 
        cache_key: str, 
        cache_type: str = "response"
    ) -> Optional[Any]:
        """Get cached response."""
        cache = self._get_cache_by_type(cache_type)
        
        try:
            result = cache.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return result
            else:
                logger.debug(f"Cache miss for key: {cache_key}")
                return None
        except Exception as e:
            logger.error(f"Error getting cached response: {e}")
            return None
    
    async def cache_response(
        self, 
        cache_key: str, 
        response: Any, 
        ttl: Optional[int] = None,
        cache_type: str = "response",
        tags: List[str] = None
    ) -> bool:
        """Cache response with TTL."""
        cache = self._get_cache_by_type(cache_type)
        ttl = ttl or self.default_ttl
        
        try:
            success = cache.set(cache_key, response, ttl=ttl, tags=tags)
            if success:
                logger.debug(f"Cached response for key: {cache_key} (TTL: {ttl}s)")
            return success
        except Exception as e:
            logger.error(f"Error caching response: {e}")
            return False
    
    async def invalidate_cache(self, pattern: str = None, tags: List[str] = None):
        """Invalidate cache entries by pattern or tags."""
        try:
            if pattern:
                # Invalidate by pattern
                for cache in [self.response_cache, self.embedding_cache, self.model_cache]:
                    keys_to_delete = []
                    with cache.lock:
                        for key in cache.cache.keys():
                            if pattern in key:
                                keys_to_delete.append(key)
                    
                    for key in keys_to_delete:
                        cache.delete(key)
                        logger.debug(f"Invalidated cache key: {key}")
            
            if tags:
                # Invalidate by tags
                for cache in [self.response_cache, self.embedding_cache, self.model_cache]:
                    keys_to_delete = []
                    with cache.lock:
                        for key, entry in cache.cache.items():
                            if any(tag in entry.tags for tag in tags):
                                keys_to_delete.append(key)
                    
                    for key in keys_to_delete:
                        cache.delete(key)
                        logger.debug(f"Invalidated cache key by tag: {key}")
                        
        except Exception as e:
            logger.error(f"Error invalidating cache: {e}")
    
    async def warm_cache(self, warm_func: Callable, cache_key: str, **kwargs):
        """Warm cache with background task."""
        if not self.warming_enabled:
            return
        
        try:
            # Cancel existing warming task for this key
            if cache_key in self.warming_tasks:
                self.warming_tasks[cache_key].cancel()
            
            # Create new warming task
            async def warm_task():
                try:
                    result = await warm_func(**kwargs)
                    await self.cache_response(cache_key, result)
                    logger.debug(f"Cache warmed for key: {cache_key}")
                except Exception as e:
                    logger.error(f"Cache warming failed for key {cache_key}: {e}")
                finally:
                    self.warming_tasks.pop(cache_key, None)
            
            task = asyncio.create_task(warm_task())
            self.warming_tasks[cache_key] = task
            
        except Exception as e:
            logger.error(f"Error starting cache warming: {e}")
    
    def get_cache_stats(self) -> Dict[str, CacheStats]:
        """Get statistics for all caches."""
        return {
            "response_cache": self.response_cache.get_stats(),
            "embedding_cache": self.embedding_cache.get_stats(),
            "model_cache": self.model_cache.get_stats()
        }
    
    def clear_all_caches(self):
        """Clear all caches."""
        self.response_cache.clear()
        self.embedding_cache.clear()
        self.model_cache.clear()
        logger.info("All caches cleared")
    
    def _get_cache_by_type(self, cache_type: str) -> LRUCache:
        """Get cache instance by type."""
        if cache_type == "embedding":
            return self.embedding_cache
        elif cache_type == "model":
            return self.model_cache
        else:
            return self.response_cache
    
    @asynccontextmanager
    async def cached_operation(
        self, 
        cache_key: str, 
        operation: Callable,
        ttl: Optional[int] = None,
        cache_type: str = "response",
        **operation_kwargs
    ):
        """Context manager for cached operations."""
        # Try to get from cache first
        cached_result = await self.get_cached_response(cache_key, cache_type)
        if cached_result is not None:
            yield cached_result
            return
        
        # Execute operation and cache result
        try:
            result = await operation(**operation_kwargs)
            await self.cache_response(cache_key, result, ttl, cache_type)
            yield result
        except Exception as e:
            logger.error(f"Cached operation failed: {e}")
            raise


# Global cache manager instance
cache_manager = ResponseCacheManager()
