"""
Optimized HTTP client with connection pooling and performance features.
"""
import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
import aiohttp
import json
from dataclasses import dataclass
from contextlib import asynccontextmanager

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


@dataclass
class ConnectionPoolStats:
    """Connection pool statistics."""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    requests_sent: int = 0
    requests_failed: int = 0
    average_response_time: float = 0.0
    pool_created_at: datetime = None


@dataclass
class RequestConfig:
    """HTTP request configuration."""
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff: float = 2.0
    headers: Dict[str, str] = None
    follow_redirects: bool = True


class OptimizedHTTPClient:
    """High-performance HTTP client with connection pooling."""
    
    def __init__(
        self,
        max_connections: int = 100,
        max_connections_per_host: int = 30,
        connection_timeout: int = 30,
        read_timeout: int = 60,
        keepalive_timeout: int = 30
    ):
        self.max_connections = max_connections
        self.max_connections_per_host = max_connections_per_host
        self.connection_timeout = connection_timeout
        self.read_timeout = read_timeout
        self.keepalive_timeout = keepalive_timeout
        
        # Connection pool
        self._session: Optional[aiohttp.ClientSession] = None
        self._connector: Optional[aiohttp.TCPConnector] = None
        
        # Statistics
        self.stats = ConnectionPoolStats(pool_created_at=datetime.utcnow())
        self._request_times: List[float] = []
        
        # Circuit breaker
        self.circuit_breaker_enabled = True
        self.failure_threshold = 5
        self.recovery_timeout = 60
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.circuit_open = False
        
        logger.info(f"HTTP client initialized with max_connections={max_connections}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _ensure_session(self):
        """Ensure HTTP session is created."""
        if self._session is None or self._session.closed:
            # Create TCP connector with optimized settings
            self._connector = aiohttp.TCPConnector(
                limit=self.max_connections,
                limit_per_host=self.max_connections_per_host,
                ttl_dns_cache=300,  # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=self.keepalive_timeout,
                enable_cleanup_closed=True,
                force_close=False,
                ssl=False  # Disable SSL verification for internal services
            )
            
            # Create timeout configuration
            timeout = aiohttp.ClientTimeout(
                total=self.read_timeout,
                connect=self.connection_timeout,
                sock_read=self.read_timeout
            )
            
            # Create session
            self._session = aiohttp.ClientSession(
                connector=self._connector,
                timeout=timeout,
                headers={
                    'User-Agent': f'AI-Service-Client/{settings.app_version}',
                    'Accept': 'application/json',
                    'Accept-Encoding': 'gzip, deflate'
                },
                json_serialize=json.dumps,
                raise_for_status=False  # Handle status codes manually
            )
            
            logger.info("HTTP session created with optimized settings")
    
    async def request(
        self,
        method: str,
        url: str,
        config: Optional[RequestConfig] = None,
        **kwargs
    ) -> aiohttp.ClientResponse:
        """Make HTTP request with retry logic and circuit breaker."""
        config = config or RequestConfig()
        
        # Check circuit breaker
        if self._is_circuit_open():
            raise aiohttp.ClientError("Circuit breaker is open")
        
        await self._ensure_session()
        
        # Prepare request parameters
        request_kwargs = {
            'timeout': aiohttp.ClientTimeout(total=config.timeout),
            'allow_redirects': config.follow_redirects,
            **kwargs
        }
        
        if config.headers:
            request_kwargs['headers'] = {
                **self._session.headers,
                **config.headers
            }
        
        # Retry logic
        last_exception = None
        for attempt in range(config.max_retries + 1):
            start_time = time.time()
            
            try:
                # Make request
                response = await self._session.request(method, url, **request_kwargs)
                
                # Record timing
                duration = time.time() - start_time
                self._record_request_time(duration)
                
                # Update statistics
                self.stats.requests_sent += 1
                
                # Check for success
                if response.status < 400:
                    self._record_success()
                    
                    # Record metrics
                    metrics_collector.record_histogram(
                        "http_request_duration_ms",
                        duration * 1000,
                        {
                            "method": method,
                            "status_code": str(response.status),
                            "host": response.url.host
                        }
                    )
                    
                    return response
                else:
                    # Handle HTTP errors
                    self._record_failure()
                    if attempt == config.max_retries:
                        return response  # Return last response for caller to handle
                
            except Exception as e:
                last_exception = e
                duration = time.time() - start_time
                self._record_request_time(duration)
                self.stats.requests_failed += 1
                self._record_failure()
                
                logger.warning(f"HTTP request failed (attempt {attempt + 1}): {e}")
                
                # Don't retry on certain exceptions
                if isinstance(e, (aiohttp.ClientConnectorError, asyncio.TimeoutError)):
                    if attempt == config.max_retries:
                        break
                else:
                    break
            
            # Wait before retry
            if attempt < config.max_retries:
                delay = config.retry_delay * (config.retry_backoff ** attempt)
                await asyncio.sleep(delay)
        
        # All retries failed
        if last_exception:
            raise last_exception
        else:
            raise aiohttp.ClientError("All retry attempts failed")
    
    async def get(self, url: str, config: Optional[RequestConfig] = None, **kwargs):
        """Make GET request."""
        return await self.request('GET', url, config, **kwargs)
    
    async def post(self, url: str, config: Optional[RequestConfig] = None, **kwargs):
        """Make POST request."""
        return await self.request('POST', url, config, **kwargs)
    
    async def put(self, url: str, config: Optional[RequestConfig] = None, **kwargs):
        """Make PUT request."""
        return await self.request('PUT', url, config, **kwargs)
    
    async def delete(self, url: str, config: Optional[RequestConfig] = None, **kwargs):
        """Make DELETE request."""
        return await self.request('DELETE', url, config, **kwargs)
    
    @asynccontextmanager
    async def batch_requests(self, max_concurrent: int = 10):
        """Context manager for batch requests with concurrency control."""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_request(method: str, url: str, **kwargs):
            async with semaphore:
                return await self.request(method, url, **kwargs)
        
        yield limited_request
    
    async def get_connection_stats(self) -> ConnectionPoolStats:
        """Get connection pool statistics."""
        if self._connector:
            # Update connection stats from connector
            self.stats.total_connections = len(self._connector._conns)
            self.stats.active_connections = sum(
                len(conns) for conns in self._connector._conns.values()
            )
        
        # Calculate average response time
        if self._request_times:
            self.stats.average_response_time = sum(self._request_times) / len(self._request_times)
        
        return self.stats
    
    def _record_request_time(self, duration: float):
        """Record request timing."""
        self._request_times.append(duration)
        
        # Keep only recent times (last 1000)
        if len(self._request_times) > 1000:
            self._request_times = self._request_times[-1000:]
    
    def _record_success(self):
        """Record successful request."""
        if self.circuit_breaker_enabled:
            self.failure_count = 0
            self.circuit_open = False
    
    def _record_failure(self):
        """Record failed request."""
        if self.circuit_breaker_enabled:
            self.failure_count += 1
            self.last_failure_time = datetime.utcnow()
            
            if self.failure_count >= self.failure_threshold:
                self.circuit_open = True
                logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
    
    def _is_circuit_open(self) -> bool:
        """Check if circuit breaker is open."""
        if not self.circuit_breaker_enabled or not self.circuit_open:
            return False
        
        # Check if recovery timeout has passed
        if self.last_failure_time:
            time_since_failure = datetime.utcnow() - self.last_failure_time
            if time_since_failure.total_seconds() > self.recovery_timeout:
                self.circuit_open = False
                self.failure_count = 0
                logger.info("Circuit breaker closed after recovery timeout")
                return False
        
        return True
    
    async def close(self):
        """Close HTTP session and connections."""
        if self._session and not self._session.closed:
            await self._session.close()
            logger.info("HTTP session closed")
        
        if self._connector:
            await self._connector.close()
            logger.info("HTTP connector closed")


class HTTPClientPool:
    """Pool of HTTP clients for different services."""
    
    def __init__(self):
        self.clients: Dict[str, OptimizedHTTPClient] = {}
        self.default_client: Optional[OptimizedHTTPClient] = None
    
    async def get_client(self, service_name: str = "default") -> OptimizedHTTPClient:
        """Get HTTP client for service."""
        if service_name == "default":
            if self.default_client is None:
                self.default_client = OptimizedHTTPClient()
                await self.default_client._ensure_session()
            return self.default_client
        
        if service_name not in self.clients:
            self.clients[service_name] = OptimizedHTTPClient()
            await self.clients[service_name]._ensure_session()
        
        return self.clients[service_name]
    
    async def close_all(self):
        """Close all HTTP clients."""
        if self.default_client:
            await self.default_client.close()
        
        for client in self.clients.values():
            await client.close()
        
        self.clients.clear()
        logger.info("All HTTP clients closed")


# Global HTTP client pool
http_client_pool = HTTPClientPool()
