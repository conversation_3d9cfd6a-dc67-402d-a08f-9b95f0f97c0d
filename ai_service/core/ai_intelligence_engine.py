"""
AI Intelligence Engine for Phase 8.
Provides ML-based routing, predictive scaling, and intelligent optimization.
"""

import asyncio
import logging
import time
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import pickle
from collections import deque, defaultdict
import statistics

logger = logging.getLogger(__name__)


class MLModel(Enum):
    """Machine learning model types."""
    ROUTING_OPTIMIZER = "routing_optimizer"
    LOAD_PREDICTOR = "load_predictor"
    QUALITY_PREDICTOR = "quality_predictor"
    COST_OPTIMIZER = "cost_optimizer"
    ANOMALY_DETECTOR = "anomaly_detector"
    PERFORMANCE_FORECASTER = "performance_forecaster"


class PredictionType(Enum):
    """Types of predictions."""
    OPTIMAL_PROVIDER = "optimal_provider"
    EXPECTED_LATENCY = "expected_latency"
    QUALITY_SCORE = "quality_score"
    COST_ESTIMATE = "cost_estimate"
    LOAD_FORECAST = "load_forecast"
    ANOMALY_PROBABILITY = "anomaly_probability"


@dataclass
class FeatureVector:
    """Feature vector for ML models."""
    # Request features
    capability: str
    prompt_length: int
    complexity_score: float
    priority: str
    
    # Context features
    time_of_day: int
    day_of_week: int
    user_tier: str
    region: str
    
    # Historical features
    recent_latency: float
    recent_success_rate: float
    recent_cost: float
    
    # Provider features
    provider_health: Dict[str, float]
    provider_load: Dict[str, float]
    provider_performance: Dict[str, float]
    
    # System features
    global_load: float
    cache_hit_rate: float
    error_rate: float


@dataclass
class Prediction:
    """ML prediction result."""
    prediction_type: PredictionType
    value: Any
    confidence: float
    model_version: str
    features_used: List[str]
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrainingData:
    """Training data for ML models."""
    features: FeatureVector
    target: Any
    outcome: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)


class SimpleMLModel:
    """Simple ML model implementation."""
    
    def __init__(self, model_type: MLModel):
        self.model_type = model_type
        self.is_trained = False
        self.training_data: List[TrainingData] = []
        self.model_params: Dict[str, Any] = {}
        self.feature_importance: Dict[str, float] = {}
        self.last_training: Optional[float] = None
        self.version = "1.0.0"
    
    def add_training_data(self, data: TrainingData):
        """Add training data point."""
        self.training_data.append(data)
        
        # Keep only last 10000 data points
        if len(self.training_data) > 10000:
            self.training_data = self.training_data[-10000:]
    
    def train(self) -> bool:
        """Train the model."""
        if len(self.training_data) < 100:  # Minimum data required
            return False
        
        try:
            if self.model_type == MLModel.ROUTING_OPTIMIZER:
                return self._train_routing_optimizer()
            elif self.model_type == MLModel.LOAD_PREDICTOR:
                return self._train_load_predictor()
            elif self.model_type == MLModel.QUALITY_PREDICTOR:
                return self._train_quality_predictor()
            elif self.model_type == MLModel.COST_OPTIMIZER:
                return self._train_cost_optimizer()
            elif self.model_type == MLModel.ANOMALY_DETECTOR:
                return self._train_anomaly_detector()
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error training {self.model_type.value}: {e}")
            return False
    
    def _train_routing_optimizer(self) -> bool:
        """Train routing optimization model."""
        # Simple weighted scoring model
        provider_scores = defaultdict(list)
        
        for data in self.training_data[-1000:]:  # Use recent data
            if 'selected_provider' in data.outcome and 'actual_latency' in data.outcome:
                provider = data.outcome['selected_provider']
                latency = data.outcome['actual_latency']
                success = data.outcome.get('success', True)
                
                # Calculate score (lower is better)
                score = latency * (1 if success else 10)  # Penalty for failures
                provider_scores[provider].append(score)
        
        # Calculate average scores
        self.model_params['provider_scores'] = {
            provider: statistics.mean(scores)
            for provider, scores in provider_scores.items()
            if len(scores) >= 10
        }
        
        self.is_trained = True
        self.last_training = time.time()
        return True
    
    def _train_load_predictor(self) -> bool:
        """Train load prediction model."""
        # Simple time-series prediction
        load_by_hour = defaultdict(list)
        
        for data in self.training_data[-2000:]:
            hour = time.localtime(data.timestamp).tm_hour
            load = data.features.global_load
            load_by_hour[hour].append(load)
        
        # Calculate average load by hour
        self.model_params['hourly_load'] = {
            hour: statistics.mean(loads)
            for hour, loads in load_by_hour.items()
            if len(loads) >= 5
        }
        
        self.is_trained = True
        self.last_training = time.time()
        return True
    
    def _train_quality_predictor(self) -> bool:
        """Train quality prediction model."""
        # Simple feature correlation model
        quality_factors = {
            'prompt_length': [],
            'complexity_score': [],
            'provider_health': [],
            'quality_score': []
        }
        
        for data in self.training_data[-1000:]:
            if 'quality_score' in data.outcome:
                quality_factors['prompt_length'].append(data.features.prompt_length)
                quality_factors['complexity_score'].append(data.features.complexity_score)
                quality_factors['provider_health'].append(
                    statistics.mean(data.features.provider_health.values()) if data.features.provider_health else 0.5
                )
                quality_factors['quality_score'].append(data.outcome['quality_score'])
        
        # Simple correlation analysis
        if len(quality_factors['quality_score']) >= 50:
            self.model_params['quality_baseline'] = statistics.mean(quality_factors['quality_score'])
            self.model_params['length_factor'] = self._calculate_correlation(
                quality_factors['prompt_length'], quality_factors['quality_score']
            )
            self.model_params['complexity_factor'] = self._calculate_correlation(
                quality_factors['complexity_score'], quality_factors['quality_score']
            )
            
            self.is_trained = True
            self.last_training = time.time()
            return True
        
        return False
    
    def _train_cost_optimizer(self) -> bool:
        """Train cost optimization model."""
        # Simple cost-performance model
        provider_costs = defaultdict(list)
        
        for data in self.training_data[-1000:]:
            if 'selected_provider' in data.outcome and 'actual_cost' in data.outcome:
                provider = data.outcome['selected_provider']
                cost = data.outcome['actual_cost']
                quality = data.outcome.get('quality_score', 0.5)
                
                # Cost-effectiveness score
                cost_effectiveness = quality / (cost + 0.001)  # Avoid division by zero
                provider_costs[provider].append(cost_effectiveness)
        
        # Calculate average cost-effectiveness
        self.model_params['provider_cost_effectiveness'] = {
            provider: statistics.mean(scores)
            for provider, scores in provider_costs.items()
            if len(scores) >= 10
        }
        
        self.is_trained = True
        self.last_training = time.time()
        return True
    
    def _train_anomaly_detector(self) -> bool:
        """Train anomaly detection model."""
        # Simple statistical anomaly detection
        metrics = {
            'latency': [],
            'error_rate': [],
            'cost': []
        }
        
        for data in self.training_data[-2000:]:
            if 'actual_latency' in data.outcome:
                metrics['latency'].append(data.outcome['actual_latency'])
                metrics['error_rate'].append(data.features.error_rate)
                metrics['cost'].append(data.outcome.get('actual_cost', 0))
        
        # Calculate statistical thresholds
        for metric, values in metrics.items():
            if len(values) >= 100:
                mean_val = statistics.mean(values)
                std_val = statistics.stdev(values) if len(values) > 1 else 0
                
                self.model_params[f'{metric}_mean'] = mean_val
                self.model_params[f'{metric}_std'] = std_val
                self.model_params[f'{metric}_threshold'] = mean_val + (2 * std_val)  # 2 sigma
        
        self.is_trained = True
        self.last_training = time.time()
        return True
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """Calculate simple correlation coefficient."""
        if len(x) != len(y) or len(x) < 2:
            return 0.0
        
        try:
            n = len(x)
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[i] * y[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)
            sum_y2 = sum(yi * yi for yi in y)
            
            numerator = n * sum_xy - sum_x * sum_y
            denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5
            
            return numerator / denominator if denominator != 0 else 0.0
        except:
            return 0.0
    
    def predict(self, features: FeatureVector) -> Optional[Prediction]:
        """Make prediction using the model."""
        if not self.is_trained:
            return None
        
        try:
            if self.model_type == MLModel.ROUTING_OPTIMIZER:
                return self._predict_optimal_provider(features)
            elif self.model_type == MLModel.LOAD_PREDICTOR:
                return self._predict_load(features)
            elif self.model_type == MLModel.QUALITY_PREDICTOR:
                return self._predict_quality(features)
            elif self.model_type == MLModel.COST_OPTIMIZER:
                return self._predict_cost_optimal_provider(features)
            elif self.model_type == MLModel.ANOMALY_DETECTOR:
                return self._predict_anomaly(features)
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error making prediction with {self.model_type.value}: {e}")
            return None
    
    def _predict_optimal_provider(self, features: FeatureVector) -> Prediction:
        """Predict optimal provider for routing."""
        provider_scores = self.model_params.get('provider_scores', {})
        
        if not provider_scores:
            return Prediction(
                prediction_type=PredictionType.OPTIMAL_PROVIDER,
                value=None,
                confidence=0.0,
                model_version=self.version,
                features_used=['provider_scores']
            )
        
        # Adjust scores based on current health and load
        adjusted_scores = {}
        for provider, base_score in provider_scores.items():
            health = features.provider_health.get(provider, 0.5)
            load = features.provider_load.get(provider, 0.5)
            
            # Lower score is better, adjust for health and load
            adjusted_score = base_score * (2 - health) * (1 + load)
            adjusted_scores[provider] = adjusted_score
        
        # Select provider with lowest adjusted score
        best_provider = min(adjusted_scores.items(), key=lambda x: x[1])
        
        return Prediction(
            prediction_type=PredictionType.OPTIMAL_PROVIDER,
            value=best_provider[0],
            confidence=min(0.9, 1.0 / (best_provider[1] + 0.1)),
            model_version=self.version,
            features_used=['provider_scores', 'provider_health', 'provider_load']
        )
    
    def _predict_load(self, features: FeatureVector) -> Prediction:
        """Predict system load."""
        hourly_load = self.model_params.get('hourly_load', {})
        current_hour = features.time_of_day
        
        predicted_load = hourly_load.get(current_hour, features.global_load)
        
        return Prediction(
            prediction_type=PredictionType.LOAD_FORECAST,
            value=predicted_load,
            confidence=0.7,
            model_version=self.version,
            features_used=['time_of_day', 'hourly_load']
        )
    
    def _predict_quality(self, features: FeatureVector) -> Prediction:
        """Predict quality score."""
        baseline = self.model_params.get('quality_baseline', 0.8)
        length_factor = self.model_params.get('length_factor', 0.0)
        complexity_factor = self.model_params.get('complexity_factor', 0.0)
        
        # Simple linear model
        quality_adjustment = (
            length_factor * (features.prompt_length - 100) / 1000 +
            complexity_factor * (features.complexity_score - 0.5)
        )
        
        predicted_quality = max(0.0, min(1.0, baseline + quality_adjustment))
        
        return Prediction(
            prediction_type=PredictionType.QUALITY_SCORE,
            value=predicted_quality,
            confidence=0.6,
            model_version=self.version,
            features_used=['prompt_length', 'complexity_score', 'quality_baseline']
        )
    
    def _predict_cost_optimal_provider(self, features: FeatureVector) -> Prediction:
        """Predict cost-optimal provider."""
        cost_effectiveness = self.model_params.get('provider_cost_effectiveness', {})
        
        if not cost_effectiveness:
            return Prediction(
                prediction_type=PredictionType.OPTIMAL_PROVIDER,
                value=None,
                confidence=0.0,
                model_version=self.version,
                features_used=['provider_cost_effectiveness']
            )
        
        # Select provider with highest cost-effectiveness
        best_provider = max(cost_effectiveness.items(), key=lambda x: x[1])
        
        return Prediction(
            prediction_type=PredictionType.OPTIMAL_PROVIDER,
            value=best_provider[0],
            confidence=0.8,
            model_version=self.version,
            features_used=['provider_cost_effectiveness']
        )
    
    def _predict_anomaly(self, features: FeatureVector) -> Prediction:
        """Predict anomaly probability."""
        anomaly_score = 0.0
        
        # Check latency anomaly
        if 'latency_threshold' in self.model_params:
            if features.recent_latency > self.model_params['latency_threshold']:
                anomaly_score += 0.3
        
        # Check error rate anomaly
        if 'error_rate_threshold' in self.model_params:
            if features.error_rate > self.model_params['error_rate_threshold']:
                anomaly_score += 0.4
        
        # Check provider health
        avg_health = statistics.mean(features.provider_health.values()) if features.provider_health else 1.0
        if avg_health < 0.7:
            anomaly_score += 0.3
        
        return Prediction(
            prediction_type=PredictionType.ANOMALY_PROBABILITY,
            value=min(1.0, anomaly_score),
            confidence=0.7,
            model_version=self.version,
            features_used=['recent_latency', 'error_rate', 'provider_health']
        )


class AIIntelligenceEngine:
    """AI Intelligence Engine for intelligent automation."""
    
    def __init__(self):
        self.models: Dict[MLModel, SimpleMLModel] = {}
        self.feature_extractors: Dict[str, Any] = {}
        self.prediction_cache: Dict[str, Prediction] = {}
        
        # Training and prediction history
        self.training_queue: deque = deque(maxlen=1000)
        self.prediction_history: List[Prediction] = []
        
        # Configuration
        self.auto_training_enabled = True
        self.training_interval = 3600  # 1 hour
        self.min_training_data = 100
        
        # Background tasks
        self._training_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Initialize models
        self._initialize_models()
        
        logger.info("AI Intelligence Engine initialized")
    
    def _initialize_models(self):
        """Initialize ML models."""
        model_types = [
            MLModel.ROUTING_OPTIMIZER,
            MLModel.LOAD_PREDICTOR,
            MLModel.QUALITY_PREDICTOR,
            MLModel.COST_OPTIMIZER,
            MLModel.ANOMALY_DETECTOR
        ]
        
        for model_type in model_types:
            self.models[model_type] = SimpleMLModel(model_type)
    
    async def start(self):
        """Start AI intelligence engine."""
        if self._running:
            return
        
        self._running = True
        
        # Start background training
        if self.auto_training_enabled:
            self._training_task = asyncio.create_task(self._training_loop())
        
        logger.info("AI Intelligence Engine started")
    
    async def stop(self):
        """Stop AI intelligence engine."""
        self._running = False
        
        if self._training_task:
            self._training_task.cancel()
            try:
                await self._training_task
            except asyncio.CancelledError:
                pass
        
        logger.info("AI Intelligence Engine stopped")
    
    def extract_features(self, request_data: Dict[str, Any]) -> FeatureVector:
        """Extract features from request data."""
        # Extract basic features
        prompt = request_data.get('prompt', '')
        capability = request_data.get('capability', 'text_generation')
        priority = request_data.get('priority', 'normal')
        
        # Calculate complexity score (simple heuristic)
        complexity_score = min(1.0, len(prompt) / 1000 + 
                             prompt.count('?') * 0.1 + 
                             prompt.count('!') * 0.1)
        
        # Time features
        current_time = time.localtime()
        
        # Get system metrics (mock data for demo)
        provider_health = request_data.get('provider_health', {'gemini': 0.9, 'openai': 0.8})
        provider_load = request_data.get('provider_load', {'gemini': 0.3, 'openai': 0.5})
        provider_performance = request_data.get('provider_performance', {'gemini': 0.85, 'openai': 0.80})
        
        return FeatureVector(
            capability=capability,
            prompt_length=len(prompt),
            complexity_score=complexity_score,
            priority=priority,
            time_of_day=current_time.tm_hour,
            day_of_week=current_time.tm_wday,
            user_tier=request_data.get('user_tier', 'standard'),
            region=request_data.get('region', 'us-east-1'),
            recent_latency=request_data.get('recent_latency', 0.5),
            recent_success_rate=request_data.get('recent_success_rate', 0.95),
            recent_cost=request_data.get('recent_cost', 0.001),
            provider_health=provider_health,
            provider_load=provider_load,
            provider_performance=provider_performance,
            global_load=request_data.get('global_load', 0.4),
            cache_hit_rate=request_data.get('cache_hit_rate', 0.7),
            error_rate=request_data.get('error_rate', 0.02)
        )
    
    async def predict_optimal_routing(self, request_data: Dict[str, Any]) -> Optional[Prediction]:
        """Predict optimal routing for a request."""
        features = self.extract_features(request_data)
        
        # Try routing optimizer first
        routing_model = self.models.get(MLModel.ROUTING_OPTIMIZER)
        if routing_model and routing_model.is_trained:
            prediction = routing_model.predict(features)
            if prediction and prediction.confidence > 0.5:
                return prediction
        
        # Fallback to cost optimizer
        cost_model = self.models.get(MLModel.COST_OPTIMIZER)
        if cost_model and cost_model.is_trained:
            return cost_model.predict(features)
        
        return None
    
    async def predict_quality(self, request_data: Dict[str, Any]) -> Optional[Prediction]:
        """Predict quality score for a request."""
        features = self.extract_features(request_data)
        
        quality_model = self.models.get(MLModel.QUALITY_PREDICTOR)
        if quality_model and quality_model.is_trained:
            return quality_model.predict(features)
        
        return None
    
    async def detect_anomalies(self, request_data: Dict[str, Any]) -> Optional[Prediction]:
        """Detect anomalies in system behavior."""
        features = self.extract_features(request_data)
        
        anomaly_model = self.models.get(MLModel.ANOMALY_DETECTOR)
        if anomaly_model and anomaly_model.is_trained:
            return anomaly_model.predict(features)
        
        return None
    
    async def predict_load(self, request_data: Dict[str, Any]) -> Optional[Prediction]:
        """Predict system load."""
        features = self.extract_features(request_data)
        
        load_model = self.models.get(MLModel.LOAD_PREDICTOR)
        if load_model and load_model.is_trained:
            return load_model.predict(features)
        
        return None
    
    def record_training_data(self, request_data: Dict[str, Any], outcome: Dict[str, Any]):
        """Record training data from request outcome."""
        features = self.extract_features(request_data)
        
        training_data = TrainingData(
            features=features,
            target=outcome.get('selected_provider'),
            outcome=outcome
        )
        
        # Add to all relevant models
        for model in self.models.values():
            model.add_training_data(training_data)
        
        # Add to training queue for batch processing
        self.training_queue.append(training_data)
    
    async def _training_loop(self):
        """Background training loop."""
        while self._running:
            try:
                await self._train_models()
                await asyncio.sleep(self.training_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in training loop: {e}")
                await asyncio.sleep(self.training_interval)
    
    async def _train_models(self):
        """Train all models with available data."""
        for model_type, model in self.models.items():
            if len(model.training_data) >= self.min_training_data:
                try:
                    success = model.train()
                    if success:
                        logger.info(f"Successfully trained {model_type.value} model")
                    else:
                        logger.warning(f"Failed to train {model_type.value} model")
                except Exception as e:
                    logger.error(f"Error training {model_type.value}: {e}")
    
    def get_intelligence_status(self) -> Dict[str, Any]:
        """Get AI intelligence engine status."""
        model_status = {}
        for model_type, model in self.models.items():
            model_status[model_type.value] = {
                "is_trained": model.is_trained,
                "training_data_count": len(model.training_data),
                "last_training": model.last_training,
                "version": model.version
            }
        
        return {
            "models": model_status,
            "auto_training_enabled": self.auto_training_enabled,
            "training_interval": self.training_interval,
            "prediction_cache_size": len(self.prediction_cache),
            "training_queue_size": len(self.training_queue),
            "total_predictions": len(self.prediction_history)
        }


# Global AI intelligence engine instance
ai_intelligence_engine = AIIntelligenceEngine()
