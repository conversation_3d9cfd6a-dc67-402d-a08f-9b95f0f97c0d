"""
Enhanced Function Executor for advanced function calling capabilities.
"""
import asyncio
import logging
import time
import inspect
from typing import Any, Dict, List, Optional, Callable, Union
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from dataclasses import dataclass
import json

from ai_service.models.schemas import (
    FunctionExecutionConfig, FunctionChain, FunctionChainStep,
    FunctionExecutionResult, FunctionChainResult, EnhancedFunctionDeclaration
)
from ai_service.core.exceptions import (
    AIServiceException, ValidationError, APIError, ErrorCode
)

logger = logging.getLogger(__name__)


@dataclass
class ExecutionContext:
    """Context for function execution."""
    step_results: Dict[str, Any]
    global_config: Optional[FunctionExecutionConfig]
    chain_name: Optional[str] = None
    step_index: int = 0


class EnhancedFunctionExecutor:
    """Enhanced function executor with chaining, parallel execution, and retry capabilities."""
    
    def __init__(self, max_workers: int = 8):
        self.function_registry: Dict[str, Callable] = {}
        self.function_metadata: Dict[str, EnhancedFunctionDeclaration] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.default_config = FunctionExecutionConfig()
        
        # Built-in utility functions
        self._register_builtin_functions()
        
        logger.info(f"Enhanced function executor initialized with {max_workers} workers")
    
    def _register_builtin_functions(self):
        """Register built-in utility functions."""
        
        def get_current_time() -> str:
            """Get current timestamp."""
            return time.strftime("%Y-%m-%d %H:%M:%S")
        
        def format_json(data: Any) -> str:
            """Format data as JSON."""
            return json.dumps(data, indent=2, ensure_ascii=False)
        
        def calculate_sum(numbers: List[Union[int, float]]) -> Union[int, float]:
            """Calculate sum of numbers."""
            return sum(numbers)
        
        def string_length(text: str) -> int:
            """Get length of string."""
            return len(text)
        
        # Register built-in functions
        self.register_function("get_current_time", get_current_time)
        self.register_function("format_json", format_json)
        self.register_function("calculate_sum", calculate_sum)
        self.register_function("string_length", string_length)
    
    def register_function(
        self, 
        name: str, 
        func: Callable, 
        metadata: Optional[EnhancedFunctionDeclaration] = None
    ):
        """Register a function for execution.
        
        Args:
            name: Function name
            func: Function to register
            metadata: Function metadata
        """
        self.function_registry[name] = func
        
        if metadata:
            self.function_metadata[name] = metadata
        else:
            # Create basic metadata from function signature
            sig = inspect.signature(func)
            parameters = []
            
            for param_name, param in sig.parameters.items():
                param_type = "string"  # Default type
                if param.annotation != inspect.Parameter.empty:
                    if param.annotation == int:
                        param_type = "integer"
                    elif param.annotation == float:
                        param_type = "number"
                    elif param.annotation == bool:
                        param_type = "boolean"
                    elif param.annotation == list:
                        param_type = "array"
                    elif param.annotation == dict:
                        param_type = "object"
                
                from ai_service.models.schemas import FunctionParameter
                parameters.append(FunctionParameter(
                    name=param_name,
                    type=param_type,
                    description=f"Parameter {param_name}",
                    required=param.default == inspect.Parameter.empty
                ))
            
            self.function_metadata[name] = EnhancedFunctionDeclaration(
                name=name,
                description=func.__doc__ or f"Function {name}",
                parameters=parameters
            )
        
        logger.info(f"Registered function: {name}")
    
    def unregister_function(self, name: str):
        """Unregister a function.
        
        Args:
            name: Function name to unregister
        """
        if name in self.function_registry:
            del self.function_registry[name]
        if name in self.function_metadata:
            del self.function_metadata[name]
        logger.info(f"Unregistered function: {name}")
    
    def list_functions(self) -> List[EnhancedFunctionDeclaration]:
        """List all registered functions.
        
        Returns:
            List of function declarations
        """
        return list(self.function_metadata.values())
    
    def validate_function_call(self, function_name: str, arguments: Dict[str, Any]) -> None:
        """Validate a function call.
        
        Args:
            function_name: Name of the function
            arguments: Function arguments
            
        Raises:
            ValidationError: If validation fails
        """
        if function_name not in self.function_registry:
            raise ValidationError(
                f"Function '{function_name}' is not registered",
                field="function_name",
                details={"available_functions": list(self.function_registry.keys())}
            )
        
        func = self.function_registry[function_name]
        
        try:
            # Validate function signature
            sig = inspect.signature(func)
            bound_args = sig.bind(**arguments)
            bound_args.apply_defaults()
        except TypeError as e:
            raise ValidationError(
                f"Invalid arguments for function '{function_name}': {str(e)}",
                field="arguments",
                details={"function_name": function_name, "error": str(e)}
            )
    
    async def execute_function_with_retry(
        self,
        function_name: str,
        arguments: Dict[str, Any],
        config: Optional[FunctionExecutionConfig] = None,
        context: Optional[ExecutionContext] = None
    ) -> FunctionExecutionResult:
        """Execute a function with retry logic.
        
        Args:
            function_name: Name of the function to execute
            arguments: Function arguments
            config: Execution configuration
            context: Execution context
            
        Returns:
            Function execution result
        """
        exec_config = config or self.default_config
        start_time = time.time()
        retry_count = 0
        last_error = None
        
        # Validate function call
        self.validate_function_call(function_name, arguments)
        
        for attempt in range(exec_config.max_retries + 1):
            try:
                logger.info(f"Executing function '{function_name}' (attempt {attempt + 1})")
                
                # Execute function
                result = await self._execute_single_function(
                    function_name, arguments, exec_config.timeout
                )
                
                execution_time = time.time() - start_time
                
                return FunctionExecutionResult(
                    function_name=function_name,
                    success=True,
                    result=result,
                    execution_time=execution_time,
                    retry_count=retry_count,
                    metadata={
                        "attempt": attempt + 1,
                        "context": context.chain_name if context else None
                    }
                )
                
            except Exception as e:
                retry_count += 1
                last_error = str(e)
                logger.warning(f"Function '{function_name}' failed (attempt {attempt + 1}): {e}")
                
                if attempt < exec_config.max_retries:
                    await asyncio.sleep(exec_config.retry_delay)
                else:
                    break
        
        # All retries failed
        execution_time = time.time() - start_time
        
        return FunctionExecutionResult(
            function_name=function_name,
            success=False,
            error=f"Function failed after {exec_config.max_retries + 1} attempts: {last_error}",
            execution_time=execution_time,
            retry_count=retry_count,
            metadata={
                "max_retries_reached": True,
                "context": context.chain_name if context else None
            }
        )
    
    async def _execute_single_function(
        self,
        function_name: str,
        arguments: Dict[str, Any],
        timeout: int
    ) -> Any:
        """Execute a single function with timeout.
        
        Args:
            function_name: Function name
            arguments: Function arguments
            timeout: Execution timeout
            
        Returns:
            Function result
        """
        func = self.function_registry[function_name]
        
        # Execute function in thread pool with timeout
        loop = asyncio.get_event_loop()
        
        try:
            result = await asyncio.wait_for(
                loop.run_in_executor(self.executor, func, **arguments),
                timeout=timeout
            )
            return result
        except asyncio.TimeoutError:
            raise Exception(f"Function execution timed out after {timeout} seconds")
    
    async def execute_parallel_functions(
        self,
        function_calls: List[Dict[str, Any]],
        config: Optional[FunctionExecutionConfig] = None
    ) -> List[FunctionExecutionResult]:
        """Execute multiple functions in parallel.
        
        Args:
            function_calls: List of function calls with name and arguments
            config: Execution configuration
            
        Returns:
            List of execution results
        """
        exec_config = config or self.default_config
        max_parallel = min(exec_config.max_parallel, len(function_calls))
        
        logger.info(f"Executing {len(function_calls)} functions in parallel (max {max_parallel})")
        
        # Create semaphore to limit concurrent executions
        semaphore = asyncio.Semaphore(max_parallel)
        
        async def execute_with_semaphore(call):
            async with semaphore:
                return await self.execute_function_with_retry(
                    call["name"], call["arguments"], exec_config
                )
        
        # Execute all functions concurrently
        tasks = [execute_with_semaphore(call) for call in function_calls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to error results
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append(FunctionExecutionResult(
                    function_name=function_calls[i]["name"],
                    success=False,
                    error=str(result),
                    execution_time=0.0,
                    retry_count=0,
                    metadata={"parallel_execution": True, "index": i}
                ))
            else:
                result.metadata = result.metadata or {}
                result.metadata.update({"parallel_execution": True, "index": i})
                final_results.append(result)
        
        return final_results


    async def execute_function_chain(
        self,
        chain: FunctionChain,
        initial_context: Optional[Dict[str, Any]] = None
    ) -> FunctionChainResult:
        """Execute a function chain with dependency management.

        Args:
            chain: Function chain to execute
            initial_context: Initial context data

        Returns:
            Chain execution result
        """
        start_time = time.time()
        context = ExecutionContext(
            step_results=initial_context or {},
            global_config=chain.execution_config or self.default_config,
            chain_name=chain.name
        )

        step_results = []
        executed_steps = 0

        logger.info(f"Executing function chain '{chain.name}' with {len(chain.steps)} steps")

        try:
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(chain.steps)

            # Execute steps in dependency order
            for step_batch in dependency_graph:
                batch_results = []

                # Check if we can execute steps in parallel
                if len(step_batch) > 1 and context.global_config.parallel_execution:
                    # Execute batch in parallel
                    function_calls = []
                    for step in step_batch:
                        # Resolve arguments with context
                        resolved_args = self._resolve_step_arguments(step, context)
                        function_calls.append({
                            "name": step.function_name,
                            "arguments": resolved_args
                        })

                    batch_results = await self.execute_parallel_functions(
                        function_calls, context.global_config
                    )
                else:
                    # Execute batch sequentially
                    for step in step_batch:
                        # Check step condition
                        if not self._evaluate_step_condition(step, context):
                            logger.info(f"Skipping step '{step.function_name}' due to condition")
                            continue

                        # Resolve arguments with context
                        resolved_args = self._resolve_step_arguments(step, context)

                        # Execute step
                        step_config = step.retry_config or context.global_config
                        result = await self.execute_function_with_retry(
                            step.function_name, resolved_args, step_config, context
                        )

                        batch_results.append(result)

                # Update context with results
                for i, result in enumerate(batch_results):
                    step = step_batch[i] if i < len(step_batch) else None
                    if step:
                        context.step_results[step.function_name] = result.result
                        context.step_index += 1

                    step_results.append(result)
                    if result.success:
                        executed_steps += 1
                    else:
                        # Chain failed
                        execution_time = time.time() - start_time
                        return FunctionChainResult(
                            chain_name=chain.name,
                            success=False,
                            steps_executed=executed_steps,
                            total_steps=len(chain.steps),
                            execution_time=execution_time,
                            step_results=step_results,
                            error=f"Step '{result.function_name}' failed: {result.error}"
                        )

            # Chain completed successfully
            execution_time = time.time() - start_time
            final_result = self._aggregate_chain_results(step_results, context)

            return FunctionChainResult(
                chain_name=chain.name,
                success=True,
                steps_executed=executed_steps,
                total_steps=len(chain.steps),
                execution_time=execution_time,
                step_results=step_results,
                final_result=final_result
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Function chain '{chain.name}' failed: {e}")

            return FunctionChainResult(
                chain_name=chain.name,
                success=False,
                steps_executed=executed_steps,
                total_steps=len(chain.steps),
                execution_time=execution_time,
                step_results=step_results,
                error=str(e)
            )

    def _build_dependency_graph(self, steps: List[FunctionChainStep]) -> List[List[FunctionChainStep]]:
        """Build dependency graph for function chain steps.

        Args:
            steps: List of chain steps

        Returns:
            List of step batches in execution order
        """
        # Create step lookup
        step_map = {step.function_name: step for step in steps}

        # Build dependency graph
        graph = []
        remaining_steps = set(step.function_name for step in steps)
        completed_steps = set()

        while remaining_steps:
            # Find steps with no unmet dependencies
            ready_steps = []
            for step_name in remaining_steps:
                step = step_map[step_name]
                dependencies = step.depends_on or []

                if all(dep in completed_steps for dep in dependencies):
                    ready_steps.append(step)

            if not ready_steps:
                # Circular dependency or missing dependency
                raise ValueError(f"Circular dependency detected in chain steps: {remaining_steps}")

            graph.append(ready_steps)

            # Mark steps as completed
            for step in ready_steps:
                completed_steps.add(step.function_name)
                remaining_steps.remove(step.function_name)

        return graph

    def _resolve_step_arguments(
        self,
        step: FunctionChainStep,
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """Resolve step arguments with context data.

        Args:
            step: Function chain step
            context: Execution context

        Returns:
            Resolved arguments
        """
        resolved_args = {}

        for key, value in step.arguments.items():
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                # Variable reference
                var_name = value[2:-1]
                if var_name in context.step_results:
                    resolved_args[key] = context.step_results[var_name]
                else:
                    logger.warning(f"Variable '{var_name}' not found in context")
                    resolved_args[key] = value
            else:
                resolved_args[key] = value

        return resolved_args

    def _evaluate_step_condition(
        self,
        step: FunctionChainStep,
        context: ExecutionContext
    ) -> bool:
        """Evaluate step execution condition.

        Args:
            step: Function chain step
            context: Execution context

        Returns:
            Whether step should be executed
        """
        if not step.condition:
            return True

        # Simple condition evaluation (can be enhanced)
        try:
            # Replace variables in condition
            condition = step.condition
            for var_name, var_value in context.step_results.items():
                condition = condition.replace(f"${{{var_name}}}", str(var_value))

            # Evaluate condition (basic implementation)
            # In production, use a safer expression evaluator
            return eval(condition)
        except Exception as e:
            logger.warning(f"Failed to evaluate condition '{step.condition}': {e}")
            return True

    def _aggregate_chain_results(
        self,
        step_results: List[FunctionExecutionResult],
        context: ExecutionContext
    ) -> Any:
        """Aggregate results from chain execution.

        Args:
            step_results: Results from all steps
            context: Execution context

        Returns:
            Aggregated result
        """
        # Simple aggregation - return all results
        return {
            "step_results": {result.function_name: result.result for result in step_results if result.success},
            "execution_summary": {
                "total_steps": len(step_results),
                "successful_steps": sum(1 for r in step_results if r.success),
                "failed_steps": sum(1 for r in step_results if not r.success),
                "total_execution_time": sum(r.execution_time for r in step_results)
            }
        }


# Global enhanced function executor
enhanced_function_executor = EnhancedFunctionExecutor()
