"""
Advanced Compliance Manager for Phase 7.
Supports SOC2, GDPR, HIPAA, and other enterprise compliance requirements.
"""

import asyncio
import logging
import time
import json
import hashlib
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import uuid
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ComplianceFramework(Enum):
    """Supported compliance frameworks."""
    SOC2 = "soc2"
    GDPR = "gdpr"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    ISO_27001 = "iso_27001"
    CCPA = "ccpa"
    FERPA = "ferpa"
    FISMA = "fisma"


class DataClassification(Enum):
    """Data classification levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"


class ProcessingPurpose(Enum):
    """Data processing purposes."""
    AI_GENERATION = "ai_generation"
    ANALYTICS = "analytics"
    PERSONALIZATION = "personalization"
    SECURITY = "security"
    COMPLIANCE = "compliance"
    RESEARCH = "research"
    MARKETING = "marketing"


class ConsentStatus(Enum):
    """User consent status."""
    GRANTED = "granted"
    DENIED = "denied"
    WITHDRAWN = "withdrawn"
    PENDING = "pending"
    EXPIRED = "expired"


@dataclass
class DataSubject:
    """Data subject (user) information."""
    subject_id: str
    email: Optional[str]
    jurisdiction: str  # Country/region code
    
    # Consent management
    consents: Dict[ProcessingPurpose, ConsentStatus] = field(default_factory=dict)
    consent_timestamps: Dict[ProcessingPurpose, float] = field(default_factory=dict)
    
    # Rights exercised
    access_requests: List[str] = field(default_factory=list)
    deletion_requests: List[str] = field(default_factory=list)
    portability_requests: List[str] = field(default_factory=list)
    
    # Metadata
    created_at: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)


@dataclass
class DataProcessingRecord:
    """Record of data processing activity."""
    record_id: str
    subject_id: str
    purpose: ProcessingPurpose
    data_types: List[str]
    classification: DataClassification
    
    # Processing details
    processor: str  # System/service that processed data
    legal_basis: str  # Legal basis for processing
    retention_period: int  # Days
    
    # Location and transfer
    processing_location: str
    data_transfers: List[str] = field(default_factory=list)
    
    # Timestamps
    processed_at: float = field(default_factory=time.time)
    expires_at: Optional[float] = None
    
    # Security
    encryption_used: bool = True
    access_controls: List[str] = field(default_factory=list)


@dataclass
class ComplianceViolation:
    """Compliance violation record."""
    violation_id: str
    framework: ComplianceFramework
    severity: str  # "low", "medium", "high", "critical"
    description: str
    
    # Context
    affected_subjects: List[str] = field(default_factory=list)
    data_types: List[str] = field(default_factory=list)
    system_component: Optional[str] = None
    
    # Resolution
    status: str = "open"  # "open", "investigating", "resolved", "false_positive"
    resolution_notes: Optional[str] = None
    
    # Timestamps
    detected_at: float = field(default_factory=time.time)
    resolved_at: Optional[float] = None
    
    # Notifications
    authorities_notified: bool = False
    subjects_notified: bool = False


@dataclass
class AuditTrail:
    """Audit trail entry."""
    audit_id: str
    event_type: str
    user_id: Optional[str]
    subject_id: Optional[str]
    
    # Event details
    action: str
    resource: str
    outcome: str  # "success", "failure", "partial"
    
    # Context
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    
    # Data
    before_state: Optional[Dict[str, Any]] = None
    after_state: Optional[Dict[str, Any]] = None
    
    # Timestamp
    timestamp: float = field(default_factory=time.time)


class ComplianceManager:
    """Advanced compliance management system."""
    
    def __init__(self):
        self.enabled_frameworks: Set[ComplianceFramework] = set()
        self.data_subjects: Dict[str, DataSubject] = {}
        self.processing_records: List[DataProcessingRecord] = []
        self.violations: List[ComplianceViolation] = []
        self.audit_trail: List[AuditTrail] = []
        
        # Configuration
        self.data_retention_policies: Dict[DataClassification, int] = {
            DataClassification.PUBLIC: 365 * 5,  # 5 years
            DataClassification.INTERNAL: 365 * 3,  # 3 years
            DataClassification.CONFIDENTIAL: 365 * 2,  # 2 years
            DataClassification.RESTRICTED: 365,  # 1 year
            DataClassification.TOP_SECRET: 90  # 90 days
        }
        
        self.consent_expiry_days = 365  # 1 year
        self.breach_notification_hours = 72  # GDPR requirement
        
        # Monitoring
        self.compliance_metrics: Dict[str, Any] = {
            "total_subjects": 0,
            "active_consents": 0,
            "processing_records": 0,
            "violations": 0,
            "audit_entries": 0
        }
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("Compliance Manager initialized")
    
    async def start(self):
        """Start compliance manager."""
        if self._running:
            return
        
        self._running = True
        
        # Start background tasks
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info("Compliance Manager started")
    
    async def stop(self):
        """Stop compliance manager."""
        self._running = False
        
        # Cancel background tasks
        if self._cleanup_task:
            self._cleanup_task.cancel()
        if self._monitoring_task:
            self._monitoring_task.cancel()
        
        # Wait for tasks to complete
        tasks = [self._cleanup_task, self._monitoring_task]
        for task in tasks:
            if task:
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("Compliance Manager stopped")
    
    def enable_framework(self, framework: ComplianceFramework):
        """Enable compliance framework."""
        self.enabled_frameworks.add(framework)
        logger.info(f"Enabled compliance framework: {framework.value}")
    
    def disable_framework(self, framework: ComplianceFramework):
        """Disable compliance framework."""
        self.enabled_frameworks.discard(framework)
        logger.info(f"Disabled compliance framework: {framework.value}")
    
    async def register_data_subject(
        self,
        subject_id: str,
        email: Optional[str] = None,
        jurisdiction: str = "US"
    ) -> DataSubject:
        """Register a new data subject."""
        if subject_id in self.data_subjects:
            return self.data_subjects[subject_id]
        
        subject = DataSubject(
            subject_id=subject_id,
            email=email,
            jurisdiction=jurisdiction
        )
        
        self.data_subjects[subject_id] = subject
        self._update_metrics()
        
        # Audit trail
        await self._log_audit_event(
            event_type="data_subject_registration",
            subject_id=subject_id,
            action="register",
            resource="data_subject",
            outcome="success"
        )
        
        logger.info(f"Registered data subject: {subject_id}")
        return subject
    
    async def record_consent(
        self,
        subject_id: str,
        purpose: ProcessingPurpose,
        status: ConsentStatus,
        legal_basis: Optional[str] = None
    ) -> bool:
        """Record user consent for data processing."""
        if subject_id not in self.data_subjects:
            await self.register_data_subject(subject_id)
        
        subject = self.data_subjects[subject_id]
        
        # Update consent
        old_status = subject.consents.get(purpose)
        subject.consents[purpose] = status
        subject.consent_timestamps[purpose] = time.time()
        subject.last_updated = time.time()
        
        # Audit trail
        await self._log_audit_event(
            event_type="consent_update",
            subject_id=subject_id,
            action="update_consent",
            resource=f"consent_{purpose.value}",
            outcome="success",
            before_state={"status": old_status.value if old_status else None},
            after_state={"status": status.value}
        )
        
        self._update_metrics()
        
        logger.info(f"Recorded consent for {subject_id}: {purpose.value} = {status.value}")
        return True
    
    async def record_data_processing(
        self,
        subject_id: str,
        purpose: ProcessingPurpose,
        data_types: List[str],
        classification: DataClassification,
        processor: str,
        legal_basis: str,
        processing_location: str = "US"
    ) -> str:
        """Record data processing activity."""
        # Check consent if required
        if await self._requires_consent(purpose, classification):
            if not await self._has_valid_consent(subject_id, purpose):
                raise ValueError(f"No valid consent for {purpose.value}")
        
        # Create processing record
        record_id = str(uuid.uuid4())
        retention_days = self.data_retention_policies.get(classification, 365)
        
        record = DataProcessingRecord(
            record_id=record_id,
            subject_id=subject_id,
            purpose=purpose,
            data_types=data_types,
            classification=classification,
            processor=processor,
            legal_basis=legal_basis,
            retention_period=retention_days,
            processing_location=processing_location,
            expires_at=time.time() + (retention_days * 86400)
        )
        
        self.processing_records.append(record)
        
        # Audit trail
        await self._log_audit_event(
            event_type="data_processing",
            subject_id=subject_id,
            action="process_data",
            resource="personal_data",
            outcome="success",
            after_state={
                "purpose": purpose.value,
                "data_types": data_types,
                "classification": classification.value
            }
        )
        
        self._update_metrics()
        
        logger.info(f"Recorded data processing: {record_id}")
        return record_id
    
    async def handle_subject_access_request(self, subject_id: str) -> Dict[str, Any]:
        """Handle data subject access request (GDPR Article 15)."""
        if subject_id not in self.data_subjects:
            return {"error": "Subject not found"}
        
        subject = self.data_subjects[subject_id]
        
        # Collect all data
        processing_records = [
            {
                "record_id": r.record_id,
                "purpose": r.purpose.value,
                "data_types": r.data_types,
                "processed_at": datetime.fromtimestamp(r.processed_at).isoformat(),
                "legal_basis": r.legal_basis,
                "retention_period": r.retention_period
            }
            for r in self.processing_records
            if r.subject_id == subject_id
        ]
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        subject.access_requests.append(request_id)
        
        # Audit trail
        await self._log_audit_event(
            event_type="subject_access_request",
            subject_id=subject_id,
            action="access_request",
            resource="personal_data",
            outcome="success"
        )
        
        response = {
            "request_id": request_id,
            "subject_id": subject_id,
            "email": subject.email,
            "jurisdiction": subject.jurisdiction,
            "consents": {
                purpose.value: {
                    "status": status.value,
                    "timestamp": datetime.fromtimestamp(
                        subject.consent_timestamps.get(purpose, 0)
                    ).isoformat()
                }
                for purpose, status in subject.consents.items()
            },
            "processing_records": processing_records,
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Generated subject access response: {request_id}")
        return response
    
    async def handle_deletion_request(self, subject_id: str) -> bool:
        """Handle data subject deletion request (GDPR Article 17)."""
        if subject_id not in self.data_subjects:
            return False
        
        # Check if deletion is allowed
        if not await self._can_delete_subject_data(subject_id):
            await self._log_violation(
                framework=ComplianceFramework.GDPR,
                severity="medium",
                description=f"Deletion request denied for {subject_id} - legal obligation to retain"
            )
            return False
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        subject = self.data_subjects[subject_id]
        subject.deletion_requests.append(request_id)
        
        # Mark processing records for deletion
        for record in self.processing_records:
            if record.subject_id == subject_id:
                record.expires_at = time.time()  # Immediate expiry
        
        # Audit trail
        await self._log_audit_event(
            event_type="deletion_request",
            subject_id=subject_id,
            action="delete_request",
            resource="personal_data",
            outcome="success"
        )
        
        logger.info(f"Processed deletion request for {subject_id}")
        return True
    
    async def _requires_consent(self, purpose: ProcessingPurpose, classification: DataClassification) -> bool:
        """Check if consent is required for processing."""
        # GDPR requires consent for non-essential processing
        if ComplianceFramework.GDPR in self.enabled_frameworks:
            if purpose in [ProcessingPurpose.MARKETING, ProcessingPurpose.PERSONALIZATION]:
                return True
            if classification in [DataClassification.CONFIDENTIAL, DataClassification.RESTRICTED]:
                return True
        
        return False
    
    async def _has_valid_consent(self, subject_id: str, purpose: ProcessingPurpose) -> bool:
        """Check if subject has valid consent for purpose."""
        if subject_id not in self.data_subjects:
            return False
        
        subject = self.data_subjects[subject_id]
        
        if purpose not in subject.consents:
            return False
        
        if subject.consents[purpose] != ConsentStatus.GRANTED:
            return False
        
        # Check if consent has expired
        consent_time = subject.consent_timestamps.get(purpose, 0)
        if time.time() - consent_time > (self.consent_expiry_days * 86400):
            return False
        
        return True
    
    async def _can_delete_subject_data(self, subject_id: str) -> bool:
        """Check if subject data can be deleted."""
        # Check for legal obligations to retain data
        for record in self.processing_records:
            if record.subject_id == subject_id:
                if record.purpose in [ProcessingPurpose.COMPLIANCE, ProcessingPurpose.SECURITY]:
                    if record.expires_at and record.expires_at > time.time():
                        return False
        
        return True
    
    async def _log_violation(
        self,
        framework: ComplianceFramework,
        severity: str,
        description: str,
        affected_subjects: Optional[List[str]] = None
    ):
        """Log compliance violation."""
        violation = ComplianceViolation(
            violation_id=str(uuid.uuid4()),
            framework=framework,
            severity=severity,
            description=description,
            affected_subjects=affected_subjects or []
        )
        
        self.violations.append(violation)
        
        # Check if breach notification is required
        if severity in ["high", "critical"] and framework == ComplianceFramework.GDPR:
            # Would trigger breach notification process
            logger.warning(f"GDPR breach detected: {description}")
        
        logger.warning(f"Compliance violation: {framework.value} - {description}")
    
    async def _log_audit_event(
        self,
        event_type: str,
        action: str,
        resource: str,
        outcome: str,
        user_id: Optional[str] = None,
        subject_id: Optional[str] = None,
        before_state: Optional[Dict[str, Any]] = None,
        after_state: Optional[Dict[str, Any]] = None
    ):
        """Log audit trail event."""
        audit_entry = AuditTrail(
            audit_id=str(uuid.uuid4()),
            event_type=event_type,
            user_id=user_id,
            subject_id=subject_id,
            action=action,
            resource=resource,
            outcome=outcome,
            before_state=before_state,
            after_state=after_state
        )
        
        self.audit_trail.append(audit_entry)
        
        # Keep only last 100,000 entries
        if len(self.audit_trail) > 100000:
            self.audit_trail = self.audit_trail[-100000:]
    
    def _update_metrics(self):
        """Update compliance metrics."""
        self.compliance_metrics.update({
            "total_subjects": len(self.data_subjects),
            "active_consents": sum(
                1 for subject in self.data_subjects.values()
                for status in subject.consents.values()
                if status == ConsentStatus.GRANTED
            ),
            "processing_records": len(self.processing_records),
            "violations": len(self.violations),
            "audit_entries": len(self.audit_trail)
        })
    
    async def _cleanup_loop(self):
        """Background cleanup of expired data."""
        while self._running:
            try:
                await self._cleanup_expired_data()
                await asyncio.sleep(3600)  # Run every hour
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(3600)
    
    async def _cleanup_expired_data(self):
        """Clean up expired data according to retention policies."""
        current_time = time.time()
        
        # Clean up expired processing records
        expired_records = [
            r for r in self.processing_records
            if r.expires_at and r.expires_at <= current_time
        ]
        
        for record in expired_records:
            self.processing_records.remove(record)
            
            # Audit trail
            await self._log_audit_event(
                event_type="data_retention",
                subject_id=record.subject_id,
                action="delete_expired",
                resource="processing_record",
                outcome="success"
            )
        
        if expired_records:
            logger.info(f"Cleaned up {len(expired_records)} expired processing records")
    
    async def _monitoring_loop(self):
        """Background compliance monitoring."""
        while self._running:
            try:
                await self._monitor_compliance()
                await asyncio.sleep(300)  # Run every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(300)
    
    async def _monitor_compliance(self):
        """Monitor for compliance violations."""
        # Check for expired consents
        current_time = time.time()
        
        for subject in self.data_subjects.values():
            for purpose, timestamp in subject.consent_timestamps.items():
                if current_time - timestamp > (self.consent_expiry_days * 86400):
                    if subject.consents.get(purpose) == ConsentStatus.GRANTED:
                        # Mark consent as expired
                        subject.consents[purpose] = ConsentStatus.EXPIRED
                        
                        await self._log_audit_event(
                            event_type="consent_expiry",
                            subject_id=subject.subject_id,
                            action="expire_consent",
                            resource=f"consent_{purpose.value}",
                            outcome="success"
                        )
    
    def get_compliance_status(self) -> Dict[str, Any]:
        """Get compliance system status."""
        return {
            "enabled_frameworks": [f.value for f in self.enabled_frameworks],
            "metrics": self.compliance_metrics,
            "recent_violations": len([
                v for v in self.violations
                if time.time() - v.detected_at < 86400  # Last 24 hours
            ]),
            "data_retention_policies": {
                classification.value: days
                for classification, days in self.data_retention_policies.items()
            },
            "consent_expiry_days": self.consent_expiry_days,
            "breach_notification_hours": self.breach_notification_hours
        }


# Global compliance manager instance
compliance_manager = ComplianceManager()
