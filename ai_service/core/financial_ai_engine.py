"""
Financial Services AI Engine for Phase 9.
Provides regulatory-compliant financial AI capabilities with risk assessment and compliance.
"""

import asyncio
import logging
import time
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import uuid
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)


class FinancialRegulation(Enum):
    """Financial regulations supported."""
    SOX = "sarbanes_oxley"           # Sarbanes-Oxley Act
    GDPR = "gdpr"                    # General Data Protection Regulation
    PCI_DSS = "pci_dss"             # Payment Card Industry Data Security Standard
    BASEL_III = "basel_iii"         # Basel III banking regulations
    MiFID_II = "mifid_ii"           # Markets in Financial Instruments Directive
    DODD_FRANK = "dodd_frank"       # Dodd-Frank Wall Street Reform
    CCPA = "ccpa"                   # California Consumer Privacy Act
    GLBA = "glba"                   # Gramm-Leach-Bliley Act


class FinancialSector(Enum):
    """Financial sectors supported."""
    BANKING = "banking"
    INVESTMENT_MANAGEMENT = "investment_management"
    INSURANCE = "insurance"
    PAYMENTS = "payments"
    LENDING = "lending"
    TRADING = "trading"
    WEALTH_MANAGEMENT = "wealth_management"
    FINTECH = "fintech"
    CRYPTOCURRENCY = "cryptocurrency"
    REGULATORY_REPORTING = "regulatory_reporting"


class RiskLevel(Enum):
    """Risk assessment levels."""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class FinancialAICapability(Enum):
    """Financial AI capabilities."""
    FRAUD_DETECTION = "fraud_detection"
    RISK_ASSESSMENT = "risk_assessment"
    CREDIT_SCORING = "credit_scoring"
    REGULATORY_COMPLIANCE = "regulatory_compliance"
    MARKET_ANALYSIS = "market_analysis"
    PORTFOLIO_OPTIMIZATION = "portfolio_optimization"
    AML_SCREENING = "aml_screening"           # Anti-Money Laundering
    KYC_VERIFICATION = "kyc_verification"     # Know Your Customer
    STRESS_TESTING = "stress_testing"
    ALGORITHMIC_TRADING = "algorithmic_trading"
    ESG_ANALYSIS = "esg_analysis"             # Environmental, Social, Governance
    FINANCIAL_PLANNING = "financial_planning"


@dataclass
class FinancialContext:
    """Financial context for AI processing."""
    sector: FinancialSector
    regulations: List[FinancialRegulation]
    
    # Institution information
    institution_id: str
    institution_type: str  # "bank", "broker", "insurer", "fintech"
    jurisdiction: str = "US"
    
    # Customer information (anonymized)
    customer_id: Optional[str] = None
    customer_segment: str = "retail"  # "retail", "corporate", "institutional"
    risk_profile: RiskLevel = RiskLevel.MEDIUM
    
    # Transaction context
    transaction_type: Optional[str] = None
    transaction_amount: Optional[float] = None
    currency: str = "USD"
    
    # Compliance requirements
    requires_audit_trail: bool = True
    requires_human_review: bool = False
    data_retention_days: int = 2555  # 7 years default


@dataclass
class FinancialAIRequest:
    """Financial AI processing request."""
    request_id: str
    capability: FinancialAICapability
    financial_context: FinancialContext
    
    # Input data
    input_data: Dict[str, Any]
    sensitive_data: Dict[str, Any] = field(default_factory=dict)
    
    # Processing requirements
    confidence_threshold: float = 0.85
    max_response_time: float = 30.0
    require_explanation: bool = True
    
    # Compliance requirements
    regulatory_approval_required: bool = False
    model_validation_required: bool = False
    
    timestamp: float = field(default_factory=time.time)


@dataclass
class FinancialAIResponse:
    """Financial AI processing response."""
    request_id: str
    capability: FinancialAICapability
    
    # Results
    primary_result: Any
    confidence_score: float
    risk_score: float
    explanation: str
    
    # Risk assessment
    risk_level: RiskLevel
    risk_factors: List[str] = field(default_factory=list)
    mitigation_strategies: List[str] = field(default_factory=list)
    
    # Regulatory compliance
    compliance_status: str = "compliant"  # "compliant", "non_compliant", "requires_review"
    regulatory_notes: List[str] = field(default_factory=list)
    audit_trail: Dict[str, Any] = field(default_factory=dict)
    
    # Recommendations
    recommendations: List[str] = field(default_factory=list)
    alerts: List[str] = field(default_factory=list)
    
    # Metadata
    processing_time: float = 0.0
    model_version: str = "1.0.0"
    timestamp: float = field(default_factory=time.time)


@dataclass
class RegulatoryRule:
    """Regulatory compliance rule."""
    rule_id: str
    regulation: FinancialRegulation
    sector: FinancialSector
    
    # Rule definition
    title: str
    description: str
    rule_logic: Dict[str, Any]
    
    # Compliance requirements
    severity: str = "medium"  # "low", "medium", "high", "critical"
    mandatory: bool = True
    effective_date: float = field(default_factory=time.time)
    
    # Monitoring
    violation_count: int = 0
    last_violation: Optional[float] = None


class PIIDetector:
    """Personally Identifiable Information detector for financial data."""
    
    def __init__(self):
        # Financial PII patterns
        self.pii_patterns = {
            'ssn': r'\b\d{3}-\d{2}-\d{4}\b',
            'credit_card': r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',
            'bank_account': r'\b\d{8,17}\b',
            'routing_number': r'\b\d{9}\b',
            'iban': r'\b[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}\b',
            'swift_code': r'\b[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?\b',
            'tax_id': r'\b\d{2}-\d{7}\b'
        }
    
    def detect_pii(self, text: str) -> Dict[str, List[str]]:
        """Detect PII in financial text."""
        detected_pii = {}
        
        for pii_type, pattern in self.pii_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                detected_pii[pii_type] = matches
        
        return detected_pii
    
    def anonymize_financial_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Anonymize financial data."""
        anonymized_data = data.copy()
        
        # Anonymize sensitive fields
        sensitive_fields = ['ssn', 'account_number', 'credit_card', 'tax_id']
        
        for field in sensitive_fields:
            if field in anonymized_data:
                if field == 'ssn':
                    anonymized_data[field] = 'XXX-XX-XXXX'
                elif field == 'credit_card':
                    anonymized_data[field] = 'XXXX-XXXX-XXXX-XXXX'
                elif field == 'account_number':
                    anonymized_data[field] = 'XXXXXXXXXX'
                else:
                    anonymized_data[field] = '[REDACTED]'
        
        return anonymized_data


class RiskAssessmentEngine:
    """Financial risk assessment engine."""
    
    def __init__(self):
        # Risk scoring models (simplified)
        self.risk_factors = {
            'credit_risk': {
                'income_debt_ratio': {'weight': 0.3, 'threshold': 0.4},
                'credit_history_length': {'weight': 0.2, 'threshold': 24},
                'payment_history': {'weight': 0.35, 'threshold': 0.95},
                'credit_utilization': {'weight': 0.15, 'threshold': 0.3}
            },
            'fraud_risk': {
                'transaction_velocity': {'weight': 0.25, 'threshold': 10},
                'geographic_anomaly': {'weight': 0.2, 'threshold': 0.8},
                'amount_anomaly': {'weight': 0.3, 'threshold': 0.9},
                'behavioral_pattern': {'weight': 0.25, 'threshold': 0.7}
            },
            'market_risk': {
                'volatility': {'weight': 0.4, 'threshold': 0.25},
                'correlation': {'weight': 0.3, 'threshold': 0.8},
                'liquidity': {'weight': 0.3, 'threshold': 0.1}
            }
        }
    
    def assess_credit_risk(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess credit risk for a customer."""
        risk_score = 0.0
        risk_factors = []
        
        # Income-to-debt ratio
        if 'income' in customer_data and 'debt' in customer_data:
            debt_ratio = customer_data['debt'] / max(customer_data['income'], 1)
            if debt_ratio > 0.4:
                risk_score += 0.3
                risk_factors.append(f"High debt-to-income ratio: {debt_ratio:.2%}")
        
        # Credit history
        if 'credit_history_months' in customer_data:
            if customer_data['credit_history_months'] < 24:
                risk_score += 0.2
                risk_factors.append("Limited credit history")
        
        # Payment history
        if 'payment_history_score' in customer_data:
            if customer_data['payment_history_score'] < 0.95:
                risk_score += 0.35
                risk_factors.append("Poor payment history")
        
        # Credit utilization
        if 'credit_utilization' in customer_data:
            if customer_data['credit_utilization'] > 0.3:
                risk_score += 0.15
                risk_factors.append("High credit utilization")
        
        # Determine risk level
        if risk_score >= 0.8:
            risk_level = RiskLevel.VERY_HIGH
        elif risk_score >= 0.6:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.4:
            risk_level = RiskLevel.MEDIUM
        elif risk_score >= 0.2:
            risk_level = RiskLevel.LOW
        else:
            risk_level = RiskLevel.VERY_LOW
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'credit_score': max(300, 850 - int(risk_score * 550))  # Convert to FICO-like score
        }
    
    def assess_fraud_risk(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess fraud risk for a transaction."""
        risk_score = 0.0
        risk_factors = []
        
        # Transaction velocity
        if 'transactions_last_hour' in transaction_data:
            if transaction_data['transactions_last_hour'] > 10:
                risk_score += 0.25
                risk_factors.append("High transaction velocity")
        
        # Geographic anomaly
        if 'location_risk_score' in transaction_data:
            if transaction_data['location_risk_score'] > 0.8:
                risk_score += 0.2
                risk_factors.append("Unusual geographic location")
        
        # Amount anomaly
        if 'amount_percentile' in transaction_data:
            if transaction_data['amount_percentile'] > 0.95:
                risk_score += 0.3
                risk_factors.append("Unusually large transaction amount")
        
        # Behavioral pattern
        if 'behavioral_score' in transaction_data:
            if transaction_data['behavioral_score'] < 0.3:
                risk_score += 0.25
                risk_factors.append("Unusual behavioral pattern")
        
        # Determine risk level
        if risk_score >= 0.8:
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.6:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.4:
            risk_level = RiskLevel.MEDIUM
        elif risk_score >= 0.2:
            risk_level = RiskLevel.LOW
        else:
            risk_level = RiskLevel.VERY_LOW
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'fraud_probability': risk_score
        }


class ComplianceEngine:
    """Financial regulatory compliance engine."""
    
    def __init__(self):
        self.regulatory_rules: Dict[str, RegulatoryRule] = {}
        self.compliance_violations: List[Dict[str, Any]] = []
        
        # Initialize regulatory rules
        self._initialize_regulatory_rules()
    
    def _initialize_regulatory_rules(self):
        """Initialize regulatory compliance rules."""
        rules = [
            RegulatoryRule(
                rule_id="sox_001",
                regulation=FinancialRegulation.SOX,
                sector=FinancialSector.BANKING,
                title="Financial Reporting Accuracy",
                description="Ensure accuracy of financial reporting and internal controls",
                rule_logic={"requires_audit": True, "accuracy_threshold": 0.99},
                severity="critical",
                mandatory=True
            ),
            RegulatoryRule(
                rule_id="aml_001",
                regulation=FinancialRegulation.GLBA,
                sector=FinancialSector.BANKING,
                title="Anti-Money Laundering Screening",
                description="Screen transactions for money laundering indicators",
                rule_logic={"transaction_threshold": 10000, "requires_reporting": True},
                severity="high",
                mandatory=True
            ),
            RegulatoryRule(
                rule_id="kyc_001",
                regulation=FinancialRegulation.GLBA,
                sector=FinancialSector.BANKING,
                title="Know Your Customer Verification",
                description="Verify customer identity and risk profile",
                rule_logic={"identity_verification": True, "risk_assessment": True},
                severity="high",
                mandatory=True
            )
        ]
        
        for rule in rules:
            self.regulatory_rules[rule.rule_id] = rule
    
    def check_compliance(self, request: FinancialAIRequest, response: FinancialAIResponse) -> Dict[str, Any]:
        """Check regulatory compliance for request/response."""
        compliance_results = {
            'status': 'compliant',
            'violations': [],
            'warnings': [],
            'required_actions': []
        }
        
        # Check applicable regulations
        for regulation in request.financial_context.regulations:
            applicable_rules = [
                rule for rule in self.regulatory_rules.values()
                if rule.regulation == regulation and rule.sector == request.financial_context.sector
            ]
            
            for rule in applicable_rules:
                violation = self._check_rule_compliance(rule, request, response)
                if violation:
                    compliance_results['violations'].append(violation)
                    compliance_results['status'] = 'non_compliant'
        
        # Check data retention requirements
        if request.financial_context.requires_audit_trail:
            if not response.audit_trail:
                compliance_results['warnings'].append("Missing audit trail")
        
        # Check confidence thresholds for critical decisions
        if request.capability in [FinancialAICapability.FRAUD_DETECTION, FinancialAICapability.AML_SCREENING]:
            if response.confidence_score < 0.9:
                compliance_results['warnings'].append("Low confidence for critical decision")
                compliance_results['required_actions'].append("Human review required")
        
        return compliance_results
    
    def _check_rule_compliance(self, rule: RegulatoryRule, request: FinancialAIRequest, response: FinancialAIResponse) -> Optional[Dict[str, Any]]:
        """Check compliance with specific regulatory rule."""
        # SOX compliance check
        if rule.regulation == FinancialRegulation.SOX:
            if rule.rule_logic.get('requires_audit') and not response.audit_trail:
                return {
                    'rule_id': rule.rule_id,
                    'violation': 'Missing audit trail required by SOX',
                    'severity': rule.severity
                }
        
        # AML compliance check
        if rule.regulation == FinancialRegulation.GLBA and 'aml' in rule.rule_id:
            transaction_amount = request.financial_context.transaction_amount
            if transaction_amount and transaction_amount >= rule.rule_logic.get('transaction_threshold', 10000):
                if request.capability != FinancialAICapability.AML_SCREENING:
                    return {
                        'rule_id': rule.rule_id,
                        'violation': 'Large transaction requires AML screening',
                        'severity': rule.severity
                    }
        
        return None


class FinancialAIEngine:
    """Financial services AI engine with regulatory compliance."""
    
    def __init__(self):
        self.pii_detector = PIIDetector()
        self.risk_engine = RiskAssessmentEngine()
        self.compliance_engine = ComplianceEngine()
        
        # Processing history for audit
        self.processing_history: List[FinancialAIResponse] = []
        self.audit_logs: List[Dict[str, Any]] = []
        
        # Compliance settings
        self.regulatory_compliance_enabled = True
        self.pii_protection_enabled = True
        self.audit_all_transactions = True
        
        logger.info("Financial AI Engine initialized with regulatory compliance")
    
    async def process_financial_request(self, request: FinancialAIRequest) -> FinancialAIResponse:
        """Process financial AI request with regulatory compliance."""
        start_time = time.time()
        
        try:
            # Audit logging
            if self.audit_all_transactions:
                await self._log_audit_event("request_received", request)
            
            # PII detection and protection
            if self.pii_protection_enabled:
                request.sensitive_data = self.pii_detector.anonymize_financial_data(request.sensitive_data)
            
            # Process based on capability
            if request.capability == FinancialAICapability.FRAUD_DETECTION:
                response = await self._process_fraud_detection(request)
            elif request.capability == FinancialAICapability.CREDIT_SCORING:
                response = await self._process_credit_scoring(request)
            elif request.capability == FinancialAICapability.RISK_ASSESSMENT:
                response = await self._process_risk_assessment(request)
            elif request.capability == FinancialAICapability.AML_SCREENING:
                response = await self._process_aml_screening(request)
            elif request.capability == FinancialAICapability.REGULATORY_COMPLIANCE:
                response = await self._process_regulatory_compliance(request)
            else:
                response = await self._process_general_financial_query(request)
            
            # Calculate processing time
            response.processing_time = time.time() - start_time
            
            # Compliance checks
            if self.regulatory_compliance_enabled:
                compliance_results = self.compliance_engine.check_compliance(request, response)
                response.compliance_status = compliance_results['status']
                response.regulatory_notes.extend(compliance_results.get('warnings', []))
                if compliance_results['violations']:
                    response.alerts.extend([v['violation'] for v in compliance_results['violations']])
            
            # Store for audit
            self.processing_history.append(response)
            
            # Audit logging
            if self.audit_all_transactions:
                await self._log_audit_event("request_completed", response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing financial request: {e}")
            
            # Create error response
            error_response = FinancialAIResponse(
                request_id=request.request_id,
                capability=request.capability,
                primary_result=None,
                confidence_score=0.0,
                risk_score=1.0,
                risk_level=RiskLevel.CRITICAL,
                explanation=f"Processing error: {str(e)}",
                alerts=["Processing failed - manual review required"],
                processing_time=time.time() - start_time
            )
            
            await self._log_audit_event("request_failed", {"error": str(e)})
            return error_response
    
    async def _process_fraud_detection(self, request: FinancialAIRequest) -> FinancialAIResponse:
        """Process fraud detection request."""
        # Assess fraud risk
        fraud_assessment = self.risk_engine.assess_fraud_risk(request.input_data)
        
        # Generate response
        is_fraud = fraud_assessment['fraud_probability'] > 0.7
        
        return FinancialAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result={"fraud_detected": is_fraud, "fraud_probability": fraud_assessment['fraud_probability']},
            confidence_score=0.9 if fraud_assessment['fraud_probability'] > 0.8 or fraud_assessment['fraud_probability'] < 0.2 else 0.7,
            risk_score=fraud_assessment['fraud_probability'],
            risk_level=fraud_assessment['risk_level'],
            explanation="Fraud detection based on transaction patterns and risk factors",
            risk_factors=fraud_assessment['risk_factors'],
            alerts=["Potential fraud detected"] if is_fraud else [],
            recommendations=["Block transaction", "Contact customer"] if is_fraud else ["Approve transaction"],
            audit_trail={"fraud_assessment": fraud_assessment}
        )
    
    async def _process_credit_scoring(self, request: FinancialAIRequest) -> FinancialAIResponse:
        """Process credit scoring request."""
        # Assess credit risk
        credit_assessment = self.risk_engine.assess_credit_risk(request.input_data)
        
        return FinancialAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result={"credit_score": credit_assessment['credit_score']},
            confidence_score=0.85,
            risk_score=credit_assessment['risk_score'],
            risk_level=credit_assessment['risk_level'],
            explanation="Credit score based on financial history and risk factors",
            risk_factors=credit_assessment['risk_factors'],
            recommendations=["Approve loan"] if credit_assessment['credit_score'] > 650 else ["Decline loan", "Require additional documentation"],
            audit_trail={"credit_assessment": credit_assessment}
        )
    
    async def _process_risk_assessment(self, request: FinancialAIRequest) -> FinancialAIResponse:
        """Process general risk assessment request."""
        # Determine risk type and assess
        if 'transaction' in request.input_data:
            risk_assessment = self.risk_engine.assess_fraud_risk(request.input_data)
        else:
            risk_assessment = self.risk_engine.assess_credit_risk(request.input_data)
        
        return FinancialAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result={"risk_assessment": risk_assessment},
            confidence_score=0.8,
            risk_score=risk_assessment['risk_score'],
            risk_level=risk_assessment['risk_level'],
            explanation="Comprehensive risk assessment based on available data",
            risk_factors=risk_assessment['risk_factors'],
            audit_trail={"risk_assessment": risk_assessment}
        )
    
    async def _process_aml_screening(self, request: FinancialAIRequest) -> FinancialAIResponse:
        """Process Anti-Money Laundering screening."""
        # Simple AML screening logic
        transaction_amount = request.financial_context.transaction_amount or 0
        
        # Check for suspicious patterns
        suspicious_indicators = []
        risk_score = 0.0
        
        if transaction_amount >= 10000:
            suspicious_indicators.append("Large cash transaction")
            risk_score += 0.3
        
        if 'high_risk_country' in request.input_data and request.input_data['high_risk_country']:
            suspicious_indicators.append("Transaction from high-risk jurisdiction")
            risk_score += 0.4
        
        if 'structured_transactions' in request.input_data and request.input_data['structured_transactions']:
            suspicious_indicators.append("Potential transaction structuring")
            risk_score += 0.5
        
        # Determine if SAR (Suspicious Activity Report) is needed
        requires_sar = risk_score >= 0.6
        
        return FinancialAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result={"requires_sar": requires_sar, "suspicious_indicators": suspicious_indicators},
            confidence_score=0.9,
            risk_score=risk_score,
            risk_level=RiskLevel.HIGH if requires_sar else RiskLevel.LOW,
            explanation="AML screening based on transaction patterns and regulatory requirements",
            alerts=["SAR filing required"] if requires_sar else [],
            recommendations=["File SAR within 30 days", "Enhanced due diligence"] if requires_sar else ["Continue monitoring"],
            audit_trail={"aml_screening": {"risk_score": risk_score, "indicators": suspicious_indicators}}
        )
    
    async def _process_regulatory_compliance(self, request: FinancialAIRequest) -> FinancialAIResponse:
        """Process regulatory compliance check."""
        # Mock compliance response
        compliance_results = self.compliance_engine.check_compliance(request, 
            FinancialAIResponse(
                request_id=request.request_id,
                capability=request.capability,
                primary_result={},
                confidence_score=0.8,
                risk_score=0.3,
                risk_level=RiskLevel.LOW,
                explanation=""
            )
        )
        
        return FinancialAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result=compliance_results,
            confidence_score=0.95,
            risk_score=0.1 if compliance_results['status'] == 'compliant' else 0.8,
            risk_level=RiskLevel.LOW if compliance_results['status'] == 'compliant' else RiskLevel.HIGH,
            explanation="Regulatory compliance assessment based on applicable regulations",
            regulatory_notes=compliance_results.get('warnings', []),
            alerts=compliance_results.get('violations', []),
            audit_trail={"compliance_check": compliance_results}
        )
    
    async def _process_general_financial_query(self, request: FinancialAIRequest) -> FinancialAIResponse:
        """Process general financial query."""
        return FinancialAIResponse(
            request_id=request.request_id,
            capability=request.capability,
            primary_result="General financial analysis provided",
            confidence_score=0.7,
            risk_score=0.3,
            risk_level=RiskLevel.LOW,
            explanation="General financial query processed",
            regulatory_notes=["For informational purposes only - not financial advice"]
        )
    
    async def _log_audit_event(self, event_type: str, data: Any):
        """Log audit event for regulatory compliance."""
        audit_entry = {
            "event_id": str(uuid.uuid4()),
            "event_type": event_type,
            "timestamp": time.time(),
            "data": data if isinstance(data, dict) else {"info": str(data)},
            "user_id": "system",
            "ip_address": "127.0.0.1"
        }
        
        self.audit_logs.append(audit_entry)
        
        # Keep only last 50000 audit entries (7 years of data)
        if len(self.audit_logs) > 50000:
            self.audit_logs = self.audit_logs[-50000:]
    
    def get_financial_status(self) -> Dict[str, Any]:
        """Get financial AI engine status."""
        return {
            "regulatory_compliance_enabled": self.regulatory_compliance_enabled,
            "pii_protection_enabled": self.pii_protection_enabled,
            "audit_all_transactions": self.audit_all_transactions,
            "total_requests_processed": len(self.processing_history),
            "regulatory_rules": len(self.compliance_engine.regulatory_rules),
            "audit_log_entries": len(self.audit_logs),
            "supported_sectors": [s.value for s in FinancialSector],
            "supported_regulations": [r.value for r in FinancialRegulation],
            "supported_capabilities": [c.value for c in FinancialAICapability],
            "recent_processing_stats": {
                "last_hour": len([
                    r for r in self.processing_history 
                    if time.time() - r.timestamp < 3600
                ]),
                "average_confidence": sum(r.confidence_score for r in self.processing_history[-100:]) / min(100, len(self.processing_history)) if self.processing_history else 0.0,
                "average_risk_score": sum(r.risk_score for r in self.processing_history[-100:]) / min(100, len(self.processing_history)) if self.processing_history else 0.0,
                "compliance_rate": len([r for r in self.processing_history[-100:] if r.compliance_status == 'compliant']) / min(100, len(self.processing_history)) if self.processing_history else 1.0
            }
        }


# Global financial AI engine instance
financial_ai_engine = FinancialAIEngine()
