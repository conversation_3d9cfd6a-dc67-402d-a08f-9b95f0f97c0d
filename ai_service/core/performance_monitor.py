"""
Performance monitoring and optimization system.
"""
import asyncio
import logging
import time
import psutil
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque, defaultdict
import threading
from contextlib import asynccontextmanager

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics snapshot."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_connections: int
    request_rate: float
    avg_response_time: float
    error_rate: float


@dataclass
class PerformanceAlert:
    """Performance alert."""
    alert_type: str
    severity: str  # low, medium, high, critical
    message: str
    timestamp: datetime
    metric_value: float
    threshold: float
    resolved: bool = False
    resolved_at: Optional[datetime] = None


@dataclass
class PerformanceThresholds:
    """Performance monitoring thresholds."""
    cpu_warning: float = 70.0
    cpu_critical: float = 90.0
    memory_warning: float = 70.0
    memory_critical: float = 90.0
    disk_warning: float = 80.0
    disk_critical: float = 95.0
    response_time_warning: float = 1000.0  # ms
    response_time_critical: float = 5000.0  # ms
    error_rate_warning: float = 5.0  # %
    error_rate_critical: float = 10.0  # %


class PerformanceOptimizer:
    """Automatic performance optimization."""
    
    def __init__(self):
        self.optimization_enabled = True
        self.optimization_history: List[Dict[str, Any]] = []
        
    async def optimize_memory(self) -> Dict[str, Any]:
        """Optimize memory usage."""
        if not self.optimization_enabled:
            return {"status": "disabled"}
        
        try:
            import gc
            
            # Force garbage collection
            collected = gc.collect()
            
            # Get memory info
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)
            
            optimization = {
                "type": "memory_optimization",
                "timestamp": datetime.utcnow(),
                "garbage_collected": collected,
                "memory_after_mb": memory_mb,
                "status": "completed"
            }
            
            self.optimization_history.append(optimization)
            logger.info(f"Memory optimization completed: collected {collected} objects")
            
            return optimization
            
        except Exception as e:
            logger.error(f"Memory optimization failed: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def optimize_connections(self) -> Dict[str, Any]:
        """Optimize connection pools."""
        try:
            # This would integrate with HTTP client pool
            # For now, just log the optimization attempt
            
            optimization = {
                "type": "connection_optimization",
                "timestamp": datetime.utcnow(),
                "status": "completed",
                "action": "connection_pool_cleanup"
            }
            
            self.optimization_history.append(optimization)
            logger.info("Connection optimization completed")
            
            return optimization
            
        except Exception as e:
            logger.error(f"Connection optimization failed: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def optimize_cache(self) -> Dict[str, Any]:
        """Optimize cache performance."""
        try:
            from ai_service.core.cache_manager import cache_manager
            
            # Get cache stats before optimization
            stats_before = cache_manager.get_cache_stats()
            
            # Perform cache optimization (example: clear old entries)
            # This is a placeholder - real optimization would be more sophisticated
            
            optimization = {
                "type": "cache_optimization",
                "timestamp": datetime.utcnow(),
                "stats_before": stats_before,
                "status": "completed"
            }
            
            self.optimization_history.append(optimization)
            logger.info("Cache optimization completed")
            
            return optimization
            
        except Exception as e:
            logger.error(f"Cache optimization failed: {e}")
            return {"status": "failed", "error": str(e)}


class PerformanceMonitor:
    """Comprehensive performance monitoring system."""
    
    def __init__(self, collection_interval: int = 30):
        self.collection_interval = collection_interval
        self.thresholds = PerformanceThresholds()
        self.optimizer = PerformanceOptimizer()
        
        # Data storage
        self.metrics_history: deque = deque(maxlen=1000)  # Keep last 1000 metrics
        self.alerts: List[PerformanceAlert] = []
        self.active_alerts: Dict[str, PerformanceAlert] = {}
        
        # Monitoring state
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Performance baselines
        self.baseline_metrics: Optional[PerformanceMetrics] = None
        self.baseline_established = False
        
        # Network baseline
        self.last_network_stats = None
        
        logger.info(f"Performance monitor initialized with {collection_interval}s interval")
    
    async def start_monitoring(self):
        """Start performance monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop performance monitoring."""
        self.monitoring_active = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                # Collect metrics
                metrics = await self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Establish baseline if needed
                if not self.baseline_established and len(self.metrics_history) >= 10:
                    await self._establish_baseline()
                
                # Check thresholds and generate alerts
                await self._check_thresholds(metrics)
                
                # Auto-optimization if needed
                await self._auto_optimize(metrics)
                
                # Record metrics for external monitoring
                self._record_metrics(metrics)
                
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Network metrics
        network = psutil.net_io_counters()
        network_sent = network.bytes_sent if network else 0
        network_recv = network.bytes_recv if network else 0
        
        # Application metrics from metrics collector
        app_metrics = metrics_collector.get_metrics_summary()
        
        # Calculate rates
        request_rate = 0.0
        avg_response_time = 0.0
        error_rate = 0.0
        
        if app_metrics["requests"]["recent_5min"] > 0:
            request_rate = app_metrics["requests"]["recent_5min"] / 300  # per second
            avg_response_time = app_metrics["performance"]["avg_response_time_ms"]
            error_rate = app_metrics["requests"]["error_rate_percent"]
        
        return PerformanceMetrics(
            timestamp=datetime.utcnow(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024 * 1024),
            memory_available_mb=memory.available / (1024 * 1024),
            disk_usage_percent=(disk.used / disk.total) * 100,
            network_bytes_sent=network_sent,
            network_bytes_recv=network_recv,
            active_connections=0,  # Would be populated from HTTP client pool
            request_rate=request_rate,
            avg_response_time=avg_response_time,
            error_rate=error_rate
        )
    
    async def _establish_baseline(self):
        """Establish performance baseline."""
        if len(self.metrics_history) < 10:
            return
        
        # Calculate baseline from recent metrics
        recent_metrics = list(self.metrics_history)[-10:]
        
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_response_time = sum(m.avg_response_time for m in recent_metrics) / len(recent_metrics)
        
        self.baseline_metrics = PerformanceMetrics(
            timestamp=datetime.utcnow(),
            cpu_percent=avg_cpu,
            memory_percent=avg_memory,
            memory_used_mb=0,
            memory_available_mb=0,
            disk_usage_percent=0,
            network_bytes_sent=0,
            network_bytes_recv=0,
            active_connections=0,
            request_rate=0,
            avg_response_time=avg_response_time,
            error_rate=0
        )
        
        self.baseline_established = True
        logger.info(f"Performance baseline established: CPU {avg_cpu:.1f}%, Memory {avg_memory:.1f}%, Response Time {avg_response_time:.1f}ms")
    
    async def _check_thresholds(self, metrics: PerformanceMetrics):
        """Check performance thresholds and generate alerts."""
        alerts_to_check = [
            ("cpu_usage", metrics.cpu_percent, self.thresholds.cpu_warning, self.thresholds.cpu_critical),
            ("memory_usage", metrics.memory_percent, self.thresholds.memory_warning, self.thresholds.memory_critical),
            ("disk_usage", metrics.disk_usage_percent, self.thresholds.disk_warning, self.thresholds.disk_critical),
            ("response_time", metrics.avg_response_time, self.thresholds.response_time_warning, self.thresholds.response_time_critical),
            ("error_rate", metrics.error_rate, self.thresholds.error_rate_warning, self.thresholds.error_rate_critical)
        ]
        
        for alert_type, value, warning_threshold, critical_threshold in alerts_to_check:
            # Check for critical alert
            if value >= critical_threshold:
                await self._create_alert(alert_type, "critical", value, critical_threshold, metrics.timestamp)
            # Check for warning alert
            elif value >= warning_threshold:
                await self._create_alert(alert_type, "warning", value, warning_threshold, metrics.timestamp)
            # Check if alert should be resolved
            elif alert_type in self.active_alerts:
                await self._resolve_alert(alert_type, metrics.timestamp)
    
    async def _create_alert(self, alert_type: str, severity: str, value: float, threshold: float, timestamp: datetime):
        """Create performance alert."""
        # Check if alert already exists
        if alert_type in self.active_alerts:
            # Update existing alert
            self.active_alerts[alert_type].metric_value = value
            self.active_alerts[alert_type].timestamp = timestamp
            return
        
        # Create new alert
        alert = PerformanceAlert(
            alert_type=alert_type,
            severity=severity,
            message=f"{alert_type.replace('_', ' ').title()} is {value:.1f} (threshold: {threshold:.1f})",
            timestamp=timestamp,
            metric_value=value,
            threshold=threshold
        )
        
        self.alerts.append(alert)
        self.active_alerts[alert_type] = alert
        
        logger.warning(f"Performance alert: {alert.message}")
        
        # Record alert metric
        metrics_collector.record_counter(
            "performance_alerts_total",
            1.0,
            {"alert_type": alert_type, "severity": severity}
        )
    
    async def _resolve_alert(self, alert_type: str, timestamp: datetime):
        """Resolve performance alert."""
        if alert_type in self.active_alerts:
            alert = self.active_alerts[alert_type]
            alert.resolved = True
            alert.resolved_at = timestamp
            
            del self.active_alerts[alert_type]
            
            logger.info(f"Performance alert resolved: {alert_type}")
    
    async def _auto_optimize(self, metrics: PerformanceMetrics):
        """Perform automatic optimization if needed."""
        # Memory optimization
        if metrics.memory_percent > 85:
            await self.optimizer.optimize_memory()
        
        # Connection optimization
        if metrics.active_connections > 100:  # Example threshold
            await self.optimizer.optimize_connections()
        
        # Cache optimization
        if metrics.avg_response_time > 2000:  # 2 seconds
            await self.optimizer.optimize_cache()
    
    def _record_metrics(self, metrics: PerformanceMetrics):
        """Record metrics for external monitoring."""
        # Record system metrics
        metrics_collector.record_gauge("system_cpu_percent", metrics.cpu_percent)
        metrics_collector.record_gauge("system_memory_percent", metrics.memory_percent)
        metrics_collector.record_gauge("system_disk_percent", metrics.disk_usage_percent)
        
        # Record application metrics
        metrics_collector.record_gauge("app_request_rate", metrics.request_rate)
        metrics_collector.record_gauge("app_avg_response_time_ms", metrics.avg_response_time)
        metrics_collector.record_gauge("app_error_rate_percent", metrics.error_rate)
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """Get current performance metrics."""
        return self.metrics_history[-1] if self.metrics_history else None
    
    def get_metrics_history(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """Get metrics history for specified time period."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_active_alerts(self) -> List[PerformanceAlert]:
        """Get active performance alerts."""
        return list(self.active_alerts.values())
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        current = self.get_current_metrics()
        if not current:
            return {"status": "no_data"}
        
        # Calculate trends
        recent_metrics = self.get_metrics_history(30)  # Last 30 minutes
        
        cpu_trend = "stable"
        memory_trend = "stable"
        response_time_trend = "stable"
        
        if len(recent_metrics) >= 2:
            cpu_change = current.cpu_percent - recent_metrics[0].cpu_percent
            memory_change = current.memory_percent - recent_metrics[0].memory_percent
            response_change = current.avg_response_time - recent_metrics[0].avg_response_time
            
            cpu_trend = "increasing" if cpu_change > 5 else "decreasing" if cpu_change < -5 else "stable"
            memory_trend = "increasing" if memory_change > 5 else "decreasing" if memory_change < -5 else "stable"
            response_time_trend = "increasing" if response_change > 100 else "decreasing" if response_change < -100 else "stable"
        
        return {
            "timestamp": current.timestamp,
            "current_metrics": {
                "cpu_percent": current.cpu_percent,
                "memory_percent": current.memory_percent,
                "disk_percent": current.disk_usage_percent,
                "request_rate": current.request_rate,
                "avg_response_time_ms": current.avg_response_time,
                "error_rate_percent": current.error_rate
            },
            "trends": {
                "cpu": cpu_trend,
                "memory": memory_trend,
                "response_time": response_time_trend
            },
            "active_alerts": len(self.active_alerts),
            "total_alerts": len(self.alerts),
            "baseline_established": self.baseline_established,
            "optimization_history": len(self.optimizer.optimization_history)
        }


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
