"""
Advanced authentication and authorization manager.
"""
import asyncio
import hashlib
import hmac
import jwt
import logging
import secrets
import time
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import bcrypt
from passlib.context import CryptContext

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class AuthMethod(Enum):
    """Authentication methods."""
    API_KEY = "api_key"
    JWT_TOKEN = "jwt_token"
    OAUTH2 = "oauth2"
    BASIC_AUTH = "basic_auth"
    CERTIFICATE = "certificate"
    MULTI_FACTOR = "multi_factor"


class UserRole(Enum):
    """User roles for RBAC."""
    ADMIN = "admin"
    DEVELOPER = "developer"
    USER = "user"
    VIEWER = "viewer"
    SERVICE_ACCOUNT = "service_account"


class Permission(Enum):
    """System permissions."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"
    STREAM = "stream"
    COLLABORATE = "collaborate"
    BATCH = "batch"
    CACHE_MANAGE = "cache_manage"
    METRICS_VIEW = "metrics_view"
    AUDIT_VIEW = "audit_view"


@dataclass
class User:
    """User entity."""
    id: str
    username: str
    email: str
    password_hash: Optional[str] = None
    roles: Set[UserRole] = field(default_factory=set)
    permissions: Set[Permission] = field(default_factory=set)
    api_keys: List[str] = field(default_factory=list)
    tenant_id: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    is_active: bool = True
    is_verified: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class APIKey:
    """API Key entity."""
    key_id: str
    key_hash: str
    user_id: str
    name: str
    permissions: Set[Permission] = field(default_factory=set)
    tenant_id: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    last_used: Optional[datetime] = None
    is_active: bool = True
    usage_count: int = 0
    rate_limit: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AuthSession:
    """Authentication session."""
    session_id: str
    user_id: str
    auth_method: AuthMethod
    created_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: datetime = field(default_factory=lambda: datetime.utcnow() + timedelta(hours=24))
    last_activity: datetime = field(default_factory=datetime.utcnow)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


class AuthenticationManager:
    """Advanced authentication and authorization manager."""
    
    def __init__(self):
        # User and session storage
        self.users: Dict[str, User] = {}
        self.api_keys: Dict[str, APIKey] = {}
        self.sessions: Dict[str, AuthSession] = {}
        
        # Username and email indexes
        self.username_index: Dict[str, str] = {}  # username -> user_id
        self.email_index: Dict[str, str] = {}     # email -> user_id
        
        # Password hashing
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # JWT configuration
        self.jwt_secret = settings.jwt_secret_key or secrets.token_urlsafe(32)
        self.jwt_algorithm = "HS256"
        self.jwt_expiration = timedelta(hours=24)
        
        # Role-based permissions
        self.role_permissions = self._initialize_role_permissions()
        
        # Background cleanup task
        self.cleanup_task: Optional[asyncio.Task] = None
        self._start_background_tasks()
        
        logger.info("Authentication manager initialized")
    
    def _initialize_role_permissions(self) -> Dict[UserRole, Set[Permission]]:
        """Initialize default role permissions."""
        return {
            UserRole.ADMIN: {
                Permission.READ, Permission.WRITE, Permission.DELETE, Permission.ADMIN,
                Permission.STREAM, Permission.COLLABORATE, Permission.BATCH,
                Permission.CACHE_MANAGE, Permission.METRICS_VIEW, Permission.AUDIT_VIEW
            },
            UserRole.DEVELOPER: {
                Permission.READ, Permission.WRITE, Permission.STREAM,
                Permission.COLLABORATE, Permission.BATCH, Permission.METRICS_VIEW
            },
            UserRole.USER: {
                Permission.READ, Permission.WRITE, Permission.STREAM, Permission.COLLABORATE
            },
            UserRole.VIEWER: {
                Permission.READ, Permission.METRICS_VIEW
            },
            UserRole.SERVICE_ACCOUNT: {
                Permission.READ, Permission.WRITE, Permission.STREAM, Permission.BATCH
            }
        }
    
    def _start_background_tasks(self):
        """Start background cleanup tasks."""
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    # User Management
    async def create_user(
        self,
        username: str,
        email: str,
        password: str,
        roles: Optional[Set[UserRole]] = None,
        tenant_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> User:
        """Create a new user."""
        # Check if username or email already exists
        if username in self.username_index:
            raise ValueError(f"Username '{username}' already exists")
        
        if email in self.email_index:
            raise ValueError(f"Email '{email}' already exists")
        
        # Generate user ID
        user_id = secrets.token_urlsafe(16)
        
        # Hash password
        password_hash = self.pwd_context.hash(password)
        
        # Set default role if none provided
        if roles is None:
            roles = {UserRole.USER}
        
        # Calculate permissions from roles
        permissions = set()
        for role in roles:
            permissions.update(self.role_permissions.get(role, set()))
        
        # Create user
        user = User(
            id=user_id,
            username=username,
            email=email,
            password_hash=password_hash,
            roles=roles,
            permissions=permissions,
            tenant_id=tenant_id,
            metadata=metadata or {}
        )
        
        # Store user
        self.users[user_id] = user
        self.username_index[username] = user_id
        self.email_index[email] = user_id
        
        logger.info(f"Created user: {username} ({user_id})")
        
        # Record metrics
        metrics_collector.record_counter("users_created_total", 1.0, {"tenant_id": tenant_id or "default"})
        
        return user
    
    async def authenticate_user(
        self,
        username: str,
        password: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Optional[AuthSession]:
        """Authenticate user with username/password."""
        # Find user
        user_id = self.username_index.get(username)
        if not user_id:
            logger.warning(f"Authentication failed: user not found - {username}")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "user_not_found"})
            return None
        
        user = self.users[user_id]
        
        # Check if user is active
        if not user.is_active:
            logger.warning(f"Authentication failed: user inactive - {username}")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "user_inactive"})
            return None
        
        # Verify password
        if not user.password_hash or not self.pwd_context.verify(password, user.password_hash):
            logger.warning(f"Authentication failed: invalid password - {username}")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "invalid_password"})
            return None
        
        # Create session
        session = await self._create_session(
            user_id=user_id,
            auth_method=AuthMethod.BASIC_AUTH,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        # Update user last login
        user.last_login = datetime.utcnow()
        
        logger.info(f"User authenticated: {username} ({session.session_id})")
        metrics_collector.record_counter("auth_successes_total", 1.0, {"method": "basic_auth"})
        
        return session
    
    # API Key Management
    async def create_api_key(
        self,
        user_id: str,
        name: str,
        permissions: Optional[Set[Permission]] = None,
        expires_at: Optional[datetime] = None,
        rate_limit: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> tuple[str, APIKey]:
        """Create API key for user."""
        if user_id not in self.users:
            raise ValueError(f"User not found: {user_id}")
        
        user = self.users[user_id]
        
        # Generate API key
        api_key = secrets.token_urlsafe(32)
        key_id = secrets.token_urlsafe(16)
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Use user permissions if none specified
        if permissions is None:
            permissions = user.permissions.copy()
        
        # Create API key entity
        api_key_entity = APIKey(
            key_id=key_id,
            key_hash=key_hash,
            user_id=user_id,
            name=name,
            permissions=permissions,
            tenant_id=user.tenant_id,
            expires_at=expires_at,
            rate_limit=rate_limit,
            metadata=metadata or {}
        )
        
        # Store API key
        self.api_keys[key_hash] = api_key_entity
        user.api_keys.append(key_id)
        
        logger.info(f"Created API key: {name} for user {user.username}")
        metrics_collector.record_counter("api_keys_created_total", 1.0, {"user_id": user_id})
        
        return api_key, api_key_entity
    
    async def authenticate_api_key(
        self,
        api_key: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Optional[AuthSession]:
        """Authenticate using API key."""
        # Hash the provided key
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Find API key
        api_key_entity = self.api_keys.get(key_hash)
        if not api_key_entity:
            logger.warning("Authentication failed: invalid API key")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "invalid_api_key"})
            return None
        
        # Check if API key is active
        if not api_key_entity.is_active:
            logger.warning(f"Authentication failed: API key inactive - {api_key_entity.key_id}")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "api_key_inactive"})
            return None
        
        # Check expiration
        if api_key_entity.expires_at and datetime.utcnow() > api_key_entity.expires_at:
            logger.warning(f"Authentication failed: API key expired - {api_key_entity.key_id}")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "api_key_expired"})
            return None
        
        # Check if user exists and is active
        user = self.users.get(api_key_entity.user_id)
        if not user or not user.is_active:
            logger.warning(f"Authentication failed: user inactive for API key - {api_key_entity.key_id}")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "user_inactive"})
            return None
        
        # Update API key usage
        api_key_entity.last_used = datetime.utcnow()
        api_key_entity.usage_count += 1
        
        # Create session
        session = await self._create_session(
            user_id=api_key_entity.user_id,
            auth_method=AuthMethod.API_KEY,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata={"api_key_id": api_key_entity.key_id}
        )
        
        logger.info(f"API key authenticated: {api_key_entity.name} ({session.session_id})")
        metrics_collector.record_counter("auth_successes_total", 1.0, {"method": "api_key"})
        
        return session
    
    # JWT Token Management
    async def create_jwt_token(
        self,
        user_id: str,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create JWT token for user."""
        if user_id not in self.users:
            raise ValueError(f"User not found: {user_id}")
        
        user = self.users[user_id]
        
        # Create JWT payload
        now = datetime.utcnow()
        payload = {
            "sub": user_id,
            "username": user.username,
            "email": user.email,
            "roles": [role.value for role in user.roles],
            "permissions": [perm.value for perm in user.permissions],
            "tenant_id": user.tenant_id,
            "iat": now,
            "exp": now + self.jwt_expiration,
            "iss": "ai-service",
            "aud": "ai-service-client"
        }
        
        # Add additional claims
        if additional_claims:
            payload.update(additional_claims)
        
        # Generate token
        token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
        
        logger.info(f"Created JWT token for user: {user.username}")
        metrics_collector.record_counter("jwt_tokens_created_total", 1.0, {"user_id": user_id})
        
        return token
    
    async def authenticate_jwt_token(
        self,
        token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Optional[AuthSession]:
        """Authenticate using JWT token."""
        try:
            # Decode and verify token
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            user_id = payload.get("sub")
            if not user_id:
                logger.warning("JWT authentication failed: missing subject")
                metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "jwt_invalid"})
                return None
            
            # Check if user exists and is active
            user = self.users.get(user_id)
            if not user or not user.is_active:
                logger.warning(f"JWT authentication failed: user inactive - {user_id}")
                metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "user_inactive"})
                return None
            
            # Create session
            session = await self._create_session(
                user_id=user_id,
                auth_method=AuthMethod.JWT_TOKEN,
                ip_address=ip_address,
                user_agent=user_agent,
                metadata={"jwt_payload": payload}
            )
            
            logger.info(f"JWT token authenticated: {user.username} ({session.session_id})")
            metrics_collector.record_counter("auth_successes_total", 1.0, {"method": "jwt_token"})
            
            return session
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT authentication failed: token expired")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "jwt_expired"})
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"JWT authentication failed: invalid token - {e}")
            metrics_collector.record_counter("auth_failures_total", 1.0, {"reason": "jwt_invalid"})
            return None
    
    # Session Management
    async def _create_session(
        self,
        user_id: str,
        auth_method: AuthMethod,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AuthSession:
        """Create authentication session."""
        session_id = secrets.token_urlsafe(32)
        
        session = AuthSession(
            session_id=session_id,
            user_id=user_id,
            auth_method=auth_method,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata=metadata or {}
        )
        
        self.sessions[session_id] = session
        
        return session
    
    async def get_session(self, session_id: str) -> Optional[AuthSession]:
        """Get authentication session."""
        session = self.sessions.get(session_id)
        
        if session and session.is_active:
            # Check expiration
            if datetime.utcnow() > session.expires_at:
                session.is_active = False
                return None
            
            # Update last activity
            session.last_activity = datetime.utcnow()
            return session
        
        return None
    
    async def revoke_session(self, session_id: str) -> bool:
        """Revoke authentication session."""
        session = self.sessions.get(session_id)
        if session:
            session.is_active = False
            logger.info(f"Session revoked: {session_id}")
            return True
        return False
    
    # Authorization
    async def check_permission(
        self,
        user_id: str,
        permission: Permission,
        resource: Optional[str] = None
    ) -> bool:
        """Check if user has permission."""
        user = self.users.get(user_id)
        if not user or not user.is_active:
            return False
        
        # Check if user has the permission
        has_permission = permission in user.permissions
        
        # Log permission check
        logger.debug(f"Permission check: {user.username} -> {permission.value} = {has_permission}")
        
        return has_permission
    
    async def check_tenant_access(
        self,
        user_id: str,
        tenant_id: str
    ) -> bool:
        """Check if user has access to tenant."""
        user = self.users.get(user_id)
        if not user or not user.is_active:
            return False
        
        # Admin users have access to all tenants
        if UserRole.ADMIN in user.roles:
            return True
        
        # Check if user belongs to the tenant
        return user.tenant_id == tenant_id
    
    def get_auth_stats(self) -> Dict[str, Any]:
        """Get authentication statistics."""
        active_sessions = len([s for s in self.sessions.values() if s.is_active])
        active_api_keys = len([k for k in self.api_keys.values() if k.is_active])
        
        return {
            "total_users": len(self.users),
            "active_users": len([u for u in self.users.values() if u.is_active]),
            "total_sessions": len(self.sessions),
            "active_sessions": active_sessions,
            "total_api_keys": len(self.api_keys),
            "active_api_keys": active_api_keys,
            "auth_methods": {
                method.value: len([s for s in self.sessions.values() 
                                if s.auth_method == method and s.is_active])
                for method in AuthMethod
            }
        }
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
                current_time = datetime.utcnow()
                
                # Clean up expired sessions
                expired_sessions = [
                    sid for sid, session in self.sessions.items()
                    if current_time > session.expires_at
                ]
                
                for session_id in expired_sessions:
                    del self.sessions[session_id]
                    logger.debug(f"Cleaned up expired session: {session_id}")
                
                # Clean up expired API keys
                expired_keys = [
                    key_hash for key_hash, api_key in self.api_keys.items()
                    if api_key.expires_at and current_time > api_key.expires_at
                ]
                
                for key_hash in expired_keys:
                    api_key = self.api_keys[key_hash]
                    api_key.is_active = False
                    logger.debug(f"Deactivated expired API key: {api_key.key_id}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
    
    async def shutdown(self):
        """Shutdown authentication manager."""
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        logger.info("Authentication manager shutdown completed")


# Global authentication manager instance
auth_manager = AuthenticationManager()
