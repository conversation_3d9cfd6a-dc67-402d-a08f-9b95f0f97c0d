"""
Security enhancements for production deployment.
"""
import hashlib
import hmac
import time
import logging
import secrets
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict
import re
import ipaddress
from fastapi import HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt

from ai_service.core.config import settings

logger = logging.getLogger(__name__)

# Import new security components
try:
    from ai_service.core.auth_manager import auth_manager, Permission
    from ai_service.core.rate_limiter import rate_limiter
    from ai_service.core.audit_logger import audit_logger, AuditEventType, AuditLevel
    from ai_service.core.tenant_manager import tenant_manager
    from ai_service.core.monitoring_system import monitoring_system
    ADVANCED_SECURITY_AVAILABLE = True
except ImportError:
    ADVANCED_SECURITY_AVAILABLE = False
    logger.warning("Advanced security components not available, using basic security")


@dataclass
class SecurityEvent:
    """Security event for audit logging."""
    event_type: str
    timestamp: datetime
    client_ip: str
    user_agent: str
    details: Dict[str, Any]
    severity: str = "info"  # info, warning, critical


@dataclass
class RateLimitRule:
    """Rate limiting rule."""
    requests_per_minute: int
    requests_per_hour: int
    burst_limit: int


class SecurityManager:
    """Comprehensive security management system."""
    
    def __init__(self):
        # Rate limiting
        self.rate_limit_storage: Dict[str, List[datetime]] = defaultdict(list)
        self.blocked_ips: Set[str] = set()
        self.suspicious_ips: Set[str] = set()
        
        # API key management
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        self.revoked_keys: Set[str] = set()
        
        # Security events
        self.security_events: List[SecurityEvent] = []
        
        # Default rate limits
        self.default_rate_limits = RateLimitRule(
            requests_per_minute=60,
            requests_per_hour=1000,
            burst_limit=10
        )
        
        # Input validation patterns
        self.dangerous_patterns = [
            r'<script[^>]*>.*?</script>',  # XSS
            r'javascript:',  # JavaScript injection
            r'data:text/html',  # Data URI XSS
            r'vbscript:',  # VBScript injection
            r'onload\s*=',  # Event handler injection
            r'onerror\s*=',  # Error handler injection
            r'eval\s*\(',  # Code evaluation
            r'exec\s*\(',  # Code execution
            r'system\s*\(',  # System command
            r'import\s+os',  # OS module import
            r'__import__',  # Dynamic import
        ]
        
        logger.info("Security manager initialized")
    
    def validate_api_key(self, api_key: str) -> Dict[str, Any]:
        """Validate API key and return key info.
        
        Args:
            api_key: API key to validate
            
        Returns:
            API key information
            
        Raises:
            HTTPException: If key is invalid or revoked
        """
        if not api_key:
            raise HTTPException(status_code=401, detail="API key required")
        
        # Check if key is revoked
        if api_key in self.revoked_keys:
            self.log_security_event(
                "api_key_revoked_access_attempt",
                {"api_key_hash": self._hash_api_key(api_key)},
                severity="warning"
            )
            raise HTTPException(status_code=401, detail="API key revoked")
        
        # For demo purposes, accept any key that starts with 'sk-'
        # In production, implement proper key validation
        if not api_key.startswith('sk-'):
            raise HTTPException(status_code=401, detail="Invalid API key format")
        
        # Return key info (mock implementation)
        return {
            "key_id": self._hash_api_key(api_key)[:8],
            "created_at": datetime.utcnow(),
            "last_used": datetime.utcnow(),
            "rate_limits": self.default_rate_limits.__dict__
        }
    
    def check_rate_limit(self, client_id: str, endpoint: str = "default") -> bool:
        """Check if client is within rate limits.
        
        Args:
            client_id: Client identifier (IP, API key, etc.)
            endpoint: Endpoint being accessed
            
        Returns:
            True if within limits, False otherwise
        """
        now = datetime.utcnow()
        key = f"{client_id}:{endpoint}"
        
        # Clean old entries
        self.rate_limit_storage[key] = [
            timestamp for timestamp in self.rate_limit_storage[key]
            if now - timestamp < timedelta(hours=1)
        ]
        
        # Check limits
        recent_requests = self.rate_limit_storage[key]
        
        # Check per-minute limit
        minute_ago = now - timedelta(minutes=1)
        recent_minute = [t for t in recent_requests if t >= minute_ago]
        
        if len(recent_minute) >= self.default_rate_limits.requests_per_minute:
            self.log_security_event(
                "rate_limit_exceeded",
                {
                    "client_id": client_id,
                    "endpoint": endpoint,
                    "requests_in_minute": len(recent_minute),
                    "limit": self.default_rate_limits.requests_per_minute
                },
                severity="warning"
            )
            return False
        
        # Check per-hour limit
        if len(recent_requests) >= self.default_rate_limits.requests_per_hour:
            self.log_security_event(
                "hourly_rate_limit_exceeded",
                {
                    "client_id": client_id,
                    "endpoint": endpoint,
                    "requests_in_hour": len(recent_requests),
                    "limit": self.default_rate_limits.requests_per_hour
                },
                severity="warning"
            )
            return False
        
        # Record this request
        self.rate_limit_storage[key].append(now)
        return True
    
    def validate_input(self, input_text: str, max_length: int = 10000) -> str:
        """Validate and sanitize input text.
        
        Args:
            input_text: Input text to validate
            max_length: Maximum allowed length
            
        Returns:
            Sanitized input text
            
        Raises:
            HTTPException: If input is invalid or dangerous
        """
        if not input_text:
            return input_text
        
        # Check length
        if len(input_text) > max_length:
            raise HTTPException(
                status_code=400,
                detail=f"Input too long. Maximum {max_length} characters allowed."
            )
        
        # Check for dangerous patterns
        for pattern in self.dangerous_patterns:
            if re.search(pattern, input_text, re.IGNORECASE):
                self.log_security_event(
                    "dangerous_input_detected",
                    {
                        "pattern": pattern,
                        "input_length": len(input_text),
                        "input_preview": input_text[:100]
                    },
                    severity="critical"
                )
                raise HTTPException(
                    status_code=400,
                    detail="Input contains potentially dangerous content"
                )
        
        # Basic sanitization
        sanitized = input_text.strip()
        
        # Remove null bytes
        sanitized = sanitized.replace('\x00', '')
        
        return sanitized
    
    def check_ip_reputation(self, client_ip: str) -> bool:
        """Check IP reputation.
        
        Args:
            client_ip: Client IP address
            
        Returns:
            True if IP is allowed, False if blocked
        """
        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            self.log_security_event(
                "blocked_ip_access_attempt",
                {"client_ip": client_ip},
                severity="warning"
            )
            return False
        
        # Check if IP is suspicious
        if client_ip in self.suspicious_ips:
            self.log_security_event(
                "suspicious_ip_access",
                {"client_ip": client_ip},
                severity="info"
            )
        
        return True
    
    def log_security_event(
        self,
        event_type: str,
        details: Dict[str, Any],
        severity: str = "info",
        client_ip: str = "unknown",
        user_agent: str = "unknown"
    ):
        """Log security event.
        
        Args:
            event_type: Type of security event
            details: Event details
            severity: Event severity (info, warning, critical)
            client_ip: Client IP address
            user_agent: User agent string
        """
        event = SecurityEvent(
            event_type=event_type,
            timestamp=datetime.utcnow(),
            client_ip=client_ip,
            user_agent=user_agent,
            details=details,
            severity=severity
        )
        
        self.security_events.append(event)
        
        # Keep only recent events (last 1000)
        if len(self.security_events) > 1000:
            self.security_events = self.security_events[-1000:]
        
        # Log to standard logger
        log_message = f"Security event: {event_type} from {client_ip} - {details}"
        
        if severity == "critical":
            logger.critical(log_message)
        elif severity == "warning":
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security summary.
        
        Returns:
            Security summary information
        """
        now = datetime.utcnow()
        recent_cutoff = now - timedelta(hours=24)
        
        recent_events = [
            event for event in self.security_events
            if event.timestamp >= recent_cutoff
        ]
        
        # Count events by type and severity
        event_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        
        for event in recent_events:
            event_counts[event.event_type] += 1
            severity_counts[event.severity] += 1
        
        return {
            "timestamp": now,
            "blocked_ips": len(self.blocked_ips),
            "suspicious_ips": len(self.suspicious_ips),
            "total_security_events": len(self.security_events),
            "recent_events_24h": len(recent_events),
            "event_counts": dict(event_counts),
            "severity_counts": dict(severity_counts),
            "rate_limit_clients": len(self.rate_limit_storage),
            "revoked_api_keys": len(self.revoked_keys)
        }
    
    def _hash_api_key(self, api_key: str) -> str:
        """Hash API key for logging/storage.
        
        Args:
            api_key: API key to hash
            
        Returns:
            Hashed API key
        """
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    def block_ip(self, ip_address: str, reason: str = "Manual block"):
        """Block an IP address.
        
        Args:
            ip_address: IP address to block
            reason: Reason for blocking
        """
        self.blocked_ips.add(ip_address)
        self.log_security_event(
            "ip_blocked",
            {"ip_address": ip_address, "reason": reason},
            severity="warning"
        )
        logger.warning(f"IP {ip_address} blocked: {reason}")
    
    def unblock_ip(self, ip_address: str):
        """Unblock an IP address.
        
        Args:
            ip_address: IP address to unblock
        """
        self.blocked_ips.discard(ip_address)
        self.log_security_event(
            "ip_unblocked",
            {"ip_address": ip_address},
            severity="info"
        )
        logger.info(f"IP {ip_address} unblocked")
    
    def revoke_api_key(self, api_key: str, reason: str = "Manual revocation"):
        """Revoke an API key.
        
        Args:
            api_key: API key to revoke
            reason: Reason for revocation
        """
        self.revoked_keys.add(api_key)
        self.log_security_event(
            "api_key_revoked",
            {
                "api_key_hash": self._hash_api_key(api_key),
                "reason": reason
            },
            severity="warning"
        )
        logger.warning(f"API key revoked: {reason}")


class SecurityMiddleware:
    """Enhanced security middleware for FastAPI."""

    def __init__(self, security_manager: SecurityManager):
        self.security_manager = security_manager

    async def __call__(self, request: Request, call_next):
        """Process request through enhanced security middleware."""
        start_time = time.time()

        # Get client info
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "unknown")

        # Extract authentication info
        auth_info = await self._extract_auth_info(request)
        user_id = auth_info.get("user_id")
        tenant_id = auth_info.get("tenant_id")
        session_id = auth_info.get("session_id")

        try:
            # Enhanced security checks if available
            if ADVANCED_SECURITY_AVAILABLE:
                await self._advanced_security_checks(
                    request, client_ip, user_id, tenant_id
                )
            else:
                # Fallback to basic security
                await self._basic_security_checks(request, client_ip)

            # Process request
            response = await call_next(request)

            # Log successful request
            await self._log_request_success(
                request, response, client_ip, user_agent,
                user_id, tenant_id, session_id, start_time
            )

            return response

        except HTTPException as e:
            # Log security-related HTTP exceptions
            await self._log_request_blocked(
                request, e, client_ip, user_agent,
                user_id, tenant_id, session_id
            )
            raise
        except Exception as e:
            # Log unexpected errors
            await self._log_request_error(
                request, e, client_ip, user_agent,
                user_id, tenant_id, session_id
            )
            raise

    async def _extract_auth_info(self, request: Request) -> Dict[str, Any]:
        """Extract authentication information from request."""
        auth_info = {}

        if not ADVANCED_SECURITY_AVAILABLE:
            return auth_info

        try:
            # Check for API key
            api_key = request.headers.get("x-api-key")
            if api_key:
                session = await auth_manager.authenticate_api_key(
                    api_key,
                    ip_address=self._get_client_ip(request),
                    user_agent=request.headers.get("user-agent")
                )
                if session:
                    user = await auth_manager.users.get(session.user_id)
                    if user:
                        auth_info.update({
                            "user_id": session.user_id,
                            "tenant_id": user.tenant_id,
                            "session_id": session.session_id,
                            "auth_method": "api_key"
                        })

            # Check for JWT token
            elif "authorization" in request.headers:
                auth_header = request.headers["authorization"]
                if auth_header.startswith("Bearer "):
                    token = auth_header[7:]
                    session = await auth_manager.authenticate_jwt_token(
                        token,
                        ip_address=self._get_client_ip(request),
                        user_agent=request.headers.get("user-agent")
                    )
                    if session:
                        user = await auth_manager.users.get(session.user_id)
                        if user:
                            auth_info.update({
                                "user_id": session.user_id,
                                "tenant_id": user.tenant_id,
                                "session_id": session.session_id,
                                "auth_method": "jwt_token"
                            })

        except Exception as e:
            logger.debug(f"Auth extraction failed: {e}")

        return auth_info

    async def _advanced_security_checks(
        self,
        request: Request,
        client_ip: str,
        user_id: Optional[str],
        tenant_id: Optional[str]
    ):
        """Perform advanced security checks."""
        # Check IP reputation
        if not self.security_manager.check_ip_reputation(client_ip):
            raise HTTPException(status_code=403, detail="IP address blocked")

        # Advanced rate limiting
        identifier = user_id or client_ip
        identifier_type = "user_id" if user_id else "ip_address"

        is_allowed, statuses = await rate_limiter.check_rate_limit(
            identifier=identifier,
            identifier_type=identifier_type,
            request_size=1
        )

        if not is_allowed:
            # Log rate limit violation
            await audit_logger.log_event(
                event_type=AuditEventType.SECURITY_EVENT,
                message="Rate limit exceeded",
                level=AuditLevel.WARNING,
                user_id=user_id,
                tenant_id=tenant_id,
                ip_address=client_ip,
                resource=str(request.url.path),
                action="rate_limit_check",
                outcome="failure",
                details={"rate_limit_statuses": [s.__dict__ for s in statuses]}
            )
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        # Tenant access check
        if tenant_id:
            tenant = await tenant_manager.get_tenant(tenant_id)
            if not tenant or not tenant.is_active():
                await audit_logger.log_event(
                    event_type=AuditEventType.SECURITY_EVENT,
                    message="Inactive tenant access attempt",
                    level=AuditLevel.WARNING,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    ip_address=client_ip,
                    resource=str(request.url.path),
                    action="tenant_access_check",
                    outcome="failure"
                )
                raise HTTPException(status_code=403, detail="Tenant access denied")

        # Record metrics
        await monitoring_system.record_metric(
            "security_checks_total",
            1.0,
            tags={"result": "passed", "identifier_type": identifier_type}
        )

    async def _basic_security_checks(self, request: Request, client_ip: str):
        """Perform basic security checks."""
        # Check IP reputation
        if not self.security_manager.check_ip_reputation(client_ip):
            raise HTTPException(status_code=403, detail="IP address blocked")

        # Check rate limits
        if not self.security_manager.check_rate_limit(client_ip, str(request.url.path)):
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

    async def _log_request_success(
        self,
        request: Request,
        response,
        client_ip: str,
        user_agent: str,
        user_id: Optional[str],
        tenant_id: Optional[str],
        session_id: Optional[str],
        start_time: float
    ):
        """Log successful request."""
        duration_ms = (time.time() - start_time) * 1000

        # Basic logging
        self.security_manager.log_security_event(
            "request_processed",
            {
                "method": request.method,
                "path": str(request.url.path),
                "status_code": response.status_code,
                "duration_ms": duration_ms,
                "user_id": user_id,
                "tenant_id": tenant_id
            },
            client_ip=client_ip,
            user_agent=user_agent
        )

        # Advanced audit logging
        if ADVANCED_SECURITY_AVAILABLE:
            await audit_logger.log_event(
                event_type=AuditEventType.API_ACCESS,
                message=f"API request processed: {request.method} {request.url.path}",
                level=AuditLevel.INFO,
                user_id=user_id,
                session_id=session_id,
                tenant_id=tenant_id,
                ip_address=client_ip,
                user_agent=user_agent,
                resource=str(request.url.path),
                action=request.method.lower(),
                outcome="success",
                details={
                    "status_code": response.status_code,
                    "duration_ms": duration_ms
                }
            )

            # Record performance metrics
            await monitoring_system.record_metric(
                "api_request_duration_ms",
                duration_ms,
                tags={
                    "method": request.method,
                    "path": str(request.url.path),
                    "status_code": str(response.status_code)
                }
            )

    async def _log_request_blocked(
        self,
        request: Request,
        exception: HTTPException,
        client_ip: str,
        user_agent: str,
        user_id: Optional[str],
        tenant_id: Optional[str],
        session_id: Optional[str]
    ):
        """Log blocked request."""
        # Basic logging
        self.security_manager.log_security_event(
            "request_blocked",
            {
                "method": request.method,
                "path": str(request.url.path),
                "status_code": exception.status_code,
                "detail": exception.detail,
                "user_id": user_id,
                "tenant_id": tenant_id
            },
            severity="warning",
            client_ip=client_ip,
            user_agent=user_agent
        )

        # Advanced audit logging
        if ADVANCED_SECURITY_AVAILABLE:
            await audit_logger.log_event(
                event_type=AuditEventType.SECURITY_EVENT,
                message=f"API request blocked: {request.method} {request.url.path}",
                level=AuditLevel.WARNING,
                user_id=user_id,
                session_id=session_id,
                tenant_id=tenant_id,
                ip_address=client_ip,
                user_agent=user_agent,
                resource=str(request.url.path),
                action=request.method.lower(),
                outcome="failure",
                details={
                    "status_code": exception.status_code,
                    "detail": str(exception.detail)
                }
            )

    async def _log_request_error(
        self,
        request: Request,
        exception: Exception,
        client_ip: str,
        user_agent: str,
        user_id: Optional[str],
        tenant_id: Optional[str],
        session_id: Optional[str]
    ):
        """Log request error."""
        # Basic logging
        self.security_manager.log_security_event(
            "request_error",
            {
                "method": request.method,
                "path": str(request.url.path),
                "error": str(exception),
                "user_id": user_id,
                "tenant_id": tenant_id
            },
            severity="critical",
            client_ip=client_ip,
            user_agent=user_agent
        )

        # Advanced audit logging
        if ADVANCED_SECURITY_AVAILABLE:
            await audit_logger.log_event(
                event_type=AuditEventType.ERROR_EVENT,
                message=f"API request error: {request.method} {request.url.path}",
                level=AuditLevel.ERROR,
                user_id=user_id,
                session_id=session_id,
                tenant_id=tenant_id,
                ip_address=client_ip,
                user_agent=user_agent,
                resource=str(request.url.path),
                action=request.method.lower(),
                outcome="error",
                details={
                    "error": str(exception),
                    "error_type": type(exception).__name__
                }
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (load balancer/proxy)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"


# Global security manager instance
security_manager = SecurityManager()
