"""
Advanced monitoring and alerting system.
"""
import asyncio
import logging
import smtplib
import time
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json
import aiohttp

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class MetricType(Enum):
    """Metric types for monitoring."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class NotificationChannel(Enum):
    """Notification channels."""
    EMAIL = "email"
    WEBHOOK = "webhook"
    SLACK = "slack"
    TEAMS = "teams"
    SMS = "sms"
    CONSOLE = "console"


@dataclass
class MetricThreshold:
    """Metric threshold configuration."""
    metric_name: str
    operator: str  # >, <, >=, <=, ==, !=
    value: float
    duration_seconds: int = 300  # 5 minutes
    severity: AlertSeverity = AlertSeverity.WARNING


@dataclass
class AlertRule:
    """Alert rule configuration."""
    id: str
    name: str
    description: str
    thresholds: List[MetricThreshold]
    notification_channels: List[NotificationChannel]
    is_enabled: bool = True
    cooldown_seconds: int = 3600  # 1 hour
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Alert:
    """Alert instance."""
    id: str
    rule_id: str
    name: str
    description: str
    severity: AlertSeverity
    status: AlertStatus = AlertStatus.ACTIVE
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    metric_values: Dict[str, float] = field(default_factory=dict)
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_active(self) -> bool:
        """Check if alert is active."""
        return self.status == AlertStatus.ACTIVE


@dataclass
class NotificationConfig:
    """Notification configuration."""
    channel: NotificationChannel
    config: Dict[str, Any]
    is_enabled: bool = True


class MetricCollector:
    """Enhanced metric collection system."""
    
    def __init__(self):
        self.metrics: Dict[str, List[Dict[str, Any]]] = {}
        self.metric_metadata: Dict[str, Dict[str, Any]] = {}
        self.retention_seconds = 86400  # 24 hours
        self.lock = asyncio.Lock()
    
    async def record_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType = MetricType.GAUGE,
        tags: Optional[Dict[str, str]] = None,
        timestamp: Optional[datetime] = None
    ):
        """Record a metric value."""
        async with self.lock:
            if name not in self.metrics:
                self.metrics[name] = []
                self.metric_metadata[name] = {
                    "type": metric_type.value,
                    "created_at": datetime.utcnow()
                }
            
            metric_point = {
                "value": value,
                "timestamp": timestamp or datetime.utcnow(),
                "tags": tags or {}
            }
            
            self.metrics[name].append(metric_point)
            
            # Clean old metrics
            cutoff_time = datetime.utcnow() - timedelta(seconds=self.retention_seconds)
            self.metrics[name] = [
                m for m in self.metrics[name] 
                if m["timestamp"] > cutoff_time
            ]
    
    async def get_metric_value(
        self,
        name: str,
        aggregation: str = "latest",  # latest, avg, sum, min, max
        duration_seconds: Optional[int] = None
    ) -> Optional[float]:
        """Get metric value with aggregation."""
        async with self.lock:
            if name not in self.metrics or not self.metrics[name]:
                return None
            
            metrics_data = self.metrics[name]
            
            # Filter by duration if specified
            if duration_seconds:
                cutoff_time = datetime.utcnow() - timedelta(seconds=duration_seconds)
                metrics_data = [
                    m for m in metrics_data 
                    if m["timestamp"] > cutoff_time
                ]
            
            if not metrics_data:
                return None
            
            values = [m["value"] for m in metrics_data]
            
            if aggregation == "latest":
                return values[-1]
            elif aggregation == "avg":
                return sum(values) / len(values)
            elif aggregation == "sum":
                return sum(values)
            elif aggregation == "min":
                return min(values)
            elif aggregation == "max":
                return max(values)
            else:
                return values[-1]
    
    def get_all_metrics(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all metrics."""
        return self.metrics.copy()


class NotificationManager:
    """Notification delivery system."""
    
    def __init__(self):
        self.configs: Dict[NotificationChannel, NotificationConfig] = {}
        self.notification_history: List[Dict[str, Any]] = []
        
        # Initialize default configurations
        self._initialize_default_configs()
    
    def _initialize_default_configs(self):
        """Initialize default notification configurations."""
        # Console notification (always enabled)
        self.configs[NotificationChannel.CONSOLE] = NotificationConfig(
            channel=NotificationChannel.CONSOLE,
            config={},
            is_enabled=True
        )
        
        # Email configuration
        if hasattr(settings, 'smtp_server') and settings.smtp_server:
            self.configs[NotificationChannel.EMAIL] = NotificationConfig(
                channel=NotificationChannel.EMAIL,
                config={
                    "smtp_server": getattr(settings, 'smtp_server', ''),
                    "smtp_port": getattr(settings, 'smtp_port', 587),
                    "smtp_username": getattr(settings, 'smtp_username', ''),
                    "smtp_password": getattr(settings, 'smtp_password', ''),
                    "from_email": getattr(settings, 'from_email', ''),
                    "to_emails": getattr(settings, 'alert_emails', [])
                },
                is_enabled=True
            )
    
    async def send_notification(
        self,
        alert: Alert,
        channels: List[NotificationChannel]
    ) -> Dict[NotificationChannel, bool]:
        """Send notification through specified channels."""
        results = {}
        
        for channel in channels:
            if channel not in self.configs or not self.configs[channel].is_enabled:
                results[channel] = False
                continue
            
            try:
                if channel == NotificationChannel.CONSOLE:
                    success = await self._send_console_notification(alert)
                elif channel == NotificationChannel.EMAIL:
                    success = await self._send_email_notification(alert)
                elif channel == NotificationChannel.WEBHOOK:
                    success = await self._send_webhook_notification(alert)
                elif channel == NotificationChannel.SLACK:
                    success = await self._send_slack_notification(alert)
                else:
                    success = False
                
                results[channel] = success
                
                # Record notification history
                self.notification_history.append({
                    "alert_id": alert.id,
                    "channel": channel.value,
                    "success": success,
                    "timestamp": datetime.utcnow(),
                    "severity": alert.severity.value
                })
                
            except Exception as e:
                logger.error(f"Failed to send notification via {channel.value}: {e}")
                results[channel] = False
        
        return results
    
    async def _send_console_notification(self, alert: Alert) -> bool:
        """Send console notification."""
        severity_emoji = {
            AlertSeverity.INFO: "ℹ️",
            AlertSeverity.WARNING: "⚠️",
            AlertSeverity.ERROR: "❌",
            AlertSeverity.CRITICAL: "🚨"
        }
        
        emoji = severity_emoji.get(alert.severity, "📢")
        
        message = f"{emoji} ALERT [{alert.severity.value.upper()}] {alert.name}\n"
        message += f"Description: {alert.description}\n"
        message += f"Created: {alert.created_at}\n"
        message += f"Metric Values: {alert.metric_values}\n"
        
        if alert.tags:
            message += f"Tags: {alert.tags}\n"
        
        print(message)
        logger.warning(message)
        
        return True
    
    async def _send_email_notification(self, alert: Alert) -> bool:
        """Send email notification."""
        config = self.configs[NotificationChannel.EMAIL].config
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = config['from_email']
            msg['To'] = ', '.join(config['to_emails'])
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.name}"
            
            # Create email body
            body = f"""
Alert: {alert.name}
Severity: {alert.severity.value.upper()}
Description: {alert.description}
Created: {alert.created_at}
Status: {alert.status.value}

Metric Values:
{json.dumps(alert.metric_values, indent=2)}

Tags:
{json.dumps(alert.tags, indent=2)}

Alert ID: {alert.id}
Rule ID: {alert.rule_id}
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            server.starttls()
            server.login(config['smtp_username'], config['smtp_password'])
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return False
    
    async def _send_webhook_notification(self, alert: Alert) -> bool:
        """Send webhook notification."""
        config = self.configs[NotificationChannel.WEBHOOK].config
        webhook_url = config.get('url')
        
        if not webhook_url:
            return False
        
        try:
            payload = {
                "alert_id": alert.id,
                "rule_id": alert.rule_id,
                "name": alert.name,
                "description": alert.description,
                "severity": alert.severity.value,
                "status": alert.status.value,
                "created_at": alert.created_at.isoformat(),
                "metric_values": alert.metric_values,
                "tags": alert.tags
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    return response.status < 400
                    
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
            return False
    
    async def _send_slack_notification(self, alert: Alert) -> bool:
        """Send Slack notification."""
        config = self.configs[NotificationChannel.SLACK].config
        webhook_url = config.get('webhook_url')
        
        if not webhook_url:
            return False
        
        try:
            # Color coding for severity
            color_map = {
                AlertSeverity.INFO: "#36a64f",      # Green
                AlertSeverity.WARNING: "#ff9500",   # Orange
                AlertSeverity.ERROR: "#ff0000",     # Red
                AlertSeverity.CRITICAL: "#8B0000"   # Dark Red
            }
            
            payload = {
                "attachments": [{
                    "color": color_map.get(alert.severity, "#36a64f"),
                    "title": f"Alert: {alert.name}",
                    "text": alert.description,
                    "fields": [
                        {
                            "title": "Severity",
                            "value": alert.severity.value.upper(),
                            "short": True
                        },
                        {
                            "title": "Status",
                            "value": alert.status.value,
                            "short": True
                        },
                        {
                            "title": "Created",
                            "value": alert.created_at.strftime("%Y-%m-%d %H:%M:%S UTC"),
                            "short": True
                        },
                        {
                            "title": "Alert ID",
                            "value": alert.id,
                            "short": True
                        }
                    ],
                    "footer": "AI Service Monitoring",
                    "ts": int(alert.created_at.timestamp())
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    return response.status < 400
                    
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")
            return False


class MonitoringSystem:
    """Advanced monitoring and alerting system."""
    
    def __init__(self):
        # Core components
        self.metric_collector = MetricCollector()
        self.notification_manager = NotificationManager()
        
        # Alert management
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        
        # Rule evaluation
        self.rule_last_triggered: Dict[str, datetime] = {}
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.total_alerts_generated = 0
        self.alerts_by_severity = {severity: 0 for severity in AlertSeverity}
        
        # Initialize default rules
        self._initialize_default_rules()
        
        # Start monitoring
        self._start_monitoring()
        
        logger.info("Monitoring system initialized")
    
    def _initialize_default_rules(self):
        """Initialize default monitoring rules."""
        # High error rate alert
        error_rate_rule = AlertRule(
            id="high_error_rate",
            name="High Error Rate",
            description="Error rate is above acceptable threshold",
            thresholds=[
                MetricThreshold(
                    metric_name="error_rate_percent",
                    operator=">=",
                    value=5.0,
                    duration_seconds=300,
                    severity=AlertSeverity.WARNING
                ),
                MetricThreshold(
                    metric_name="error_rate_percent",
                    operator=">=",
                    value=10.0,
                    duration_seconds=300,
                    severity=AlertSeverity.CRITICAL
                )
            ],
            notification_channels=[NotificationChannel.CONSOLE, NotificationChannel.EMAIL]
        )
        
        # High response time alert
        response_time_rule = AlertRule(
            id="high_response_time",
            name="High Response Time",
            description="Average response time is above acceptable threshold",
            thresholds=[
                MetricThreshold(
                    metric_name="avg_response_time_ms",
                    operator=">=",
                    value=2000.0,
                    duration_seconds=300,
                    severity=AlertSeverity.WARNING
                ),
                MetricThreshold(
                    metric_name="avg_response_time_ms",
                    operator=">=",
                    value=5000.0,
                    duration_seconds=300,
                    severity=AlertSeverity.CRITICAL
                )
            ],
            notification_channels=[NotificationChannel.CONSOLE, NotificationChannel.EMAIL]
        )
        
        # High CPU usage alert
        cpu_usage_rule = AlertRule(
            id="high_cpu_usage",
            name="High CPU Usage",
            description="CPU usage is above acceptable threshold",
            thresholds=[
                MetricThreshold(
                    metric_name="system_cpu_percent",
                    operator=">=",
                    value=80.0,
                    duration_seconds=300,
                    severity=AlertSeverity.WARNING
                ),
                MetricThreshold(
                    metric_name="system_cpu_percent",
                    operator=">=",
                    value=95.0,
                    duration_seconds=300,
                    severity=AlertSeverity.CRITICAL
                )
            ],
            notification_channels=[NotificationChannel.CONSOLE]
        )
        
        # Add rules
        self.alert_rules[error_rate_rule.id] = error_rate_rule
        self.alert_rules[response_time_rule.id] = response_time_rule
        self.alert_rules[cpu_usage_rule.id] = cpu_usage_rule
    
    def _start_monitoring(self):
        """Start monitoring tasks."""
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def add_alert_rule(self, rule: AlertRule):
        """Add custom alert rule."""
        self.alert_rules[rule.id] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    async def remove_alert_rule(self, rule_id: str) -> bool:
        """Remove alert rule."""
        if rule_id in self.alert_rules:
            del self.alert_rules[rule_id]
            logger.info(f"Removed alert rule: {rule_id}")
            return True
        return False
    
    async def record_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType = MetricType.GAUGE,
        tags: Optional[Dict[str, str]] = None
    ):
        """Record metric for monitoring."""
        await self.metric_collector.record_metric(name, value, metric_type, tags)
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                # Evaluate all alert rules
                for rule in self.alert_rules.values():
                    if rule.is_enabled:
                        await self._evaluate_rule(rule)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
    
    async def _evaluate_rule(self, rule: AlertRule):
        """Evaluate alert rule against current metrics."""
        try:
            # Check cooldown
            last_triggered = self.rule_last_triggered.get(rule.id)
            if last_triggered:
                cooldown_end = last_triggered + timedelta(seconds=rule.cooldown_seconds)
                if datetime.utcnow() < cooldown_end:
                    return
            
            # Evaluate thresholds
            triggered_thresholds = []
            metric_values = {}
            
            for threshold in rule.thresholds:
                metric_value = await self.metric_collector.get_metric_value(
                    threshold.metric_name,
                    aggregation="avg",
                    duration_seconds=threshold.duration_seconds
                )
                
                if metric_value is not None:
                    metric_values[threshold.metric_name] = metric_value
                    
                    # Check threshold
                    if self._check_threshold(metric_value, threshold):
                        triggered_thresholds.append(threshold)
            
            # Create alert if thresholds are triggered
            if triggered_thresholds:
                # Find highest severity
                max_severity = max(t.severity for t in triggered_thresholds)
                
                # Create alert
                alert = Alert(
                    id=f"alert_{rule.id}_{int(time.time())}",
                    rule_id=rule.id,
                    name=rule.name,
                    description=rule.description,
                    severity=max_severity,
                    metric_values=metric_values,
                    tags=rule.tags.copy()
                )
                
                # Store alert
                self.active_alerts[alert.id] = alert
                self.alert_history.append(alert)
                
                # Update statistics
                self.total_alerts_generated += 1
                self.alerts_by_severity[max_severity] += 1
                
                # Update last triggered time
                self.rule_last_triggered[rule.id] = datetime.utcnow()
                
                # Send notifications
                await self.notification_manager.send_notification(
                    alert, rule.notification_channels
                )
                
                logger.warning(f"Alert triggered: {alert.name} ({alert.severity.value})")
                
                # Record metrics
                metrics_collector.record_counter(
                    "alerts_generated_total",
                    1.0,
                    {"rule_id": rule.id, "severity": max_severity.value}
                )
                
        except Exception as e:
            logger.error(f"Error evaluating rule {rule.id}: {e}")
    
    def _check_threshold(self, value: float, threshold: MetricThreshold) -> bool:
        """Check if metric value exceeds threshold."""
        if threshold.operator == ">":
            return value > threshold.value
        elif threshold.operator == "<":
            return value < threshold.value
        elif threshold.operator == ">=":
            return value >= threshold.value
        elif threshold.operator == "<=":
            return value <= threshold.value
        elif threshold.operator == "==":
            return value == threshold.value
        elif threshold.operator == "!=":
            return value != threshold.value
        else:
            return False
    
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """Acknowledge an alert."""
        alert = self.active_alerts.get(alert_id)
        if alert and alert.status == AlertStatus.ACTIVE:
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.utcnow()
            alert.acknowledged_by = acknowledged_by
            alert.updated_at = datetime.utcnow()
            
            logger.info(f"Alert acknowledged: {alert_id} by {acknowledged_by}")
            return True
        
        return False
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an alert."""
        alert = self.active_alerts.get(alert_id)
        if alert and alert.status in [AlertStatus.ACTIVE, AlertStatus.ACKNOWLEDGED]:
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.utcnow()
            alert.updated_at = datetime.utcnow()
            
            # Remove from active alerts
            del self.active_alerts[alert_id]
            
            logger.info(f"Alert resolved: {alert_id}")
            return True
        
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts."""
        return list(self.active_alerts.values())
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get monitoring statistics."""
        return {
            "total_alerts_generated": self.total_alerts_generated,
            "active_alerts": len(self.active_alerts),
            "alerts_by_severity": {
                severity.value: count 
                for severity, count in self.alerts_by_severity.items()
            },
            "alert_rules": len(self.alert_rules),
            "enabled_rules": len([r for r in self.alert_rules.values() if r.is_enabled]),
            "notification_channels": len(self.notification_manager.configs),
            "metrics_tracked": len(self.metric_collector.metrics)
        }
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(3600)  # Cleanup every hour
                
                # Clean up old alert history (keep last 1000)
                if len(self.alert_history) > 1000:
                    self.alert_history = self.alert_history[-1000:]
                
                # Clean up old notification history
                if len(self.notification_manager.notification_history) > 1000:
                    self.notification_manager.notification_history = \
                        self.notification_manager.notification_history[-1000:]
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring cleanup error: {e}")
    
    async def shutdown(self):
        """Shutdown monitoring system."""
        # Cancel background tasks
        if self.monitoring_task:
            self.monitoring_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        logger.info("Monitoring system shutdown completed")


# Global monitoring system instance
monitoring_system = MonitoringSystem()
