"""
Semantic Caching Engine for Phase 8.
Provides intelligent caching based on semantic similarity across providers.
"""

import asyncio
import logging
import time
import json
import hashlib
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import pickle
from collections import defaultdict
import statistics

logger = logging.getLogger(__name__)


class SimilarityMetric(Enum):
    """Similarity calculation methods."""
    COSINE = "cosine"
    EUCLIDEAN = "euclidean"
    JACCARD = "jaccard"
    SEMANTIC_HASH = "semantic_hash"
    EMBEDDING = "embedding"


class CacheStrategy(Enum):
    """Cache management strategies."""
    LRU = "lru"                    # Least Recently Used
    LFU = "lfu"                    # Least Frequently Used
    TTL = "ttl"                    # Time To Live
    SEMANTIC_LRU = "semantic_lru"  # Semantic-aware LRU
    ADAPTIVE = "adaptive"          # Adaptive based on usage patterns


@dataclass
class SemanticCacheEntry:
    """Semantic cache entry."""
    cache_key: str
    original_prompt: str
    prompt_embedding: Optional[List[float]]
    
    # Cached response
    response_content: Any
    response_metadata: Dict[str, Any]
    
    # Provider information
    provider: str
    model_id: str
    
    # Cache metadata
    created_at: float
    last_accessed: float
    access_count: int
    ttl: Optional[float]
    
    # Quality metrics
    quality_score: float
    confidence: float
    user_rating: Optional[float] = None
    
    # Semantic information
    semantic_hash: str = ""
    topic_tags: List[str] = field(default_factory=list)
    complexity_score: float = 0.5


@dataclass
class CacheHit:
    """Cache hit result."""
    entry: SemanticCacheEntry
    similarity_score: float
    similarity_metric: SimilarityMetric
    cache_age: float
    is_exact_match: bool = False


@dataclass
class CacheStats:
    """Cache performance statistics."""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    semantic_hits: int = 0
    exact_hits: int = 0
    
    # Performance metrics
    avg_similarity_threshold: float = 0.8
    avg_response_time_saved: float = 0.0
    total_cost_saved: float = 0.0
    
    # Storage metrics
    total_entries: int = 0
    storage_size_mb: float = 0.0
    avg_entry_size_kb: float = 0.0
    
    # Quality metrics
    avg_quality_score: float = 0.0
    user_satisfaction: float = 0.0


class SimpleEmbeddingGenerator:
    """Simple embedding generator for semantic similarity."""
    
    def __init__(self):
        # Simple word-based embedding (in production, use proper embedding models)
        self.vocab: Dict[str, int] = {}
        self.vocab_size = 1000
        self.embedding_dim = 128
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate simple embedding for text."""
        words = text.lower().split()
        
        # Build vocabulary
        for word in words:
            if word not in self.vocab and len(self.vocab) < self.vocab_size:
                self.vocab[word] = len(self.vocab)
        
        # Create simple bag-of-words embedding
        embedding = [0.0] * self.embedding_dim
        
        for word in words:
            if word in self.vocab:
                idx = self.vocab[word] % self.embedding_dim
                embedding[idx] += 1.0
        
        # Normalize
        magnitude = sum(x * x for x in embedding) ** 0.5
        if magnitude > 0:
            embedding = [x / magnitude for x in embedding]
        
        return embedding
    
    def calculate_similarity(self, emb1: List[float], emb2: List[float]) -> float:
        """Calculate cosine similarity between embeddings."""
        if len(emb1) != len(emb2):
            return 0.0
        
        dot_product = sum(a * b for a, b in zip(emb1, emb2))
        magnitude1 = sum(a * a for a in emb1) ** 0.5
        magnitude2 = sum(b * b for b in emb2) ** 0.5
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)


class SemanticCachingEngine:
    """Semantic caching engine with cross-provider support."""
    
    def __init__(self):
        self.cache_entries: Dict[str, SemanticCacheEntry] = {}
        self.embedding_generator = SimpleEmbeddingGenerator()
        self.cache_stats = CacheStats()
        
        # Configuration
        self.similarity_threshold = 0.8
        self.max_cache_size = 10000
        self.default_ttl = 3600 * 24  # 24 hours
        self.cache_strategy = CacheStrategy.SEMANTIC_LRU
        
        # Semantic indexing
        self.semantic_index: Dict[str, List[str]] = defaultdict(list)  # topic -> cache_keys
        self.similarity_cache: Dict[Tuple[str, str], float] = {}
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._optimization_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("Semantic Caching Engine initialized")
    
    async def start(self):
        """Start semantic caching engine."""
        if self._running:
            return
        
        self._running = True
        
        # Start background tasks
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._optimization_task = asyncio.create_task(self._optimization_loop())
        
        logger.info("Semantic Caching Engine started")
    
    async def stop(self):
        """Stop semantic caching engine."""
        self._running = False
        
        # Cancel background tasks
        if self._cleanup_task:
            self._cleanup_task.cancel()
        if self._optimization_task:
            self._optimization_task.cancel()
        
        # Wait for tasks to complete
        tasks = [self._cleanup_task, self._optimization_task]
        for task in tasks:
            if task:
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("Semantic Caching Engine stopped")
    
    async def get_cached_response(
        self,
        prompt: str,
        provider: Optional[str] = None,
        model_id: Optional[str] = None,
        similarity_threshold: Optional[float] = None
    ) -> Optional[CacheHit]:
        """Get cached response for prompt."""
        self.cache_stats.total_requests += 1
        
        # Generate embedding for prompt
        prompt_embedding = self.embedding_generator.generate_embedding(prompt)
        
        # Check for exact match first
        exact_key = self._generate_cache_key(prompt, provider, model_id)
        if exact_key in self.cache_entries:
            entry = self.cache_entries[exact_key]
            
            # Check TTL
            if self._is_entry_valid(entry):
                entry.last_accessed = time.time()
                entry.access_count += 1
                
                self.cache_stats.cache_hits += 1
                self.cache_stats.exact_hits += 1
                
                return CacheHit(
                    entry=entry,
                    similarity_score=1.0,
                    similarity_metric=SimilarityMetric.SEMANTIC_HASH,
                    cache_age=time.time() - entry.created_at,
                    is_exact_match=True
                )
        
        # Semantic similarity search
        threshold = similarity_threshold or self.similarity_threshold
        best_match = await self._find_semantic_match(prompt, prompt_embedding, threshold)
        
        if best_match:
            entry = best_match.entry
            entry.last_accessed = time.time()
            entry.access_count += 1
            
            self.cache_stats.cache_hits += 1
            self.cache_stats.semantic_hits += 1
            
            return best_match
        
        self.cache_stats.cache_misses += 1
        return None
    
    async def cache_response(
        self,
        prompt: str,
        response_content: Any,
        response_metadata: Dict[str, Any],
        provider: str,
        model_id: str,
        quality_score: float = 0.8,
        confidence: float = 0.8,
        ttl: Optional[float] = None
    ) -> str:
        """Cache a response."""
        # Generate cache key
        cache_key = self._generate_cache_key(prompt, provider, model_id)
        
        # Generate embedding
        prompt_embedding = self.embedding_generator.generate_embedding(prompt)
        
        # Create cache entry
        entry = SemanticCacheEntry(
            cache_key=cache_key,
            original_prompt=prompt,
            prompt_embedding=prompt_embedding,
            response_content=response_content,
            response_metadata=response_metadata,
            provider=provider,
            model_id=model_id,
            created_at=time.time(),
            last_accessed=time.time(),
            access_count=1,
            ttl=ttl or self.default_ttl,
            quality_score=quality_score,
            confidence=confidence,
            semantic_hash=self._generate_semantic_hash(prompt),
            topic_tags=self._extract_topic_tags(prompt),
            complexity_score=self._calculate_complexity_score(prompt)
        )
        
        # Check cache size and evict if necessary
        if len(self.cache_entries) >= self.max_cache_size:
            await self._evict_entries()
        
        # Store entry
        self.cache_entries[cache_key] = entry
        
        # Update semantic index
        for tag in entry.topic_tags:
            self.semantic_index[tag].append(cache_key)
        
        # Update stats
        self.cache_stats.total_entries = len(self.cache_entries)
        self._update_storage_stats()
        
        logger.debug(f"Cached response for prompt: {prompt[:50]}...")
        return cache_key
    
    async def _find_semantic_match(
        self,
        prompt: str,
        prompt_embedding: List[float],
        threshold: float
    ) -> Optional[CacheHit]:
        """Find semantically similar cached entry."""
        best_match = None
        best_similarity = 0.0
        
        # Extract topic tags for filtering
        prompt_tags = self._extract_topic_tags(prompt)
        
        # Get candidate entries from semantic index
        candidate_keys = set()
        for tag in prompt_tags:
            candidate_keys.update(self.semantic_index.get(tag, []))
        
        # If no topic matches, check all entries (expensive)
        if not candidate_keys:
            candidate_keys = set(self.cache_entries.keys())
        
        # Limit candidates for performance
        candidate_keys = list(candidate_keys)[:1000]
        
        for cache_key in candidate_keys:
            if cache_key not in self.cache_entries:
                continue
            
            entry = self.cache_entries[cache_key]
            
            # Check if entry is valid
            if not self._is_entry_valid(entry):
                continue
            
            # Calculate similarity
            if entry.prompt_embedding:
                similarity = self.embedding_generator.calculate_similarity(
                    prompt_embedding, entry.prompt_embedding
                )
            else:
                # Fallback to text similarity
                similarity = self._calculate_text_similarity(prompt, entry.original_prompt)
            
            if similarity > threshold and similarity > best_similarity:
                best_similarity = similarity
                best_match = CacheHit(
                    entry=entry,
                    similarity_score=similarity,
                    similarity_metric=SimilarityMetric.EMBEDDING,
                    cache_age=time.time() - entry.created_at
                )
        
        return best_match
    
    def _generate_cache_key(self, prompt: str, provider: Optional[str], model_id: Optional[str]) -> str:
        """Generate cache key for prompt."""
        key_components = [prompt]
        if provider:
            key_components.append(provider)
        if model_id:
            key_components.append(model_id)
        
        key_string = "|".join(key_components)
        return hashlib.sha256(key_string.encode()).hexdigest()
    
    def _generate_semantic_hash(self, text: str) -> str:
        """Generate semantic hash for text."""
        # Simple semantic hash based on key words
        words = text.lower().split()
        key_words = [w for w in words if len(w) > 3]  # Filter short words
        key_words.sort()  # Sort for consistency
        
        semantic_string = " ".join(key_words[:10])  # Use top 10 words
        return hashlib.md5(semantic_string.encode()).hexdigest()
    
    def _extract_topic_tags(self, text: str) -> List[str]:
        """Extract topic tags from text."""
        # Simple keyword extraction
        words = text.lower().split()
        
        # Common topic indicators
        topic_words = [
            w for w in words 
            if len(w) > 4 and w.isalpha()
        ]
        
        # Return top 5 topic words
        return topic_words[:5]
    
    def _calculate_complexity_score(self, text: str) -> float:
        """Calculate complexity score for text."""
        words = text.split()
        sentences = text.split('.')
        
        if not words:
            return 0.0
        
        # Simple complexity metrics
        avg_word_length = statistics.mean([len(w) for w in words])
        sentence_count = len([s for s in sentences if s.strip()])
        words_per_sentence = len(words) / max(1, sentence_count)
        
        # Normalize to 0-1 scale
        complexity = min(1.0, (avg_word_length / 10 + words_per_sentence / 20) / 2)
        return complexity
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate simple text similarity."""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _is_entry_valid(self, entry: SemanticCacheEntry) -> bool:
        """Check if cache entry is still valid."""
        if entry.ttl:
            return time.time() - entry.created_at < entry.ttl
        return True
    
    async def _evict_entries(self):
        """Evict cache entries based on strategy."""
        if self.cache_strategy == CacheStrategy.LRU:
            # Remove least recently used
            oldest_entry = min(
                self.cache_entries.values(),
                key=lambda x: x.last_accessed
            )
            await self._remove_entry(oldest_entry.cache_key)
            
        elif self.cache_strategy == CacheStrategy.LFU:
            # Remove least frequently used
            least_used = min(
                self.cache_entries.values(),
                key=lambda x: x.access_count
            )
            await self._remove_entry(least_used.cache_key)
            
        elif self.cache_strategy == CacheStrategy.SEMANTIC_LRU:
            # Remove semantically similar entries that are old
            await self._semantic_eviction()
            
        else:
            # Default to LRU
            oldest_entry = min(
                self.cache_entries.values(),
                key=lambda x: x.last_accessed
            )
            await self._remove_entry(oldest_entry.cache_key)
    
    async def _semantic_eviction(self):
        """Evict entries using semantic similarity."""
        # Group similar entries and keep the best one from each group
        similarity_groups = []
        processed_keys = set()
        
        for key1, entry1 in self.cache_entries.items():
            if key1 in processed_keys:
                continue
            
            group = [key1]
            processed_keys.add(key1)
            
            for key2, entry2 in self.cache_entries.items():
                if key2 in processed_keys:
                    continue
                
                if entry1.prompt_embedding and entry2.prompt_embedding:
                    similarity = self.embedding_generator.calculate_similarity(
                        entry1.prompt_embedding, entry2.prompt_embedding
                    )
                    
                    if similarity > 0.9:  # Very similar
                        group.append(key2)
                        processed_keys.add(key2)
            
            if len(group) > 1:
                similarity_groups.append(group)
        
        # Remove lower quality entries from each group
        for group in similarity_groups:
            if len(group) > 1:
                # Keep the highest quality entry
                best_entry = max(
                    [self.cache_entries[key] for key in group],
                    key=lambda x: x.quality_score * x.confidence
                )
                
                for key in group:
                    if key != best_entry.cache_key:
                        await self._remove_entry(key)
                        break  # Remove one entry per eviction call
    
    async def _remove_entry(self, cache_key: str):
        """Remove cache entry."""
        if cache_key in self.cache_entries:
            entry = self.cache_entries[cache_key]
            
            # Remove from semantic index
            for tag in entry.topic_tags:
                if tag in self.semantic_index:
                    self.semantic_index[tag] = [
                        k for k in self.semantic_index[tag] if k != cache_key
                    ]
            
            # Remove entry
            del self.cache_entries[cache_key]
            
            # Update stats
            self.cache_stats.total_entries = len(self.cache_entries)
            self._update_storage_stats()
    
    def _update_storage_stats(self):
        """Update storage statistics."""
        if self.cache_entries:
            # Estimate storage size (simplified)
            total_size = 0
            for entry in self.cache_entries.values():
                entry_size = len(str(entry.response_content)) + len(entry.original_prompt)
                if entry.prompt_embedding:
                    entry_size += len(entry.prompt_embedding) * 4  # 4 bytes per float
                total_size += entry_size
            
            self.cache_stats.storage_size_mb = total_size / (1024 * 1024)
            self.cache_stats.avg_entry_size_kb = (total_size / len(self.cache_entries)) / 1024
            
            # Update quality stats
            self.cache_stats.avg_quality_score = statistics.mean([
                entry.quality_score for entry in self.cache_entries.values()
            ])
    
    async def _cleanup_loop(self):
        """Background cleanup of expired entries."""
        while self._running:
            try:
                await self._cleanup_expired_entries()
                await asyncio.sleep(3600)  # Run every hour
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(3600)
    
    async def _cleanup_expired_entries(self):
        """Clean up expired cache entries."""
        current_time = time.time()
        expired_keys = []
        
        for key, entry in self.cache_entries.items():
            if entry.ttl and (current_time - entry.created_at) > entry.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            await self._remove_entry(key)
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    async def _optimization_loop(self):
        """Background optimization of cache performance."""
        while self._running:
            try:
                await self._optimize_cache()
                await asyncio.sleep(1800)  # Run every 30 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
                await asyncio.sleep(1800)
    
    async def _optimize_cache(self):
        """Optimize cache performance."""
        # Adjust similarity threshold based on hit rate
        if self.cache_stats.total_requests > 100:
            hit_rate = self.cache_stats.cache_hits / self.cache_stats.total_requests
            
            if hit_rate < 0.3:  # Low hit rate, lower threshold
                self.similarity_threshold = max(0.6, self.similarity_threshold - 0.05)
            elif hit_rate > 0.7:  # High hit rate, raise threshold
                self.similarity_threshold = min(0.9, self.similarity_threshold + 0.02)
        
        # Clean up semantic index
        empty_tags = [tag for tag, keys in self.semantic_index.items() if not keys]
        for tag in empty_tags:
            del self.semantic_index[tag]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        hit_rate = (
            self.cache_stats.cache_hits / self.cache_stats.total_requests
            if self.cache_stats.total_requests > 0 else 0.0
        )
        
        semantic_hit_rate = (
            self.cache_stats.semantic_hits / self.cache_stats.total_requests
            if self.cache_stats.total_requests > 0 else 0.0
        )
        
        return {
            "total_requests": self.cache_stats.total_requests,
            "cache_hits": self.cache_stats.cache_hits,
            "cache_misses": self.cache_stats.cache_misses,
            "hit_rate": hit_rate,
            "semantic_hit_rate": semantic_hit_rate,
            "exact_hits": self.cache_stats.exact_hits,
            "total_entries": self.cache_stats.total_entries,
            "storage_size_mb": self.cache_stats.storage_size_mb,
            "avg_entry_size_kb": self.cache_stats.avg_entry_size_kb,
            "avg_quality_score": self.cache_stats.avg_quality_score,
            "similarity_threshold": self.similarity_threshold,
            "cache_strategy": self.cache_strategy.value,
            "semantic_index_size": len(self.semantic_index)
        }
    
    async def invalidate_cache(self, pattern: Optional[str] = None):
        """Invalidate cache entries."""
        if pattern:
            # Invalidate entries matching pattern
            keys_to_remove = [
                key for key, entry in self.cache_entries.items()
                if pattern in entry.original_prompt
            ]
        else:
            # Invalidate all entries
            keys_to_remove = list(self.cache_entries.keys())
        
        for key in keys_to_remove:
            await self._remove_entry(key)
        
        logger.info(f"Invalidated {len(keys_to_remove)} cache entries")


# Global semantic caching engine instance
semantic_caching_engine = SemanticCachingEngine()
