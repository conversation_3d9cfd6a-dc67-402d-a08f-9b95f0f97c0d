"""
Context Caching utilities for Google Gemini API.
"""
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import google.generativeai as genai
from google.generativeai import caching

from ai_service.models.schemas import (
    CacheContent, CacheCreateRequest, CacheUpdateRequest, 
    CacheInfo, CacheResponse, CacheListResponse
)
from ai_service.core.exceptions import (
    AIServiceException, ValidationError, APIError, ErrorCode
)

logger = logging.getLogger(__name__)


class ContextCacheManager:
    """Manager for Google Gemini Context Caching operations."""
    
    def __init__(self):
        self.cache_registry: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = 3600  # 1 hour
        self.min_tokens = {
            "gemini-2.5-flash": 1024,
            "gemini-2.5-pro": 2048
        }
    
    def _validate_cache_request(self, request: CacheCreateRequest) -> None:
        """Validate cache creation request.
        
        Args:
            request: Cache creation request
            
        Raises:
            ValidationError: If request is invalid
        """
        # Validate model
        if not request.model:
            raise ValidationError("Model is required for caching", field="model")
        
        # Validate contents
        if not request.contents:
            raise ValidationError("Contents are required for caching", field="contents")
        
        # Validate TTL
        if request.ttl and request.ttl < 60:
            raise ValidationError("TTL must be at least 60 seconds", field="ttl")
        
        # Estimate token count (basic validation)
        total_content_length = 0
        for content in request.contents:
            for part in content.parts:
                if "text" in part:
                    total_content_length += len(part["text"])
        
        # Basic token estimation (rough: 1 token ≈ 4 characters)
        estimated_tokens = total_content_length // 4
        min_tokens_required = self.min_tokens.get(request.model, 1024)
        
        if estimated_tokens < min_tokens_required:
            raise ValidationError(
                f"Content too small for caching. Estimated {estimated_tokens} tokens, "
                f"minimum {min_tokens_required} required for {request.model}",
                field="contents"
            )
    
    def _convert_to_gemini_contents(self, contents: List[CacheContent]) -> List[Dict[str, Any]]:
        """Convert CacheContent to Gemini API format.
        
        Args:
            contents: List of cache contents
            
        Returns:
            List of contents in Gemini API format
        """
        gemini_contents = []
        
        for content in contents:
            gemini_content = {
                "role": content.role,
                "parts": content.parts
            }
            gemini_contents.append(gemini_content)
        
        return gemini_contents
    
    def _convert_from_gemini_cache(self, gemini_cache: Any) -> CacheInfo:
        """Convert Gemini cache object to CacheInfo.
        
        Args:
            gemini_cache: Gemini cache object
            
        Returns:
            CacheInfo object
        """
        # Extract cache information
        cache_name = gemini_cache.name
        model = getattr(gemini_cache, 'model', 'unknown')
        display_name = getattr(gemini_cache, 'display_name', None)
        
        # Extract usage metadata
        usage_metadata = {}
        if hasattr(gemini_cache, 'usage_metadata'):
            usage_metadata = {
                "total_token_count": getattr(gemini_cache.usage_metadata, 'total_token_count', 0),
                "cached_content_token_count": getattr(gemini_cache.usage_metadata, 'cached_content_token_count', 0)
            }
        
        # Extract timestamps
        create_time = getattr(gemini_cache, 'create_time', datetime.now())
        update_time = getattr(gemini_cache, 'update_time', datetime.now())
        expire_time = getattr(gemini_cache, 'expire_time', datetime.now() + timedelta(hours=1))
        
        # Convert to datetime if they're strings
        if isinstance(create_time, str):
            create_time = datetime.fromisoformat(create_time.replace('Z', '+00:00'))
        if isinstance(update_time, str):
            update_time = datetime.fromisoformat(update_time.replace('Z', '+00:00'))
        if isinstance(expire_time, str):
            expire_time = datetime.fromisoformat(expire_time.replace('Z', '+00:00'))
        
        return CacheInfo(
            name=cache_name,
            model=model,
            display_name=display_name,
            usage_metadata=usage_metadata,
            create_time=create_time,
            update_time=update_time,
            expire_time=expire_time
        )
    
    async def create_cache(self, request: CacheCreateRequest) -> CacheResponse:
        """Create a new context cache.
        
        Args:
            request: Cache creation request
            
        Returns:
            Cache response with created cache info
            
        Raises:
            AIServiceException: If cache creation fails
        """
        try:
            # Validate request
            self._validate_cache_request(request)
            
            # Convert contents to Gemini format
            gemini_contents = self._convert_to_gemini_contents(request.contents)
            
            # Create TTL timedelta
            ttl_seconds = request.ttl or self.default_ttl
            ttl = timedelta(seconds=ttl_seconds)
            
            logger.info(f"Creating cache for model {request.model} with TTL {ttl_seconds}s")
            
            # Create cache using Gemini API
            cache_params = {
                "model": request.model,
                "contents": gemini_contents,
                "ttl": ttl
            }
            
            if request.system_instruction:
                cache_params["system_instruction"] = request.system_instruction
            
            if request.display_name:
                cache_params["display_name"] = request.display_name
            
            # Create the cache
            gemini_cache = caching.CachedContent.create(**cache_params)
            
            # Convert to our format
            cache_info = self._convert_from_gemini_cache(gemini_cache)
            
            # Register in local registry
            self.cache_registry[cache_info.name] = {
                "cache_info": cache_info,
                "gemini_cache": gemini_cache,
                "created_at": datetime.now()
            }
            
            logger.info(f"Cache created successfully: {cache_info.name}")
            
            return CacheResponse(
                cache=cache_info,
                success=True,
                message="Cache created successfully"
            )
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to create cache: {e}")
            raise APIError(
                message=f"Failed to create cache: {str(e)}",
                error_code=ErrorCode.EXTERNAL_API_ERROR,
                details={"error": str(e)}
            )
    
    async def get_cache(self, cache_name: str) -> CacheResponse:
        """Get cache information.
        
        Args:
            cache_name: Name of the cache
            
        Returns:
            Cache response with cache info
            
        Raises:
            AIServiceException: If cache not found or retrieval fails
        """
        try:
            # Check local registry first
            if cache_name in self.cache_registry:
                cache_info = self.cache_registry[cache_name]["cache_info"]
                
                return CacheResponse(
                    cache=cache_info,
                    success=True,
                    message="Cache retrieved from registry"
                )
            
            # Get from Gemini API
            gemini_cache = caching.CachedContent.get(cache_name)
            
            # Convert to our format
            cache_info = self._convert_from_gemini_cache(gemini_cache)
            
            # Update registry
            self.cache_registry[cache_name] = {
                "cache_info": cache_info,
                "gemini_cache": gemini_cache,
                "retrieved_at": datetime.now()
            }
            
            return CacheResponse(
                cache=cache_info,
                success=True,
                message="Cache retrieved successfully"
            )
            
        except Exception as e:
            logger.error(f"Failed to get cache {cache_name}: {e}")
            raise APIError(
                message=f"Failed to get cache: {str(e)}",
                error_code=ErrorCode.EXTERNAL_API_ERROR,
                details={"cache_name": cache_name, "error": str(e)}
            )
    
    async def list_caches(self, page_size: int = 10, page_token: Optional[str] = None) -> CacheListResponse:
        """List all context caches.
        
        Args:
            page_size: Number of caches per page
            page_token: Token for pagination
            
        Returns:
            List of caches
            
        Raises:
            AIServiceException: If listing fails
        """
        try:
            # List caches from Gemini API
            caches_list = caching.CachedContent.list()
            
            # Convert to our format
            cache_infos = []
            for gemini_cache in caches_list:
                cache_info = self._convert_from_gemini_cache(gemini_cache)
                cache_infos.append(cache_info)
                
                # Update registry
                self.cache_registry[cache_info.name] = {
                    "cache_info": cache_info,
                    "gemini_cache": gemini_cache,
                    "listed_at": datetime.now()
                }
            
            return CacheListResponse(
                caches=cache_infos,
                total_count=len(cache_infos),
                next_page_token=None  # Gemini API doesn't support pagination yet
            )
            
        except Exception as e:
            logger.error(f"Failed to list caches: {e}")
            raise APIError(
                message=f"Failed to list caches: {str(e)}",
                error_code=ErrorCode.EXTERNAL_API_ERROR,
                details={"error": str(e)}
            )


    async def update_cache(self, cache_name: str, request: CacheUpdateRequest) -> CacheResponse:
        """Update an existing context cache.

        Args:
            cache_name: Name of the cache to update
            request: Cache update request

        Returns:
            Cache response with updated cache info

        Raises:
            AIServiceException: If cache update fails
        """
        try:
            # Get existing cache
            gemini_cache = caching.CachedContent.get(cache_name)

            # Update parameters
            update_params = {}

            if request.ttl is not None:
                if request.ttl < 60:
                    raise ValidationError("TTL must be at least 60 seconds", field="ttl")
                update_params["ttl"] = timedelta(seconds=request.ttl)

            if request.display_name is not None:
                update_params["display_name"] = request.display_name

            if not update_params:
                raise ValidationError("At least one field must be provided for update")

            logger.info(f"Updating cache {cache_name} with params: {list(update_params.keys())}")

            # Update the cache
            updated_cache = gemini_cache.update(**update_params)

            # Convert to our format
            cache_info = self._convert_from_gemini_cache(updated_cache)

            # Update registry
            self.cache_registry[cache_name] = {
                "cache_info": cache_info,
                "gemini_cache": updated_cache,
                "updated_at": datetime.now()
            }

            logger.info(f"Cache updated successfully: {cache_name}")

            return CacheResponse(
                cache=cache_info,
                success=True,
                message="Cache updated successfully"
            )

        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to update cache {cache_name}: {e}")
            raise APIError(
                message=f"Failed to update cache: {str(e)}",
                error_code=ErrorCode.EXTERNAL_API_ERROR,
                details={"cache_name": cache_name, "error": str(e)}
            )

    async def delete_cache(self, cache_name: str) -> Dict[str, Any]:
        """Delete a context cache.

        Args:
            cache_name: Name of the cache to delete

        Returns:
            Deletion result

        Raises:
            AIServiceException: If cache deletion fails
        """
        try:
            # Get cache first to ensure it exists
            gemini_cache = caching.CachedContent.get(cache_name)

            logger.info(f"Deleting cache: {cache_name}")

            # Delete the cache
            gemini_cache.delete()

            # Remove from registry
            if cache_name in self.cache_registry:
                del self.cache_registry[cache_name]

            logger.info(f"Cache deleted successfully: {cache_name}")

            return {
                "success": True,
                "message": "Cache deleted successfully",
                "cache_name": cache_name,
                "deleted_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to delete cache {cache_name}: {e}")
            raise APIError(
                message=f"Failed to delete cache: {str(e)}",
                error_code=ErrorCode.EXTERNAL_API_ERROR,
                details={"cache_name": cache_name, "error": str(e)}
            )

    def get_cached_model(self, cache_name: str) -> Any:
        """Get a GenerativeModel instance using cached content.

        Args:
            cache_name: Name of the cache

        Returns:
            GenerativeModel instance with cached content

        Raises:
            AIServiceException: If cache not found
        """
        try:
            # Get cache from registry or API
            if cache_name in self.cache_registry:
                gemini_cache = self.cache_registry[cache_name]["gemini_cache"]
            else:
                gemini_cache = caching.CachedContent.get(cache_name)

            # Create model from cached content
            model = genai.GenerativeModel.from_cached_content(cached_content=gemini_cache)

            logger.info(f"Created model from cache: {cache_name}")
            return model

        except Exception as e:
            logger.error(f"Failed to get cached model {cache_name}: {e}")
            raise APIError(
                message=f"Failed to get cached model: {str(e)}",
                error_code=ErrorCode.EXTERNAL_API_ERROR,
                details={"cache_name": cache_name, "error": str(e)}
            )

    def cleanup_expired_caches(self) -> Dict[str, Any]:
        """Clean up expired caches from local registry.

        Returns:
            Cleanup results
        """
        try:
            current_time = datetime.now()
            expired_caches = []

            for cache_name, cache_data in list(self.cache_registry.items()):
                cache_info = cache_data["cache_info"]

                # Check if cache is expired
                if cache_info.expire_time <= current_time:
                    expired_caches.append(cache_name)
                    del self.cache_registry[cache_name]

            logger.info(f"Cleaned up {len(expired_caches)} expired caches from registry")

            return {
                "cleaned_count": len(expired_caches),
                "expired_caches": expired_caches,
                "cleanup_time": current_time.isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to cleanup expired caches: {e}")
            return {
                "cleaned_count": 0,
                "error": str(e),
                "cleanup_time": datetime.now().isoformat()
            }


# Global context cache manager
context_cache_manager = ContextCacheManager()
