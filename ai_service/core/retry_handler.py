"""
Retry mechanism and circuit breaker for AI service operations.
"""
import asyncio
import logging
import time
from typing import Any, Callable, Dict, Optional, Type, Union
from functools import wraps
from enum import Enum

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, failing fast
    HALF_OPEN = "half_open"  # Testing if service is back


class RetryableError(Exception):
    """Base class for errors that should trigger retries."""
    pass


class RateLimitError(RetryableError):
    """Rate limit exceeded error."""
    pass


class TemporaryError(RetryableError):
    """Temporary service error."""
    pass


class CircuitBreaker:
    """Circuit breaker implementation for API calls."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: Type[Exception] = Exception
    ):
        """Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before trying again
            expected_exception: Exception type to monitor
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    async def acall(self, func: Callable, *args, **kwargs) -> Any:
        """Execute async function with circuit breaker protection."""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
        logger.debug("Circuit breaker reset to CLOSED state")
    
    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker OPENED after {self.failure_count} failures")


class RetryHandler:
    """Retry handler with exponential backoff."""
    
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        """Initialize retry handler.
        
        Args:
            max_retries: Maximum number of retry attempts
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds
            exponential_base: Base for exponential backoff
            jitter: Whether to add random jitter
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt."""
        delay = self.base_delay * (self.exponential_base ** attempt)
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """Check if error is retryable."""
        # Check for specific retryable errors
        if isinstance(error, RetryableError):
            return True
        
        # Check error message for common retryable patterns
        error_str = str(error).lower()
        retryable_patterns = [
            "rate limit",
            "quota exceeded",
            "timeout",
            "connection",
            "network",
            "temporary",
            "service unavailable",
            "internal server error",
            "502",
            "503",
            "504"
        ]
        
        return any(pattern in error_str for pattern in retryable_patterns)
    
    def retry(self, func: Callable) -> Callable:
        """Decorator for synchronous functions."""
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(self.max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == self.max_retries:
                        logger.error(f"Max retries ({self.max_retries}) exceeded for {func.__name__}")
                        raise e
                    
                    if not self._is_retryable_error(e):
                        logger.info(f"Non-retryable error in {func.__name__}: {e}")
                        raise e
                    
                    delay = self._calculate_delay(attempt)
                    logger.warning(
                        f"Attempt {attempt + 1}/{self.max_retries + 1} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s"
                    )
                    time.sleep(delay)
            
            raise last_exception
        
        return wrapper
    
    def aretry(self, func: Callable) -> Callable:
        """Decorator for asynchronous functions."""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(self.max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == self.max_retries:
                        logger.error(f"Max retries ({self.max_retries}) exceeded for {func.__name__}")
                        raise e
                    
                    if not self._is_retryable_error(e):
                        logger.info(f"Non-retryable error in {func.__name__}: {e}")
                        raise e
                    
                    delay = self._calculate_delay(attempt)
                    logger.warning(
                        f"Attempt {attempt + 1}/{self.max_retries + 1} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s"
                    )
                    await asyncio.sleep(delay)
            
            raise last_exception
        
        return wrapper


# Global instances
default_retry_handler = RetryHandler(max_retries=3, base_delay=1.0, max_delay=30.0)
api_circuit_breaker = CircuitBreaker(failure_threshold=5, recovery_timeout=60)


def with_retry(max_retries: int = 3, base_delay: float = 1.0):
    """Convenience decorator for retry functionality."""
    handler = RetryHandler(max_retries=max_retries, base_delay=base_delay)
    return handler.retry


def with_aretry(max_retries: int = 3, base_delay: float = 1.0):
    """Convenience decorator for async retry functionality."""
    handler = RetryHandler(max_retries=max_retries, base_delay=base_delay)
    return handler.aretry
