"""
Predictive Scaling Manager for Phase 8.
Provides intelligent auto-scaling based on ML predictions and usage patterns.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import statistics
from collections import deque, defaultdict

logger = logging.getLogger(__name__)


class ScalingAction(Enum):
    """Scaling actions."""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    MAINTAIN = "maintain"
    EMERGENCY_SCALE = "emergency_scale"


class ScalingTrigger(Enum):
    """Scaling triggers."""
    LOAD_PREDICTION = "load_prediction"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    COST_OPTIMIZATION = "cost_optimization"
    SCHEDULED_EVENT = "scheduled_event"
    ANOMALY_DETECTION = "anomaly_detection"
    MANUAL_OVERRIDE = "manual_override"


class ResourceType(Enum):
    """Types of resources to scale."""
    COMPUTE_INSTANCES = "compute_instances"
    CACHE_CAPACITY = "cache_capacity"
    DATABASE_CONNECTIONS = "database_connections"
    API_RATE_LIMITS = "api_rate_limits"
    STORAGE_CAPACITY = "storage_capacity"
    NETWORK_BANDWIDTH = "network_bandwidth"


@dataclass
class ScalingMetrics:
    """Current system metrics for scaling decisions."""
    # Load metrics
    cpu_utilization: float
    memory_utilization: float
    request_rate: float
    queue_length: int
    
    # Performance metrics
    avg_response_time: float
    error_rate: float
    cache_hit_rate: float
    
    # Business metrics
    active_users: int
    revenue_per_hour: float
    cost_per_request: float
    
    # Predictions
    predicted_load_1h: float
    predicted_load_6h: float
    predicted_load_24h: float
    
    timestamp: float = field(default_factory=time.time)


@dataclass
class ScalingRule:
    """Scaling rule configuration."""
    rule_id: str
    name: str
    resource_type: ResourceType
    
    # Conditions
    trigger_conditions: Dict[str, Any]
    cooldown_period: int  # seconds
    
    # Actions
    scale_up_amount: int
    scale_down_amount: int
    min_instances: int
    max_instances: int
    
    # Advanced settings
    prediction_weight: float = 0.7
    reactive_weight: float = 0.3
    confidence_threshold: float = 0.6
    
    # State
    last_action_time: float = 0
    is_enabled: bool = True


@dataclass
class ScalingEvent:
    """Scaling event record."""
    event_id: str
    timestamp: float
    resource_type: ResourceType
    action: ScalingAction
    trigger: ScalingTrigger
    
    # Context
    before_capacity: int
    after_capacity: int
    metrics_snapshot: ScalingMetrics
    
    # Decision details
    confidence: float
    reasoning: str
    prediction_accuracy: Optional[float] = None
    
    # Outcome
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class ResourcePool:
    """Resource pool configuration."""
    pool_id: str
    resource_type: ResourceType
    region: str
    
    # Current state
    current_capacity: int
    target_capacity: int
    min_capacity: int
    max_capacity: int
    
    # Performance
    utilization: float
    health_score: float
    cost_per_unit: float
    
    # Scaling settings
    scale_up_threshold: float = 0.7
    scale_down_threshold: float = 0.3
    auto_scaling_enabled: bool = True
    
    last_scaled: float = 0


class PredictiveScalingManager:
    """Predictive scaling manager with ML-based optimization."""
    
    def __init__(self):
        self.resource_pools: Dict[str, ResourcePool] = {}
        self.scaling_rules: Dict[str, ScalingRule] = {}
        self.scaling_events: List[ScalingEvent] = []
        self.metrics_history: deque = deque(maxlen=10000)
        
        # Prediction models
        self.load_patterns: Dict[str, List[float]] = defaultdict(list)
        self.seasonal_patterns: Dict[int, float] = {}  # hour -> load multiplier
        self.weekly_patterns: Dict[int, float] = {}   # day of week -> load multiplier
        
        # Configuration
        self.prediction_horizon = 3600  # 1 hour
        self.scaling_interval = 300     # 5 minutes
        self.emergency_threshold = 0.95 # 95% utilization
        
        # Background tasks
        self._scaling_task: Optional[asyncio.Task] = None
        self._analysis_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Initialize default resource pools
        self._initialize_default_pools()
        self._initialize_default_rules()
        
        logger.info("Predictive Scaling Manager initialized")
    
    def _initialize_default_pools(self):
        """Initialize default resource pools."""
        default_pools = [
            ResourcePool(
                pool_id="compute_us_east_1",
                resource_type=ResourceType.COMPUTE_INSTANCES,
                region="us-east-1",
                current_capacity=5,
                target_capacity=5,
                min_capacity=2,
                max_capacity=50,
                utilization=0.4,
                health_score=0.95,
                cost_per_unit=0.10
            ),
            ResourcePool(
                pool_id="cache_global",
                resource_type=ResourceType.CACHE_CAPACITY,
                region="global",
                current_capacity=1000,  # MB
                target_capacity=1000,
                min_capacity=500,
                max_capacity=10000,
                utilization=0.6,
                health_score=0.98,
                cost_per_unit=0.001
            ),
            ResourcePool(
                pool_id="api_rate_limits",
                resource_type=ResourceType.API_RATE_LIMITS,
                region="global",
                current_capacity=1000,  # requests/minute
                target_capacity=1000,
                min_capacity=100,
                max_capacity=10000,
                utilization=0.3,
                health_score=1.0,
                cost_per_unit=0.0001
            )
        ]
        
        for pool in default_pools:
            self.resource_pools[pool.pool_id] = pool
    
    def _initialize_default_rules(self):
        """Initialize default scaling rules."""
        default_rules = [
            ScalingRule(
                rule_id="compute_auto_scale",
                name="Compute Auto Scaling",
                resource_type=ResourceType.COMPUTE_INSTANCES,
                trigger_conditions={
                    "cpu_utilization_threshold": 0.7,
                    "response_time_threshold": 2.0,
                    "queue_length_threshold": 100
                },
                cooldown_period=300,  # 5 minutes
                scale_up_amount=2,
                scale_down_amount=1,
                min_instances=2,
                max_instances=50
            ),
            ScalingRule(
                rule_id="cache_predictive_scale",
                name="Cache Predictive Scaling",
                resource_type=ResourceType.CACHE_CAPACITY,
                trigger_conditions={
                    "cache_hit_rate_threshold": 0.8,
                    "predicted_load_increase": 0.5
                },
                cooldown_period=600,  # 10 minutes
                scale_up_amount=500,  # MB
                scale_down_amount=200,
                min_instances=500,
                max_instances=10000
            ),
            ScalingRule(
                rule_id="emergency_scale",
                name="Emergency Scaling",
                resource_type=ResourceType.COMPUTE_INSTANCES,
                trigger_conditions={
                    "cpu_utilization_threshold": 0.95,
                    "error_rate_threshold": 0.1
                },
                cooldown_period=60,   # 1 minute
                scale_up_amount=5,
                scale_down_amount=0,
                min_instances=2,
                max_instances=100
            )
        ]
        
        for rule in default_rules:
            self.scaling_rules[rule.rule_id] = rule
    
    async def start(self):
        """Start predictive scaling manager."""
        if self._running:
            return
        
        self._running = True
        
        # Start background tasks
        self._scaling_task = asyncio.create_task(self._scaling_loop())
        self._analysis_task = asyncio.create_task(self._analysis_loop())
        
        logger.info("Predictive Scaling Manager started")
    
    async def stop(self):
        """Stop predictive scaling manager."""
        self._running = False
        
        # Cancel background tasks
        if self._scaling_task:
            self._scaling_task.cancel()
        if self._analysis_task:
            self._analysis_task.cancel()
        
        # Wait for tasks to complete
        tasks = [self._scaling_task, self._analysis_task]
        for task in tasks:
            if task:
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("Predictive Scaling Manager stopped")
    
    def record_metrics(self, metrics: ScalingMetrics):
        """Record system metrics for scaling decisions."""
        self.metrics_history.append(metrics)
        
        # Update resource pool utilization
        for pool in self.resource_pools.values():
            if pool.resource_type == ResourceType.COMPUTE_INSTANCES:
                pool.utilization = metrics.cpu_utilization
            elif pool.resource_type == ResourceType.CACHE_CAPACITY:
                pool.utilization = 1.0 - metrics.cache_hit_rate
        
        # Learn patterns
        self._learn_patterns(metrics)
    
    def _learn_patterns(self, metrics: ScalingMetrics):
        """Learn load patterns from metrics."""
        current_time = time.localtime(metrics.timestamp)
        hour = current_time.tm_hour
        day_of_week = current_time.tm_wday
        
        # Record hourly patterns
        if hour not in self.seasonal_patterns:
            self.seasonal_patterns[hour] = []
        self.seasonal_patterns[hour].append(metrics.request_rate)
        
        # Keep only recent data
        if len(self.seasonal_patterns[hour]) > 100:
            self.seasonal_patterns[hour] = self.seasonal_patterns[hour][-100:]
        
        # Record weekly patterns
        if day_of_week not in self.weekly_patterns:
            self.weekly_patterns[day_of_week] = []
        self.weekly_patterns[day_of_week].append(metrics.request_rate)
        
        if len(self.weekly_patterns[day_of_week]) > 50:
            self.weekly_patterns[day_of_week] = self.weekly_patterns[day_of_week][-50:]
    
    async def predict_load(self, horizon_seconds: int = 3600) -> Dict[str, float]:
        """Predict load for the next time period."""
        current_time = time.time()
        future_time = current_time + horizon_seconds
        future_local = time.localtime(future_time)
        
        # Get base load from recent metrics
        if self.metrics_history:
            recent_metrics = list(self.metrics_history)[-10:]
            base_load = statistics.mean([m.request_rate for m in recent_metrics])
        else:
            base_load = 100.0  # Default
        
        # Apply seasonal patterns
        hour = future_local.tm_hour
        day_of_week = future_local.tm_wday
        
        seasonal_multiplier = 1.0
        if hour in self.seasonal_patterns and self.seasonal_patterns[hour]:
            hour_avg = statistics.mean(self.seasonal_patterns[hour])
            current_avg = statistics.mean([
                statistics.mean(pattern) for pattern in self.seasonal_patterns.values() 
                if pattern
            ]) if self.seasonal_patterns else base_load
            
            if current_avg > 0:
                seasonal_multiplier = hour_avg / current_avg
        
        weekly_multiplier = 1.0
        if day_of_week in self.weekly_patterns and self.weekly_patterns[day_of_week]:
            day_avg = statistics.mean(self.weekly_patterns[day_of_week])
            week_avg = statistics.mean([
                statistics.mean(pattern) for pattern in self.weekly_patterns.values() 
                if pattern
            ]) if self.weekly_patterns else base_load
            
            if week_avg > 0:
                weekly_multiplier = day_avg / week_avg
        
        predicted_load = base_load * seasonal_multiplier * weekly_multiplier
        
        return {
            "predicted_load": predicted_load,
            "base_load": base_load,
            "seasonal_multiplier": seasonal_multiplier,
            "weekly_multiplier": weekly_multiplier,
            "confidence": min(0.9, len(self.metrics_history) / 1000)
        }
    
    async def evaluate_scaling_decision(self, pool_id: str) -> Optional[ScalingAction]:
        """Evaluate if scaling is needed for a resource pool."""
        if pool_id not in self.resource_pools:
            return None
        
        pool = self.resource_pools[pool_id]
        
        if not pool.auto_scaling_enabled:
            return None
        
        # Check cooldown period
        if time.time() - pool.last_scaled < 300:  # 5 minutes
            return None
        
        # Get current metrics
        if not self.metrics_history:
            return None
        
        current_metrics = self.metrics_history[-1]
        
        # Get load prediction
        load_prediction = await self.predict_load()
        predicted_load = load_prediction["predicted_load"]
        confidence = load_prediction["confidence"]
        
        # Calculate predicted utilization
        if self.metrics_history:
            recent_load = statistics.mean([m.request_rate for m in list(self.metrics_history)[-5:]])
            if recent_load > 0:
                load_ratio = predicted_load / recent_load
                predicted_utilization = pool.utilization * load_ratio
            else:
                predicted_utilization = pool.utilization
        else:
            predicted_utilization = pool.utilization
        
        # Emergency scaling
        if pool.utilization > self.emergency_threshold:
            return ScalingAction.EMERGENCY_SCALE
        
        # Predictive scaling up
        if predicted_utilization > pool.scale_up_threshold and confidence > 0.6:
            if pool.current_capacity < pool.max_capacity:
                return ScalingAction.SCALE_UP
        
        # Predictive scaling down
        elif predicted_utilization < pool.scale_down_threshold and confidence > 0.7:
            if pool.current_capacity > pool.min_capacity:
                return ScalingAction.SCALE_DOWN
        
        return ScalingAction.MAINTAIN
    
    async def execute_scaling_action(
        self,
        pool_id: str,
        action: ScalingAction,
        trigger: ScalingTrigger = ScalingTrigger.LOAD_PREDICTION
    ) -> bool:
        """Execute scaling action."""
        if pool_id not in self.resource_pools:
            return False
        
        pool = self.resource_pools[pool_id]
        
        # Find applicable scaling rule
        scaling_rule = None
        for rule in self.scaling_rules.values():
            if rule.resource_type == pool.resource_type and rule.is_enabled:
                scaling_rule = rule
                break
        
        if not scaling_rule:
            logger.warning(f"No scaling rule found for {pool.resource_type.value}")
            return False
        
        # Calculate new capacity
        before_capacity = pool.current_capacity
        new_capacity = before_capacity
        
        if action == ScalingAction.SCALE_UP:
            new_capacity = min(
                pool.max_capacity,
                before_capacity + scaling_rule.scale_up_amount
            )
        elif action == ScalingAction.SCALE_DOWN:
            new_capacity = max(
                pool.min_capacity,
                before_capacity - scaling_rule.scale_down_amount
            )
        elif action == ScalingAction.EMERGENCY_SCALE:
            new_capacity = min(
                pool.max_capacity,
                before_capacity + scaling_rule.scale_up_amount * 2
            )
        
        if new_capacity == before_capacity:
            return False  # No change needed
        
        # Execute scaling
        try:
            success = await self._perform_scaling(pool, new_capacity)
            
            if success:
                pool.current_capacity = new_capacity
                pool.target_capacity = new_capacity
                pool.last_scaled = time.time()
                
                # Record scaling event
                event = ScalingEvent(
                    event_id=f"scale_{pool_id}_{int(time.time())}",
                    timestamp=time.time(),
                    resource_type=pool.resource_type,
                    action=action,
                    trigger=trigger,
                    before_capacity=before_capacity,
                    after_capacity=new_capacity,
                    metrics_snapshot=self.metrics_history[-1] if self.metrics_history else None,
                    confidence=0.8,
                    reasoning=f"Scaled {action.value} from {before_capacity} to {new_capacity}",
                    success=True
                )
                
                self.scaling_events.append(event)
                
                logger.info(f"Successfully scaled {pool_id}: {before_capacity} -> {new_capacity}")
                return True
            else:
                logger.error(f"Failed to scale {pool_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error scaling {pool_id}: {e}")
            return False
    
    async def _perform_scaling(self, pool: ResourcePool, new_capacity: int) -> bool:
        """Perform actual scaling operation."""
        # Mock scaling implementation
        # In production, this would integrate with cloud providers
        
        if pool.resource_type == ResourceType.COMPUTE_INSTANCES:
            # Scale compute instances
            logger.info(f"Scaling compute instances to {new_capacity}")
            await asyncio.sleep(0.1)  # Simulate scaling time
            
        elif pool.resource_type == ResourceType.CACHE_CAPACITY:
            # Scale cache capacity
            logger.info(f"Scaling cache capacity to {new_capacity} MB")
            await asyncio.sleep(0.05)
            
        elif pool.resource_type == ResourceType.API_RATE_LIMITS:
            # Adjust API rate limits
            logger.info(f"Scaling API rate limits to {new_capacity} req/min")
            
        return True
    
    async def _scaling_loop(self):
        """Background scaling evaluation loop."""
        while self._running:
            try:
                await self._evaluate_all_pools()
                await asyncio.sleep(self.scaling_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in scaling loop: {e}")
                await asyncio.sleep(self.scaling_interval)
    
    async def _evaluate_all_pools(self):
        """Evaluate scaling for all resource pools."""
        for pool_id in self.resource_pools.keys():
            try:
                action = await self.evaluate_scaling_decision(pool_id)
                
                if action and action != ScalingAction.MAINTAIN:
                    await self.execute_scaling_action(pool_id, action)
                    
            except Exception as e:
                logger.error(f"Error evaluating scaling for {pool_id}: {e}")
    
    async def _analysis_loop(self):
        """Background analysis and optimization loop."""
        while self._running:
            try:
                await self._analyze_scaling_performance()
                await asyncio.sleep(3600)  # Run every hour
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in analysis loop: {e}")
                await asyncio.sleep(3600)
    
    async def _analyze_scaling_performance(self):
        """Analyze scaling performance and optimize rules."""
        if len(self.scaling_events) < 10:
            return
        
        # Analyze recent scaling events
        recent_events = [e for e in self.scaling_events if time.time() - e.timestamp < 86400]
        
        if not recent_events:
            return
        
        # Calculate success rate
        success_rate = sum(1 for e in recent_events if e.success) / len(recent_events)
        
        # Analyze scaling frequency
        scale_up_events = [e for e in recent_events if e.action == ScalingAction.SCALE_UP]
        scale_down_events = [e for e in recent_events if e.action == ScalingAction.SCALE_DOWN]
        
        logger.info(f"Scaling analysis: {len(recent_events)} events, {success_rate:.2%} success rate")
        logger.info(f"Scale up: {len(scale_up_events)}, Scale down: {len(scale_down_events)}")
    
    def get_scaling_status(self) -> Dict[str, Any]:
        """Get scaling system status."""
        return {
            "resource_pools": {
                pool_id: {
                    "resource_type": pool.resource_type.value,
                    "current_capacity": pool.current_capacity,
                    "target_capacity": pool.target_capacity,
                    "utilization": pool.utilization,
                    "health_score": pool.health_score,
                    "auto_scaling_enabled": pool.auto_scaling_enabled
                }
                for pool_id, pool in self.resource_pools.items()
            },
            "scaling_rules": {
                rule_id: {
                    "name": rule.name,
                    "resource_type": rule.resource_type.value,
                    "is_enabled": rule.is_enabled,
                    "cooldown_period": rule.cooldown_period
                }
                for rule_id, rule in self.scaling_rules.items()
            },
            "recent_events": len([
                e for e in self.scaling_events 
                if time.time() - e.timestamp < 3600
            ]),
            "prediction_accuracy": self._calculate_prediction_accuracy(),
            "patterns_learned": {
                "seasonal_hours": len(self.seasonal_patterns),
                "weekly_days": len(self.weekly_patterns)
            }
        }
    
    def _calculate_prediction_accuracy(self) -> float:
        """Calculate prediction accuracy from recent events."""
        if len(self.scaling_events) < 5:
            return 0.0
        
        # Simple accuracy calculation based on successful scaling events
        recent_events = self.scaling_events[-20:]
        successful_predictions = sum(1 for e in recent_events if e.success)
        
        return successful_predictions / len(recent_events)


# Global predictive scaling manager instance
predictive_scaling_manager = PredictiveScalingManager()
