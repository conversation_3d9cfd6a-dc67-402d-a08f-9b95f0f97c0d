"""
AI Analytics & Intelligence Engine for Phase 6.
Provides advanced analytics, usage patterns, and intelligent insights.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import statistics
import numpy as np

from ai_service.core.multi_provider_manager import AIProvider, ModelCapability, RequestMetrics

logger = logging.getLogger(__name__)


class AnalyticsMetric(Enum):
    """Types of analytics metrics."""
    USAGE_PATTERNS = "usage_patterns"
    PERFORMANCE_TRENDS = "performance_trends"
    COST_ANALYSIS = "cost_analysis"
    QUALITY_ASSESSMENT = "quality_assessment"
    USER_BEHAVIOR = "user_behavior"
    MODEL_COMPARISON = "model_comparison"
    PROVIDER_RANKING = "provider_ranking"
    ANOMALY_DETECTION = "anomaly_detection"


class TimeWindow(Enum):
    """Time windows for analytics."""
    REAL_TIME = "real_time"  # Last 5 minutes
    HOURLY = "hourly"        # Last hour
    DAILY = "daily"          # Last 24 hours
    WEEKLY = "weekly"        # Last 7 days
    MONTHLY = "monthly"      # Last 30 days


@dataclass
class UsagePattern:
    """Usage pattern analysis."""
    capability: ModelCapability
    provider: AIProvider
    model_id: str
    request_count: int
    avg_response_time: float
    success_rate: float
    peak_hours: List[int]
    user_segments: Dict[str, int]
    cost_per_request: float
    quality_score: float


@dataclass
class PerformanceTrend:
    """Performance trend analysis."""
    metric_name: str
    time_series: List[Tuple[float, float]]  # (timestamp, value)
    trend_direction: str  # "increasing", "decreasing", "stable"
    trend_strength: float  # 0.0 to 1.0
    anomalies: List[Tuple[float, float]]  # Anomalous points
    forecast: Optional[List[Tuple[float, float]]] = None


@dataclass
class ModelComparison:
    """Model comparison analysis."""
    models: List[str]
    metrics: Dict[str, Dict[str, float]]  # metric_name -> model_id -> value
    rankings: Dict[str, List[str]]  # metric_name -> ranked_model_list
    recommendations: List[str]


@dataclass
class CostAnalysis:
    """Cost analysis and optimization."""
    total_cost: float
    cost_by_provider: Dict[str, float]
    cost_by_capability: Dict[str, float]
    cost_trends: List[Tuple[float, float]]
    optimization_opportunities: List[Dict[str, Any]]
    projected_monthly_cost: float


class AIAnalyticsEngine:
    """Advanced AI analytics and intelligence engine."""
    
    def __init__(self):
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.usage_patterns: Dict[str, UsagePattern] = {}
        self.performance_trends: Dict[str, PerformanceTrend] = {}
        self.model_comparisons: Dict[str, ModelComparison] = {}
        self.cost_analysis: Optional[CostAnalysis] = None
        
        # Real-time tracking
        self.real_time_metrics: Dict[str, Any] = {}
        self.anomaly_thresholds: Dict[str, float] = {
            "response_time": 5.0,  # seconds
            "error_rate": 0.1,     # 10%
            "cost_spike": 2.0      # 2x normal cost
        }
        
        # Background tasks
        self._analytics_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("AI Analytics Engine initialized")
    
    async def start(self):
        """Start analytics engine background tasks."""
        if self._running:
            return
        
        self._running = True
        self._analytics_task = asyncio.create_task(self._background_analytics())
        
        logger.info("AI Analytics Engine started")
    
    async def stop(self):
        """Stop analytics engine."""
        self._running = False
        
        if self._analytics_task:
            self._analytics_task.cancel()
            try:
                await self._analytics_task
            except asyncio.CancelledError:
                pass
        
        logger.info("AI Analytics Engine stopped")
    
    def record_request_metrics(self, metrics: RequestMetrics):
        """Record request metrics for analysis."""
        timestamp = time.time()
        
        # Store in time-series format
        metric_key = f"{metrics.provider.value}_{metrics.model_id}_{metrics.capability.value}"
        
        self.metrics_history[f"{metric_key}_response_time"].append((timestamp, metrics.response_time))
        self.metrics_history[f"{metric_key}_cost"].append((timestamp, metrics.cost))
        self.metrics_history[f"{metric_key}_success"].append((timestamp, 1.0 if metrics.success else 0.0))
        self.metrics_history[f"{metric_key}_tokens"].append((timestamp, metrics.tokens_used))
        
        # Update real-time metrics
        self._update_real_time_metrics(metrics)
        
        # Check for anomalies
        self._detect_anomalies(metrics)
    
    def _update_real_time_metrics(self, metrics: RequestMetrics):
        """Update real-time metrics."""
        current_time = time.time()
        window_start = current_time - 300  # 5 minutes
        
        # Clean old real-time data
        for key in list(self.real_time_metrics.keys()):
            if key.endswith("_timestamps"):
                timestamps = self.real_time_metrics[key]
                self.real_time_metrics[key] = [t for t in timestamps if t > window_start]
        
        # Add current metrics
        provider_key = f"{metrics.provider.value}_timestamps"
        if provider_key not in self.real_time_metrics:
            self.real_time_metrics[provider_key] = []
        self.real_time_metrics[provider_key].append(current_time)
        
        # Update aggregated metrics
        self.real_time_metrics["total_requests_5min"] = sum(
            len(timestamps) for key, timestamps in self.real_time_metrics.items()
            if key.endswith("_timestamps")
        )
    
    def _detect_anomalies(self, metrics: RequestMetrics):
        """Detect anomalies in request metrics."""
        anomalies = []
        
        # Response time anomaly
        if metrics.response_time > self.anomaly_thresholds["response_time"]:
            anomalies.append({
                "type": "high_response_time",
                "value": metrics.response_time,
                "threshold": self.anomaly_thresholds["response_time"],
                "provider": metrics.provider.value,
                "model": metrics.model_id
            })
        
        # Error rate anomaly
        if not metrics.success:
            # Calculate recent error rate
            metric_key = f"{metrics.provider.value}_{metrics.model_id}_{metrics.capability.value}"
            recent_successes = list(self.metrics_history[f"{metric_key}_success"])[-10:]
            if recent_successes:
                error_rate = 1.0 - (sum(success[1] for success in recent_successes) / len(recent_successes))
                if error_rate > self.anomaly_thresholds["error_rate"]:
                    anomalies.append({
                        "type": "high_error_rate",
                        "value": error_rate,
                        "threshold": self.anomaly_thresholds["error_rate"],
                        "provider": metrics.provider.value,
                        "model": metrics.model_id
                    })
        
        # Log anomalies
        for anomaly in anomalies:
            logger.warning(f"Anomaly detected: {anomaly}")
    
    async def analyze_usage_patterns(self, time_window: TimeWindow = TimeWindow.DAILY) -> Dict[str, UsagePattern]:
        """Analyze usage patterns across providers and models."""
        window_seconds = self._get_window_seconds(time_window)
        cutoff_time = time.time() - window_seconds
        
        patterns = {}
        
        # Group metrics by provider/model/capability
        grouped_metrics = defaultdict(list)
        
        for metric_key, time_series in self.metrics_history.items():
            if "_response_time" in metric_key:
                base_key = metric_key.replace("_response_time", "")
                parts = base_key.split("_")
                if len(parts) >= 3:
                    provider = parts[0]
                    model_id = "_".join(parts[1:-1])
                    capability = parts[-1]
                    
                    # Filter by time window
                    recent_data = [(t, v) for t, v in time_series if t > cutoff_time]
                    if recent_data:
                        grouped_metrics[base_key] = {
                            "provider": provider,
                            "model_id": model_id,
                            "capability": capability,
                            "response_times": recent_data
                        }
        
        # Analyze each group
        for key, data in grouped_metrics.items():
            try:
                provider = AIProvider(data["provider"])
                capability = ModelCapability(data["capability"])
                
                # Calculate metrics
                response_times = [v for _, v in data["response_times"]]
                request_count = len(response_times)
                avg_response_time = statistics.mean(response_times) if response_times else 0
                
                # Get success rate
                success_key = f"{key}_success"
                success_data = [(t, v) for t, v in self.metrics_history[success_key] if t > cutoff_time]
                success_rate = statistics.mean([v for _, v in success_data]) if success_data else 0
                
                # Get cost data
                cost_key = f"{key}_cost"
                cost_data = [(t, v) for t, v in self.metrics_history[cost_key] if t > cutoff_time]
                total_cost = sum(v for _, v in cost_data)
                cost_per_request = total_cost / request_count if request_count > 0 else 0
                
                # Analyze peak hours
                timestamps = [t for t, _ in data["response_times"]]
                peak_hours = self._analyze_peak_hours(timestamps)
                
                pattern = UsagePattern(
                    capability=capability,
                    provider=provider,
                    model_id=data["model_id"],
                    request_count=request_count,
                    avg_response_time=avg_response_time,
                    success_rate=success_rate,
                    peak_hours=peak_hours,
                    user_segments={},  # Would need user data
                    cost_per_request=cost_per_request,
                    quality_score=success_rate * (1.0 / (avg_response_time + 0.1))  # Simple quality metric
                )
                
                patterns[key] = pattern
                
            except (ValueError, KeyError) as e:
                logger.warning(f"Error analyzing pattern for {key}: {e}")
        
        self.usage_patterns = patterns
        return patterns
    
    def _analyze_peak_hours(self, timestamps: List[float]) -> List[int]:
        """Analyze peak usage hours."""
        if not timestamps:
            return []
        
        # Convert timestamps to hours
        hours = [int((time.localtime(ts).tm_hour)) for ts in timestamps]
        
        # Count requests per hour
        hour_counts = defaultdict(int)
        for hour in hours:
            hour_counts[hour] += 1
        
        # Find peak hours (top 25%)
        if not hour_counts:
            return []
        
        sorted_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)
        peak_count = max(1, len(sorted_hours) // 4)
        
        return [hour for hour, _ in sorted_hours[:peak_count]]
    
    async def analyze_performance_trends(self, metric_name: str, time_window: TimeWindow = TimeWindow.DAILY) -> PerformanceTrend:
        """Analyze performance trends for a specific metric."""
        window_seconds = self._get_window_seconds(time_window)
        cutoff_time = time.time() - window_seconds
        
        # Collect all data for the metric
        all_data = []
        for key, time_series in self.metrics_history.items():
            if metric_name in key:
                recent_data = [(t, v) for t, v in time_series if t > cutoff_time]
                all_data.extend(recent_data)
        
        if not all_data:
            return PerformanceTrend(
                metric_name=metric_name,
                time_series=[],
                trend_direction="stable",
                trend_strength=0.0,
                anomalies=[]
            )
        
        # Sort by timestamp
        all_data.sort(key=lambda x: x[0])
        
        # Calculate trend
        if len(all_data) > 1:
            timestamps = [t for t, _ in all_data]
            values = [v for _, v in all_data]
            
            # Simple linear regression for trend
            n = len(values)
            sum_x = sum(range(n))
            sum_y = sum(values)
            sum_xy = sum(i * v for i, v in enumerate(values))
            sum_x2 = sum(i * i for i in range(n))
            
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x) if (n * sum_x2 - sum_x * sum_x) != 0 else 0
            
            if slope > 0.01:
                trend_direction = "increasing"
            elif slope < -0.01:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
            
            trend_strength = min(abs(slope), 1.0)
        else:
            trend_direction = "stable"
            trend_strength = 0.0
        
        # Detect anomalies (simple outlier detection)
        if len(all_data) > 5:
            values = [v for _, v in all_data]
            mean_val = statistics.mean(values)
            std_val = statistics.stdev(values) if len(values) > 1 else 0
            
            anomalies = [
                (t, v) for t, v in all_data
                if abs(v - mean_val) > 2 * std_val
            ] if std_val > 0 else []
        else:
            anomalies = []
        
        trend = PerformanceTrend(
            metric_name=metric_name,
            time_series=all_data,
            trend_direction=trend_direction,
            trend_strength=trend_strength,
            anomalies=anomalies
        )
        
        self.performance_trends[metric_name] = trend
        return trend
    
    async def compare_models(self, capability: ModelCapability, time_window: TimeWindow = TimeWindow.DAILY) -> ModelComparison:
        """Compare models for a specific capability."""
        patterns = await self.analyze_usage_patterns(time_window)
        
        # Filter patterns by capability
        relevant_patterns = {
            key: pattern for key, pattern in patterns.items()
            if pattern.capability == capability
        }
        
        if not relevant_patterns:
            return ModelComparison(
                models=[],
                metrics={},
                rankings={},
                recommendations=[]
            )
        
        # Extract models and metrics
        models = list(set(f"{p.provider.value}:{p.model_id}" for p in relevant_patterns.values()))
        
        metrics = {
            "response_time": {},
            "success_rate": {},
            "cost_per_request": {},
            "quality_score": {}
        }
        
        for pattern in relevant_patterns.values():
            model_key = f"{pattern.provider.value}:{pattern.model_id}"
            metrics["response_time"][model_key] = pattern.avg_response_time
            metrics["success_rate"][model_key] = pattern.success_rate
            metrics["cost_per_request"][model_key] = pattern.cost_per_request
            metrics["quality_score"][model_key] = pattern.quality_score
        
        # Create rankings
        rankings = {}
        for metric_name, model_values in metrics.items():
            if metric_name in ["response_time", "cost_per_request"]:
                # Lower is better
                rankings[metric_name] = sorted(model_values.keys(), key=lambda m: model_values[m])
            else:
                # Higher is better
                rankings[metric_name] = sorted(model_values.keys(), key=lambda m: model_values[m], reverse=True)
        
        # Generate recommendations
        recommendations = self._generate_model_recommendations(metrics, rankings)
        
        comparison = ModelComparison(
            models=models,
            metrics=metrics,
            rankings=rankings,
            recommendations=recommendations
        )
        
        self.model_comparisons[capability.value] = comparison
        return comparison
    
    def _generate_model_recommendations(self, metrics: Dict[str, Dict[str, float]], rankings: Dict[str, List[str]]) -> List[str]:
        """Generate model recommendations based on analysis."""
        recommendations = []
        
        # Best overall performer
        if "quality_score" in rankings and rankings["quality_score"]:
            best_quality = rankings["quality_score"][0]
            recommendations.append(f"Best overall quality: {best_quality}")
        
        # Most cost-effective
        if "cost_per_request" in rankings and rankings["cost_per_request"]:
            most_cost_effective = rankings["cost_per_request"][0]
            recommendations.append(f"Most cost-effective: {most_cost_effective}")
        
        # Fastest response
        if "response_time" in rankings and rankings["response_time"]:
            fastest = rankings["response_time"][0]
            recommendations.append(f"Fastest response: {fastest}")
        
        # Most reliable
        if "success_rate" in rankings and rankings["success_rate"]:
            most_reliable = rankings["success_rate"][0]
            recommendations.append(f"Most reliable: {most_reliable}")
        
        return recommendations
    
    def _get_window_seconds(self, time_window: TimeWindow) -> int:
        """Get window duration in seconds."""
        windows = {
            TimeWindow.REAL_TIME: 300,      # 5 minutes
            TimeWindow.HOURLY: 3600,        # 1 hour
            TimeWindow.DAILY: 86400,        # 24 hours
            TimeWindow.WEEKLY: 604800,      # 7 days
            TimeWindow.MONTHLY: 2592000     # 30 days
        }
        return windows.get(time_window, 86400)
    
    async def _background_analytics(self):
        """Background task for continuous analytics."""
        while self._running:
            try:
                # Update usage patterns every 5 minutes
                await self.analyze_usage_patterns(TimeWindow.HOURLY)
                
                # Update performance trends every 10 minutes
                for metric in ["response_time", "cost", "success"]:
                    await self.analyze_performance_trends(metric, TimeWindow.DAILY)
                
                await asyncio.sleep(300)  # 5 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background analytics: {e}")
                await asyncio.sleep(300)
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get comprehensive analytics summary."""
        return {
            "usage_patterns": {
                name: {
                    "provider": pattern.provider.value,
                    "model": pattern.model_id,
                    "capability": pattern.capability.value,
                    "requests": pattern.request_count,
                    "avg_response_time": pattern.avg_response_time,
                    "success_rate": pattern.success_rate,
                    "cost_per_request": pattern.cost_per_request,
                    "quality_score": pattern.quality_score
                }
                for name, pattern in self.usage_patterns.items()
            },
            "performance_trends": {
                name: {
                    "trend_direction": trend.trend_direction,
                    "trend_strength": trend.trend_strength,
                    "anomalies_count": len(trend.anomalies)
                }
                for name, trend in self.performance_trends.items()
            },
            "model_comparisons": {
                capability: {
                    "models_count": len(comparison.models),
                    "best_quality": comparison.rankings.get("quality_score", [None])[0],
                    "most_cost_effective": comparison.rankings.get("cost_per_request", [None])[0],
                    "recommendations": comparison.recommendations
                }
                for capability, comparison in self.model_comparisons.items()
            },
            "real_time_metrics": self.real_time_metrics
        }


# Global analytics engine instance
ai_analytics_engine = AIAnalyticsEngine()
