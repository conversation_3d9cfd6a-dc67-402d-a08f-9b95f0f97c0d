"""
Advanced streaming manager for real-time AI service features.
"""
import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, AsyncIterator, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict
from enum import Enum
import weakref

from ai_service.config.settings import settings
from ai_service.core.metrics import metrics_collector

logger = logging.getLogger(__name__)


class StreamType(Enum):
    """Stream type enumeration."""
    TEXT_GENERATION = "text_generation"
    FUNCTION_CALLING = "function_calling"
    MULTIMODAL = "multimodal"
    CHAT = "chat"
    EMBEDDINGS = "embeddings"
    BATCH_PROCESSING = "batch_processing"
    SYSTEM_EVENTS = "system_events"


class StreamStatus(Enum):
    """Stream status enumeration."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"


@dataclass
class StreamChunk:
    """Individual stream chunk."""
    id: str
    stream_id: str
    chunk_type: str
    data: Any
    timestamp: datetime = field(default_factory=datetime.utcnow)
    sequence: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    is_final: bool = False


@dataclass
class StreamSession:
    """Stream session information."""
    id: str
    stream_type: StreamType
    status: StreamStatus
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    client_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    chunks_sent: int = 0
    bytes_sent: int = 0
    error_message: Optional[str] = None


@dataclass
class StreamConfig:
    """Stream configuration."""
    buffer_size: int = 1024
    chunk_delay_ms: int = 50
    max_chunk_size: int = 8192
    heartbeat_interval: int = 30
    timeout_seconds: int = 300
    enable_compression: bool = True
    enable_heartbeat: bool = True


class StreamBuffer:
    """Thread-safe stream buffer."""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.buffer: List[StreamChunk] = []
        self.lock = asyncio.Lock()
        self.condition = asyncio.Condition(self.lock)
        self.closed = False
    
    async def put(self, chunk: StreamChunk):
        """Add chunk to buffer."""
        async with self.condition:
            if self.closed:
                raise RuntimeError("Buffer is closed")
            
            # Remove old chunks if buffer is full
            while len(self.buffer) >= self.max_size:
                self.buffer.pop(0)
            
            self.buffer.append(chunk)
            self.condition.notify_all()
    
    async def get(self, timeout: Optional[float] = None) -> Optional[StreamChunk]:
        """Get chunk from buffer."""
        async with self.condition:
            if self.buffer:
                return self.buffer.pop(0)
            
            if self.closed:
                return None
            
            try:
                await asyncio.wait_for(
                    self.condition.wait(),
                    timeout=timeout
                )
                
                if self.buffer:
                    return self.buffer.pop(0)
                
            except asyncio.TimeoutError:
                pass
            
            return None
    
    async def close(self):
        """Close buffer."""
        async with self.condition:
            self.closed = True
            self.condition.notify_all()
    
    def is_empty(self) -> bool:
        """Check if buffer is empty."""
        return len(self.buffer) == 0


class StreamingManager:
    """Advanced streaming manager for real-time features."""
    
    def __init__(self, config: Optional[StreamConfig] = None):
        self.config = config or StreamConfig()
        
        # Stream management
        self.active_streams: Dict[str, StreamSession] = {}
        self.stream_buffers: Dict[str, StreamBuffer] = {}
        self.stream_generators: Dict[str, AsyncIterator] = {}
        
        # Client connections
        self.client_streams: Dict[str, List[str]] = defaultdict(list)
        self.stream_subscribers: Dict[str, List[Callable]] = defaultdict(list)
        
        # Background tasks
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.total_streams_created = 0
        self.total_chunks_sent = 0
        self.total_bytes_sent = 0
        
        # Start background tasks
        self._start_background_tasks()
        
        logger.info("Streaming manager initialized")
    
    def _start_background_tasks(self):
        """Start background tasks."""
        if self.config.enable_heartbeat:
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def create_stream(
        self,
        stream_type: StreamType,
        client_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new stream session."""
        stream_id = str(uuid.uuid4())
        
        session = StreamSession(
            id=stream_id,
            stream_type=stream_type,
            status=StreamStatus.INITIALIZING,
            client_id=client_id,
            metadata=metadata or {}
        )
        
        # Create stream buffer
        buffer = StreamBuffer(max_size=self.config.buffer_size)
        
        # Store stream
        self.active_streams[stream_id] = session
        self.stream_buffers[stream_id] = buffer
        
        # Track client streams
        if client_id:
            self.client_streams[client_id].append(stream_id)
        
        # Update statistics
        self.total_streams_created += 1
        
        logger.info(f"Created stream {stream_id} for client {client_id}")
        
        # Record metrics
        metrics_collector.record_counter(
            "streams_created_total",
            1.0,
            {"stream_type": stream_type.value, "client_id": client_id or "unknown"}
        )
        
        return stream_id
    
    async def start_stream(self, stream_id: str) -> bool:
        """Start streaming for a session."""
        if stream_id not in self.active_streams:
            return False
        
        session = self.active_streams[stream_id]
        session.status = StreamStatus.ACTIVE
        session.started_at = datetime.utcnow()
        
        logger.info(f"Started stream {stream_id}")
        return True
    
    async def send_chunk(
        self,
        stream_id: str,
        chunk_type: str,
        data: Any,
        metadata: Optional[Dict[str, Any]] = None,
        is_final: bool = False
    ) -> bool:
        """Send chunk to stream."""
        if stream_id not in self.active_streams:
            return False
        
        session = self.active_streams[stream_id]
        buffer = self.stream_buffers[stream_id]
        
        # Create chunk
        chunk = StreamChunk(
            id=str(uuid.uuid4()),
            stream_id=stream_id,
            chunk_type=chunk_type,
            data=data,
            sequence=session.chunks_sent,
            metadata=metadata or {},
            is_final=is_final
        )
        
        try:
            # Add to buffer
            await buffer.put(chunk)
            
            # Update session statistics
            session.chunks_sent += 1
            chunk_size = len(json.dumps(data, default=str).encode('utf-8'))
            session.bytes_sent += chunk_size
            
            # Update global statistics
            self.total_chunks_sent += 1
            self.total_bytes_sent += chunk_size
            
            # Complete stream if final chunk
            if is_final:
                await self.complete_stream(stream_id)
            
            # Record metrics
            metrics_collector.record_histogram(
                "stream_chunk_size_bytes",
                chunk_size,
                {"stream_type": session.stream_type.value, "chunk_type": chunk_type}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending chunk to stream {stream_id}: {e}")
            await self.error_stream(stream_id, str(e))
            return False
    
    async def get_stream_iterator(self, stream_id: str) -> Optional[AsyncIterator[StreamChunk]]:
        """Get async iterator for stream chunks."""
        if stream_id not in self.active_streams:
            return None
        
        buffer = self.stream_buffers[stream_id]
        session = self.active_streams[stream_id]
        
        async def chunk_iterator():
            """Async iterator for stream chunks."""
            try:
                while session.status in [StreamStatus.ACTIVE, StreamStatus.INITIALIZING]:
                    chunk = await buffer.get(timeout=1.0)
                    
                    if chunk is None:
                        # Check if stream is still active
                        if session.status != StreamStatus.ACTIVE:
                            break
                        continue
                    
                    yield chunk
                    
                    # Add delay between chunks if configured
                    if self.config.chunk_delay_ms > 0:
                        await asyncio.sleep(self.config.chunk_delay_ms / 1000)
                    
                    # Break if final chunk
                    if chunk.is_final:
                        break
                        
            except Exception as e:
                logger.error(f"Error in stream iterator {stream_id}: {e}")
                await self.error_stream(stream_id, str(e))
        
        return chunk_iterator()
    
    async def complete_stream(self, stream_id: str) -> bool:
        """Complete a stream session."""
        if stream_id not in self.active_streams:
            return False
        
        session = self.active_streams[stream_id]
        session.status = StreamStatus.COMPLETED
        session.completed_at = datetime.utcnow()
        
        # Close buffer
        buffer = self.stream_buffers[stream_id]
        await buffer.close()
        
        logger.info(f"Completed stream {stream_id}")
        
        # Record metrics
        duration = (session.completed_at - session.started_at).total_seconds() if session.started_at else 0
        metrics_collector.record_histogram(
            "stream_duration_seconds",
            duration,
            {"stream_type": session.stream_type.value, "status": "completed"}
        )
        
        return True
    
    async def error_stream(self, stream_id: str, error_message: str) -> bool:
        """Mark stream as error."""
        if stream_id not in self.active_streams:
            return False
        
        session = self.active_streams[stream_id]
        session.status = StreamStatus.ERROR
        session.error_message = error_message
        session.completed_at = datetime.utcnow()
        
        # Close buffer
        buffer = self.stream_buffers[stream_id]
        await buffer.close()
        
        logger.error(f"Stream {stream_id} error: {error_message}")
        
        # Record metrics
        metrics_collector.record_counter(
            "stream_errors_total",
            1.0,
            {"stream_type": session.stream_type.value, "error_type": "stream_error"}
        )
        
        return True
    
    async def cancel_stream(self, stream_id: str) -> bool:
        """Cancel a stream session."""
        if stream_id not in self.active_streams:
            return False
        
        session = self.active_streams[stream_id]
        session.status = StreamStatus.CANCELLED
        session.completed_at = datetime.utcnow()
        
        # Close buffer
        buffer = self.stream_buffers[stream_id]
        await buffer.close()
        
        logger.info(f"Cancelled stream {stream_id}")
        return True
    
    async def pause_stream(self, stream_id: str) -> bool:
        """Pause a stream session."""
        if stream_id not in self.active_streams:
            return False
        
        session = self.active_streams[stream_id]
        if session.status == StreamStatus.ACTIVE:
            session.status = StreamStatus.PAUSED
            logger.info(f"Paused stream {stream_id}")
            return True
        
        return False
    
    async def resume_stream(self, stream_id: str) -> bool:
        """Resume a paused stream session."""
        if stream_id not in self.active_streams:
            return False
        
        session = self.active_streams[stream_id]
        if session.status == StreamStatus.PAUSED:
            session.status = StreamStatus.ACTIVE
            logger.info(f"Resumed stream {stream_id}")
            return True
        
        return False
    
    def get_stream_session(self, stream_id: str) -> Optional[StreamSession]:
        """Get stream session information."""
        return self.active_streams.get(stream_id)
    
    def get_client_streams(self, client_id: str) -> List[StreamSession]:
        """Get all streams for a client."""
        stream_ids = self.client_streams.get(client_id, [])
        return [self.active_streams[sid] for sid in stream_ids if sid in self.active_streams]
    
    def get_active_streams(self) -> List[StreamSession]:
        """Get all active streams."""
        return [s for s in self.active_streams.values() if s.status == StreamStatus.ACTIVE]
    
    def get_streaming_stats(self) -> Dict[str, Any]:
        """Get streaming statistics."""
        active_streams = len([s for s in self.active_streams.values() if s.status == StreamStatus.ACTIVE])
        
        # Calculate average stream duration
        completed_streams = [s for s in self.active_streams.values() if s.status == StreamStatus.COMPLETED]
        avg_duration = 0.0
        if completed_streams:
            durations = []
            for stream in completed_streams:
                if stream.started_at and stream.completed_at:
                    duration = (stream.completed_at - stream.started_at).total_seconds()
                    durations.append(duration)
            
            if durations:
                avg_duration = sum(durations) / len(durations)
        
        return {
            "total_streams_created": self.total_streams_created,
            "active_streams": active_streams,
            "total_streams": len(self.active_streams),
            "total_chunks_sent": self.total_chunks_sent,
            "total_bytes_sent": self.total_bytes_sent,
            "average_stream_duration_seconds": avg_duration,
            "streams_by_type": {
                stream_type.value: len([
                    s for s in self.active_streams.values() 
                    if s.stream_type == stream_type
                ])
                for stream_type in StreamType
            },
            "streams_by_status": {
                status.value: len([
                    s for s in self.active_streams.values() 
                    if s.status == status
                ])
                for status in StreamStatus
            }
        }
    
    async def _heartbeat_loop(self):
        """Background heartbeat loop."""
        while True:
            try:
                await asyncio.sleep(self.config.heartbeat_interval)
                
                # Send heartbeat to active streams
                for stream_id, session in self.active_streams.items():
                    if session.status == StreamStatus.ACTIVE:
                        await self.send_chunk(
                            stream_id,
                            "heartbeat",
                            {"timestamp": datetime.utcnow().isoformat()},
                            metadata={"heartbeat": True}
                        )
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Heartbeat loop error: {e}")
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(60)  # Cleanup every minute
                
                current_time = datetime.utcnow()
                timeout_threshold = current_time - timedelta(seconds=self.config.timeout_seconds)
                
                # Find timed out streams
                timed_out_streams = []
                for stream_id, session in self.active_streams.items():
                    if (session.status == StreamStatus.ACTIVE and 
                        session.started_at and 
                        session.started_at < timeout_threshold):
                        timed_out_streams.append(stream_id)
                
                # Cancel timed out streams
                for stream_id in timed_out_streams:
                    await self.cancel_stream(stream_id)
                    logger.warning(f"Stream {stream_id} timed out and was cancelled")
                
                # Clean up completed streams older than 1 hour
                cleanup_threshold = current_time - timedelta(hours=1)
                streams_to_remove = []
                
                for stream_id, session in self.active_streams.items():
                    if (session.status in [StreamStatus.COMPLETED, StreamStatus.ERROR, StreamStatus.CANCELLED] and
                        session.completed_at and
                        session.completed_at < cleanup_threshold):
                        streams_to_remove.append(stream_id)
                
                # Remove old streams
                for stream_id in streams_to_remove:
                    session = self.active_streams[stream_id]
                    
                    # Remove from client tracking
                    if session.client_id:
                        client_streams = self.client_streams[session.client_id]
                        if stream_id in client_streams:
                            client_streams.remove(stream_id)
                    
                    # Remove stream data
                    del self.active_streams[stream_id]
                    if stream_id in self.stream_buffers:
                        del self.stream_buffers[stream_id]
                    if stream_id in self.stream_generators:
                        del self.stream_generators[stream_id]
                    
                    logger.debug(f"Cleaned up old stream: {stream_id}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
    
    async def shutdown(self):
        """Shutdown streaming manager."""
        # Cancel background tasks
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Cancel all active streams
        for stream_id in list(self.active_streams.keys()):
            await self.cancel_stream(stream_id)
        
        logger.info("Streaming manager shutdown completed")


class CollaborationManager:
    """Real-time collaboration features manager."""

    def __init__(self, streaming_manager: StreamingManager):
        self.streaming_manager = streaming_manager

        # Collaboration sessions
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.session_participants: Dict[str, List[str]] = {}  # session_id -> client_ids
        self.client_sessions: Dict[str, str] = {}  # client_id -> session_id

        # Shared documents/contexts
        self.shared_contexts: Dict[str, Dict[str, Any]] = {}
        self.context_subscribers: Dict[str, List[str]] = {}

        logger.info("Collaboration manager initialized")

    async def create_collaboration_session(
        self,
        session_name: str,
        creator_client_id: str,
        session_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new collaboration session."""
        session_id = str(uuid.uuid4())

        session = {
            "id": session_id,
            "name": session_name,
            "creator": creator_client_id,
            "created_at": datetime.utcnow(),
            "config": session_config or {},
            "status": "active",
            "shared_context": {},
            "activity_log": []
        }

        # Store session
        self.sessions[session_id] = session
        self.session_participants[session_id] = [creator_client_id]
        self.client_sessions[creator_client_id] = session_id

        # Create shared context
        context_id = f"session_{session_id}_context"
        self.shared_contexts[context_id] = {
            "session_id": session_id,
            "content": {},
            "version": 1,
            "last_updated": datetime.utcnow(),
            "last_updated_by": creator_client_id
        }
        self.context_subscribers[context_id] = [creator_client_id]

        # Log activity
        await self._log_session_activity(
            session_id,
            "session_created",
            creator_client_id,
            {"session_name": session_name}
        )

        logger.info(f"Created collaboration session {session_id}: {session_name}")
        return session_id

    async def join_collaboration_session(
        self,
        session_id: str,
        client_id: str
    ) -> bool:
        """Join an existing collaboration session."""
        if session_id not in self.sessions:
            return False

        # Add participant
        if client_id not in self.session_participants[session_id]:
            self.session_participants[session_id].append(client_id)

        self.client_sessions[client_id] = session_id

        # Subscribe to shared context
        context_id = f"session_{session_id}_context"
        if context_id in self.context_subscribers:
            if client_id not in self.context_subscribers[context_id]:
                self.context_subscribers[context_id].append(client_id)

        # Notify other participants
        await self._broadcast_to_session(
            session_id,
            "participant_joined",
            {
                "client_id": client_id,
                "timestamp": datetime.utcnow().isoformat()
            },
            exclude_client=client_id
        )

        # Log activity
        await self._log_session_activity(
            session_id,
            "participant_joined",
            client_id,
            {}
        )

        logger.info(f"Client {client_id} joined collaboration session {session_id}")
        return True

    async def leave_collaboration_session(
        self,
        client_id: str
    ) -> bool:
        """Leave current collaboration session."""
        if client_id not in self.client_sessions:
            return False

        session_id = self.client_sessions[client_id]

        # Remove participant
        if session_id in self.session_participants:
            if client_id in self.session_participants[session_id]:
                self.session_participants[session_id].remove(client_id)

        del self.client_sessions[client_id]

        # Unsubscribe from shared context
        context_id = f"session_{session_id}_context"
        if context_id in self.context_subscribers:
            if client_id in self.context_subscribers[context_id]:
                self.context_subscribers[context_id].remove(client_id)

        # Notify other participants
        await self._broadcast_to_session(
            session_id,
            "participant_left",
            {
                "client_id": client_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

        # Log activity
        await self._log_session_activity(
            session_id,
            "participant_left",
            client_id,
            {}
        )

        # Close session if no participants left
        if not self.session_participants[session_id]:
            await self._close_session(session_id)

        logger.info(f"Client {client_id} left collaboration session {session_id}")
        return True

    async def update_shared_context(
        self,
        session_id: str,
        client_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update shared context in collaboration session."""
        if session_id not in self.sessions:
            return False

        context_id = f"session_{session_id}_context"
        if context_id not in self.shared_contexts:
            return False

        context = self.shared_contexts[context_id]

        # Apply updates
        for key, value in updates.items():
            context["content"][key] = value

        # Update metadata
        context["version"] += 1
        context["last_updated"] = datetime.utcnow()
        context["last_updated_by"] = client_id

        # Broadcast update to all participants
        await self._broadcast_to_session(
            session_id,
            "context_updated",
            {
                "updates": updates,
                "version": context["version"],
                "updated_by": client_id,
                "timestamp": context["last_updated"].isoformat()
            },
            exclude_client=client_id
        )

        # Log activity
        await self._log_session_activity(
            session_id,
            "context_updated",
            client_id,
            {"updates": list(updates.keys())}
        )

        return True

    async def get_shared_context(
        self,
        session_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get current shared context."""
        context_id = f"session_{session_id}_context"
        return self.shared_contexts.get(context_id)

    async def start_collaborative_stream(
        self,
        session_id: str,
        stream_type: StreamType,
        initiator_client_id: str,
        stream_config: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Start collaborative streaming session."""
        if session_id not in self.sessions:
            return None

        # Create stream
        stream_id = await self.streaming_manager.create_stream(
            stream_type=stream_type,
            client_id=initiator_client_id,
            metadata={
                "collaboration_session": session_id,
                "collaborative": True,
                **(stream_config or {})
            }
        )

        # Start stream
        await self.streaming_manager.start_stream(stream_id)

        # Notify all participants
        await self._broadcast_to_session(
            session_id,
            "collaborative_stream_started",
            {
                "stream_id": stream_id,
                "stream_type": stream_type.value,
                "initiator": initiator_client_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

        # Log activity
        await self._log_session_activity(
            session_id,
            "collaborative_stream_started",
            initiator_client_id,
            {"stream_id": stream_id, "stream_type": stream_type.value}
        )

        return stream_id

    async def _broadcast_to_session(
        self,
        session_id: str,
        event_type: str,
        data: Dict[str, Any],
        exclude_client: Optional[str] = None
    ):
        """Broadcast message to all session participants."""
        if session_id not in self.session_participants:
            return

        participants = self.session_participants[session_id]

        for client_id in participants:
            if exclude_client and client_id == exclude_client:
                continue

            # Send via streaming manager
            await self.streaming_manager.send_chunk(
                f"collab_{client_id}",  # Use collaboration stream
                "collaboration_event",
                {
                    "session_id": session_id,
                    "event_type": event_type,
                    "data": data
                }
            )

    async def _log_session_activity(
        self,
        session_id: str,
        activity_type: str,
        client_id: str,
        details: Dict[str, Any]
    ):
        """Log activity in collaboration session."""
        if session_id not in self.sessions:
            return

        activity = {
            "type": activity_type,
            "client_id": client_id,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details
        }

        self.sessions[session_id]["activity_log"].append(activity)

        # Keep only last 100 activities
        if len(self.sessions[session_id]["activity_log"]) > 100:
            self.sessions[session_id]["activity_log"] = \
                self.sessions[session_id]["activity_log"][-100:]

    async def _close_session(self, session_id: str):
        """Close collaboration session."""
        if session_id not in self.sessions:
            return

        # Update session status
        self.sessions[session_id]["status"] = "closed"
        self.sessions[session_id]["closed_at"] = datetime.utcnow()

        # Clean up
        if session_id in self.session_participants:
            del self.session_participants[session_id]

        context_id = f"session_{session_id}_context"
        if context_id in self.shared_contexts:
            del self.shared_contexts[context_id]
        if context_id in self.context_subscribers:
            del self.context_subscribers[context_id]

        logger.info(f"Closed collaboration session {session_id}")

    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get collaboration session information."""
        if session_id not in self.sessions:
            return None

        session = self.sessions[session_id]
        participants = self.session_participants.get(session_id, [])

        return {
            **session,
            "participants": participants,
            "participant_count": len(participants)
        }

    def get_client_session(self, client_id: str) -> Optional[str]:
        """Get client's current collaboration session."""
        return self.client_sessions.get(client_id)

    def get_collaboration_stats(self) -> Dict[str, Any]:
        """Get collaboration statistics."""
        active_sessions = len([
            s for s in self.sessions.values()
            if s["status"] == "active"
        ])

        total_participants = sum(
            len(participants)
            for participants in self.session_participants.values()
        )

        return {
            "total_sessions": len(self.sessions),
            "active_sessions": active_sessions,
            "total_participants": total_participants,
            "shared_contexts": len(self.shared_contexts)
        }


# Global streaming manager instance
streaming_manager = StreamingManager()

# Global collaboration manager instance
collaboration_manager = CollaborationManager(streaming_manager)
