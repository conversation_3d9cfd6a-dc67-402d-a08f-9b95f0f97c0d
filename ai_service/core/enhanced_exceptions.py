"""
Enhanced Exception Handling for Production AI Service.
Provides structured error hierarchy, recovery strategies, and comprehensive logging.
"""

import time
import traceback
import logging
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from functools import wraps
import asyncio

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    RATE_LIMIT = "rate_limit"
    API_ERROR = "api_error"
    NETWORK = "network"
    TIMEOUT = "timeout"
    RESOURCE = "resource"
    INTERNAL = "internal"
    EXTERNAL = "external"


@dataclass
class ErrorContext:
    """Additional context for errors."""
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    endpoint: Optional[str] = None
    model: Optional[str] = None
    input_size: Optional[int] = None
    retry_count: int = 0
    correlation_id: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


class EnhancedAIException(Exception):
    """Base enhanced exception class with comprehensive error information."""
    
    def __init__(
        self,
        message: str,
        error_code: str,
        category: ErrorCategory = ErrorCategory.INTERNAL,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[ErrorContext] = None,
        retry_after: Optional[int] = None,
        is_retryable: bool = False,
        user_message: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.context = context or ErrorContext()
        self.retry_after = retry_after
        self.is_retryable = is_retryable
        self.user_message = user_message or self._generate_user_message()
        self.timestamp = time.time()
        self.traceback = traceback.format_exc()
    
    def _generate_user_message(self) -> str:
        """Generate user-friendly error message."""
        if self.category == ErrorCategory.VALIDATION:
            return "Invalid input provided. Please check your request and try again."
        elif self.category == ErrorCategory.RATE_LIMIT:
            return "Too many requests. Please wait a moment and try again."
        elif self.category == ErrorCategory.TIMEOUT:
            return "Request timed out. Please try again."
        elif self.category == ErrorCategory.NETWORK:
            return "Network error occurred. Please check your connection and try again."
        else:
            return "An error occurred while processing your request. Please try again."
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": self.user_message,
            "error_code": self.error_code,
            "category": self.category.value,
            "severity": self.severity.value,
            "details": self.details,
            "retry_after": self.retry_after,
            "is_retryable": self.is_retryable,
            "timestamp": self.timestamp,
            "context": {
                "request_id": self.context.request_id,
                "endpoint": self.context.endpoint,
                "retry_count": self.context.retry_count
            } if self.context else None
        }
    
    def to_log_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging."""
        return {
            "error_message": self.message,
            "error_code": self.error_code,
            "category": self.category.value,
            "severity": self.severity.value,
            "details": self.details,
            "context": self.context.__dict__ if self.context else None,
            "traceback": self.traceback,
            "timestamp": self.timestamp
        }


# Specific Exception Classes
class ValidationException(EnhancedAIException):
    """Exception for validation errors."""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            details={"field": field} if field else {},
            **kwargs
        )


class RateLimitException(EnhancedAIException):
    """Exception for rate limiting."""
    
    def __init__(self, message: str, retry_after: int = 60, **kwargs):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            category=ErrorCategory.RATE_LIMIT,
            severity=ErrorSeverity.MEDIUM,
            retry_after=retry_after,
            is_retryable=True,
            **kwargs
        )


class APIException(EnhancedAIException):
    """Exception for external API errors."""
    
    def __init__(self, message: str, api_name: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="EXTERNAL_API_ERROR",
            category=ErrorCategory.API_ERROR,
            severity=ErrorSeverity.HIGH,
            details={"api_name": api_name, "status_code": status_code},
            is_retryable=status_code in [429, 502, 503, 504] if status_code else False,
            **kwargs
        )


class TimeoutException(EnhancedAIException):
    """Exception for timeout errors."""
    
    def __init__(self, message: str, timeout_duration: float, **kwargs):
        super().__init__(
            message=message,
            error_code="TIMEOUT_ERROR",
            category=ErrorCategory.TIMEOUT,
            severity=ErrorSeverity.MEDIUM,
            details={"timeout_duration": timeout_duration},
            is_retryable=True,
            **kwargs
        )


class ResourceException(EnhancedAIException):
    """Exception for resource-related errors."""
    
    def __init__(self, message: str, resource_type: str, **kwargs):
        super().__init__(
            message=message,
            error_code="RESOURCE_ERROR",
            category=ErrorCategory.RESOURCE,
            severity=ErrorSeverity.HIGH,
            details={"resource_type": resource_type},
            **kwargs
        )


# Error Recovery Strategies
class RetryStrategy:
    """Retry strategy configuration."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt."""
        delay = self.base_delay * (self.exponential_base ** (attempt - 1))
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay


def with_retry(strategy: RetryStrategy = None):
    """Decorator for automatic retry with exponential backoff."""
    if strategy is None:
        strategy = RetryStrategy()
    
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, strategy.max_attempts + 1):
                try:
                    return await func(*args, **kwargs)
                except EnhancedAIException as e:
                    last_exception = e
                    e.context.retry_count = attempt
                    
                    if not e.is_retryable or attempt == strategy.max_attempts:
                        logger.error(f"Function {func.__name__} failed after {attempt} attempts", 
                                   extra=e.to_log_dict())
                        raise
                    
                    delay = strategy.get_delay(attempt)
                    logger.warning(f"Function {func.__name__} failed on attempt {attempt}, "
                                 f"retrying in {delay:.2f}s: {e.message}")
                    await asyncio.sleep(delay)
                except Exception as e:
                    # Convert unknown exceptions to EnhancedAIException
                    enhanced_error = EnhancedAIException(
                        message=str(e),
                        error_code="UNKNOWN_ERROR",
                        category=ErrorCategory.INTERNAL,
                        severity=ErrorSeverity.HIGH
                    )
                    enhanced_error.context.retry_count = attempt
                    
                    logger.error(f"Function {func.__name__} failed with unknown error on attempt {attempt}",
                               extra=enhanced_error.to_log_dict())
                    raise enhanced_error
            
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, strategy.max_attempts + 1):
                try:
                    return func(*args, **kwargs)
                except EnhancedAIException as e:
                    last_exception = e
                    e.context.retry_count = attempt
                    
                    if not e.is_retryable or attempt == strategy.max_attempts:
                        logger.error(f"Function {func.__name__} failed after {attempt} attempts",
                                   extra=e.to_log_dict())
                        raise
                    
                    delay = strategy.get_delay(attempt)
                    logger.warning(f"Function {func.__name__} failed on attempt {attempt}, "
                                 f"retrying in {delay:.2f}s: {e.message}")
                    time.sleep(delay)
                except Exception as e:
                    enhanced_error = EnhancedAIException(
                        message=str(e),
                        error_code="UNKNOWN_ERROR",
                        category=ErrorCategory.INTERNAL,
                        severity=ErrorSeverity.HIGH
                    )
                    enhanced_error.context.retry_count = attempt
                    
                    logger.error(f"Function {func.__name__} failed with unknown error on attempt {attempt}",
                               extra=enhanced_error.to_log_dict())
                    raise enhanced_error
            
            raise last_exception
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


# Error Handler Utilities
class ErrorHandler:
    """Centralized error handling utilities."""
    
    @staticmethod
    def classify_error(error: Exception) -> ErrorCategory:
        """Classify an error into appropriate category."""
        error_str = str(error).lower()
        
        if "validation" in error_str or "invalid" in error_str:
            return ErrorCategory.VALIDATION
        elif "rate limit" in error_str or "quota" in error_str:
            return ErrorCategory.RATE_LIMIT
        elif "timeout" in error_str:
            return ErrorCategory.TIMEOUT
        elif "network" in error_str or "connection" in error_str:
            return ErrorCategory.NETWORK
        elif "api" in error_str:
            return ErrorCategory.API_ERROR
        else:
            return ErrorCategory.INTERNAL
    
    @staticmethod
    def is_retryable(error: Exception) -> bool:
        """Determine if an error is retryable."""
        if isinstance(error, EnhancedAIException):
            return error.is_retryable
        
        error_str = str(error).lower()
        retryable_patterns = [
            "rate limit", "quota", "timeout", "network", "connection",
            "502", "503", "504", "temporary", "unavailable"
        ]
        
        return any(pattern in error_str for pattern in retryable_patterns)
    
    @staticmethod
    def enhance_exception(
        error: Exception,
        context: Optional[ErrorContext] = None
    ) -> EnhancedAIException:
        """Convert a regular exception to an enhanced exception."""
        if isinstance(error, EnhancedAIException):
            if context:
                error.context = context
            return error
        
        category = ErrorHandler.classify_error(error)
        is_retryable = ErrorHandler.is_retryable(error)
        
        return EnhancedAIException(
            message=str(error),
            error_code=f"{category.value.upper()}_ERROR",
            category=category,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            is_retryable=is_retryable
        )


# Global error handler instance
error_handler = ErrorHandler()
