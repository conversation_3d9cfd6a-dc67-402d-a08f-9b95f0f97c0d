"""
Input validation and sanitization for the AI service.
"""
import re
import logging
from typing import Dict, List, Any, Optional, Union
from ai_service.core.exceptions import ValidationError, ErrorContext
from ai_service.core.error_handler import validation_error
from ai_service.config.settings import settings

logger = logging.getLogger(__name__)


class RequestValidator:
    """Validator for API requests."""
    
    # Maximum lengths for various inputs
    MAX_PROMPT_LENGTH = 100000
    MAX_MESSAGE_LENGTH = 50000
    MAX_SYSTEM_INSTRUCTION_LENGTH = 10000
    MAX_FUNCTION_NAME_LENGTH = 100
    MAX_FUNCTION_DESCRIPTION_LENGTH = 1000
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    # Allowed file types
    ALLOWED_IMAGE_TYPES = {'image/jpeg', 'image/png', 'image/gif', 'image/webp'}
    ALLOWED_DOCUMENT_TYPES = {'application/pdf', 'text/plain', 'application/msword'}
    ALLOWED_AUDIO_TYPES = {'audio/mpeg', 'audio/wav', 'audio/ogg'}
    
    @staticmethod
    def validate_prompt(prompt: str) -> str:
        """Validate and sanitize a text prompt.
        
        Args:
            prompt: The prompt to validate
            
        Returns:
            Sanitized prompt
            
        Raises:
            ValidationError: If prompt is invalid
        """
        if not prompt:
            raise ValidationError("Prompt cannot be empty", field="prompt")
        
        if not isinstance(prompt, str):
            raise ValidationError("Prompt must be a string", field="prompt")
        
        # Strip whitespace
        prompt = prompt.strip()
        
        if not prompt:
            raise ValidationError("Prompt cannot be empty after trimming", field="prompt")
        
        if len(prompt) > RequestValidator.MAX_PROMPT_LENGTH:
            raise ValidationError(
                f"Prompt is too long (max {RequestValidator.MAX_PROMPT_LENGTH} characters)",
                field="prompt",
                details={"max_length": RequestValidator.MAX_PROMPT_LENGTH, "actual_length": len(prompt)}
            )
        
        # Basic content filtering (can be expanded)
        if RequestValidator._contains_suspicious_content(prompt):
            raise ValidationError("Prompt contains potentially harmful content", field="prompt")
        
        return prompt
    
    @staticmethod
    def validate_model(model: str) -> str:
        """Validate model name.
        
        Args:
            model: Model name to validate
            
        Returns:
            Validated model name
            
        Raises:
            ValidationError: If model is invalid
        """
        if not model:
            raise ValidationError("Model cannot be empty", field="model")
        
        if not isinstance(model, str):
            raise ValidationError("Model must be a string", field="model")
        
        if model not in settings.available_models:
            raise ValidationError(
                f"Model '{model}' is not available",
                field="model",
                details={"available_models": list(settings.available_models.keys())}
            )
        
        return model
    
    @staticmethod
    def validate_generation_params(
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        top_k: Optional[int] = None,
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """Validate generation parameters.
        
        Args:
            temperature: Temperature parameter
            top_p: Top-p parameter
            top_k: Top-k parameter
            max_tokens: Maximum tokens
            
        Returns:
            Dictionary of validated parameters
            
        Raises:
            ValidationError: If any parameter is invalid
        """
        validated = {}
        
        if temperature is not None:
            if not isinstance(temperature, (int, float)):
                raise ValidationError("Temperature must be a number", field="temperature")
            if not (0.0 <= temperature <= 2.0):
                raise ValidationError("Temperature must be between 0.0 and 2.0", field="temperature")
            validated["temperature"] = float(temperature)
        
        if top_p is not None:
            if not isinstance(top_p, (int, float)):
                raise ValidationError("Top-p must be a number", field="top_p")
            if not (0.0 <= top_p <= 1.0):
                raise ValidationError("Top-p must be between 0.0 and 1.0", field="top_p")
            validated["top_p"] = float(top_p)
        
        if top_k is not None:
            if not isinstance(top_k, int):
                raise ValidationError("Top-k must be an integer", field="top_k")
            if top_k < 1:
                raise ValidationError("Top-k must be at least 1", field="top_k")
            validated["top_k"] = top_k
        
        if max_tokens is not None:
            if not isinstance(max_tokens, int):
                raise ValidationError("Max tokens must be an integer", field="max_tokens")
            if max_tokens < 1:
                raise ValidationError("Max tokens must be at least 1", field="max_tokens")
            if max_tokens > 32768:  # Reasonable upper limit
                raise ValidationError("Max tokens cannot exceed 32768", field="max_tokens")
            validated["max_tokens"] = max_tokens
        
        return validated
    
    @staticmethod
    def validate_messages(messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Validate chat messages.
        
        Args:
            messages: List of chat messages
            
        Returns:
            Validated messages
            
        Raises:
            ValidationError: If messages are invalid
        """
        if not messages:
            raise ValidationError("Messages cannot be empty", field="messages")
        
        if not isinstance(messages, list):
            raise ValidationError("Messages must be a list", field="messages")
        
        if len(messages) > 100:  # Reasonable limit
            raise ValidationError("Too many messages (max 100)", field="messages")
        
        validated_messages = []
        
        for i, message in enumerate(messages):
            if not isinstance(message, dict):
                raise ValidationError(f"Message {i} must be a dictionary", field=f"messages[{i}]")
            
            if "role" not in message:
                raise ValidationError(f"Message {i} missing 'role' field", field=f"messages[{i}].role")
            
            if "content" not in message:
                raise ValidationError(f"Message {i} missing 'content' field", field=f"messages[{i}].content")
            
            role = message["role"]
            content = message["content"]
            
            if role not in ["user", "assistant", "system"]:
                raise ValidationError(
                    f"Message {i} has invalid role '{role}'",
                    field=f"messages[{i}].role",
                    details={"allowed_roles": ["user", "assistant", "system"]}
                )
            
            if not isinstance(content, str):
                raise ValidationError(f"Message {i} content must be a string", field=f"messages[{i}].content")
            
            content = content.strip()
            if not content:
                raise ValidationError(f"Message {i} content cannot be empty", field=f"messages[{i}].content")
            
            if len(content) > RequestValidator.MAX_MESSAGE_LENGTH:
                raise ValidationError(
                    f"Message {i} content is too long (max {RequestValidator.MAX_MESSAGE_LENGTH} characters)",
                    field=f"messages[{i}].content",
                    details={"max_length": RequestValidator.MAX_MESSAGE_LENGTH, "actual_length": len(content)}
                )
            
            validated_messages.append({
                "role": role,
                "content": content
            })
        
        return validated_messages
    
    @staticmethod
    def validate_file_upload(file_data: bytes, mime_type: str, filename: str) -> Dict[str, Any]:
        """Validate file upload.
        
        Args:
            file_data: File content as bytes
            mime_type: MIME type of the file
            filename: Name of the file
            
        Returns:
            Validation result
            
        Raises:
            ValidationError: If file is invalid
        """
        if not file_data:
            raise ValidationError("File data cannot be empty", field="file")
        
        if len(file_data) > RequestValidator.MAX_FILE_SIZE:
            raise ValidationError(
                f"File is too large (max {RequestValidator.MAX_FILE_SIZE // (1024*1024)}MB)",
                field="file",
                details={"max_size": RequestValidator.MAX_FILE_SIZE, "actual_size": len(file_data)}
            )
        
        if not mime_type:
            raise ValidationError("MIME type cannot be empty", field="mime_type")
        
        allowed_types = (
            RequestValidator.ALLOWED_IMAGE_TYPES |
            RequestValidator.ALLOWED_DOCUMENT_TYPES |
            RequestValidator.ALLOWED_AUDIO_TYPES
        )
        
        if mime_type not in allowed_types:
            raise ValidationError(
                f"File type '{mime_type}' is not allowed",
                field="mime_type",
                details={"allowed_types": list(allowed_types)}
            )
        
        if not filename:
            raise ValidationError("Filename cannot be empty", field="filename")
        
        # Basic filename validation
        if len(filename) > 255:
            raise ValidationError("Filename is too long (max 255 characters)", field="filename")
        
        # Check for suspicious filename patterns
        if re.search(r'[<>:"/\\|?*]', filename):
            raise ValidationError("Filename contains invalid characters", field="filename")
        
        return {
            "size": len(file_data),
            "mime_type": mime_type,
            "filename": filename,
            "is_image": mime_type in RequestValidator.ALLOWED_IMAGE_TYPES,
            "is_document": mime_type in RequestValidator.ALLOWED_DOCUMENT_TYPES,
            "is_audio": mime_type in RequestValidator.ALLOWED_AUDIO_TYPES
        }
    
    @staticmethod
    def _contains_suspicious_content(text: str) -> bool:
        """Check if text contains potentially harmful content.
        
        Args:
            text: Text to check
            
        Returns:
            True if suspicious content is found
        """
        # Basic patterns for suspicious content
        suspicious_patterns = [
            r'<script[^>]*>',  # Script tags
            r'javascript:',     # JavaScript URLs
            r'data:text/html',  # Data URLs with HTML
            r'vbscript:',       # VBScript URLs
        ]
        
        text_lower = text.lower()
        
        for pattern in suspicious_patterns:
            if re.search(pattern, text_lower):
                logger.warning(f"Suspicious content detected: {pattern}")
                return True
        
        return False
    
    @staticmethod
    def sanitize_text(text: str) -> str:
        """Sanitize text input.
        
        Args:
            text: Text to sanitize
            
        Returns:
            Sanitized text
        """
        if not text:
            return ""
        
        # Remove null bytes
        text = text.replace('\x00', '')
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        return text
