"""
Model Ensembling Engine for Phase 8.
Combines outputs from multiple AI models for improved quality and reliability.
"""

import asyncio
import logging
import time
import json
import statistics
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import difflib

logger = logging.getLogger(__name__)


class EnsemblingStrategy(Enum):
    """Model ensembling strategies."""
    VOTING = "voting"                    # Majority voting
    WEIGHTED_AVERAGE = "weighted_average" # Weighted by model performance
    QUALITY_SELECTION = "quality_selection" # Select highest quality output
    CONSENSUS = "consensus"              # Require consensus between models
    HYBRID = "hybrid"                    # Combine multiple strategies
    DYNAMIC = "dynamic"                  # ML-driven strategy selection


class OutputType(Enum):
    """Types of model outputs."""
    TEXT = "text"
    STRUCTURED_DATA = "structured_data"
    EMBEDDINGS = "embeddings"
    CLASSIFICATION = "classification"
    GENERATION = "generation"


class QualityMetric(Enum):
    """Quality assessment metrics."""
    COHERENCE = "coherence"
    RELEVANCE = "relevance"
    ACCURACY = "accuracy"
    COMPLETENESS = "completeness"
    CONSISTENCY = "consistency"
    CREATIVITY = "creativity"


@dataclass
class ModelOutput:
    """Output from a single model."""
    model_id: str
    provider: str
    content: Any
    
    # Quality metrics
    confidence: float
    quality_scores: Dict[QualityMetric, float] = field(default_factory=dict)
    
    # Performance metrics
    response_time: float = 0.0
    tokens_used: int = 0
    cost: float = 0.0
    
    # Metadata
    timestamp: float = field(default_factory=time.time)
    model_version: str = "unknown"
    parameters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EnsembleRequest:
    """Request for model ensembling."""
    request_id: str
    prompt: str
    capability: str
    output_type: OutputType
    
    # Ensembling configuration
    strategy: EnsemblingStrategy
    models: List[str]  # Model IDs to use
    min_models: int = 2
    max_models: int = 5
    
    # Quality requirements
    min_confidence: float = 0.7
    required_consensus: float = 0.6
    quality_weights: Dict[QualityMetric, float] = field(default_factory=dict)
    
    # Performance constraints
    max_response_time: float = 10.0
    max_cost: float = 0.1
    
    timestamp: float = field(default_factory=time.time)


@dataclass
class EnsembleResult:
    """Result from model ensembling."""
    request_id: str
    final_output: Any
    strategy_used: EnsemblingStrategy
    
    # Individual outputs
    model_outputs: List[ModelOutput]
    
    # Quality assessment
    ensemble_confidence: float
    quality_scores: Dict[QualityMetric, float]
    consensus_score: float
    
    # Performance metrics
    total_response_time: float
    total_cost: float
    models_used: int
    
    # Decision details
    selection_reasoning: str
    quality_analysis: Dict[str, Any]
    
    timestamp: float = field(default_factory=time.time)


class QualityAssessor:
    """Assesses quality of model outputs."""
    
    def __init__(self):
        self.quality_patterns = {
            QualityMetric.COHERENCE: [
                "logical flow", "clear structure", "consistent narrative"
            ],
            QualityMetric.RELEVANCE: [
                "addresses prompt", "on topic", "relevant information"
            ],
            QualityMetric.COMPLETENESS: [
                "comprehensive", "thorough", "complete answer"
            ]
        }
    
    def assess_text_quality(self, text: str, prompt: str) -> Dict[QualityMetric, float]:
        """Assess quality of text output."""
        scores = {}
        
        # Coherence assessment
        scores[QualityMetric.COHERENCE] = self._assess_coherence(text)
        
        # Relevance assessment
        scores[QualityMetric.RELEVANCE] = self._assess_relevance(text, prompt)
        
        # Completeness assessment
        scores[QualityMetric.COMPLETENESS] = self._assess_completeness(text, prompt)
        
        # Consistency assessment
        scores[QualityMetric.CONSISTENCY] = self._assess_consistency(text)
        
        return scores
    
    def _assess_coherence(self, text: str) -> float:
        """Assess text coherence."""
        if not text:
            return 0.0
        
        # Simple heuristics for coherence
        sentences = text.split('.')
        if len(sentences) < 2:
            return 0.7  # Short text, assume coherent
        
        # Check for repeated words/phrases
        words = text.lower().split()
        unique_words = set(words)
        repetition_ratio = len(unique_words) / len(words) if words else 0
        
        # Check sentence length variation
        sentence_lengths = [len(s.split()) for s in sentences if s.strip()]
        if sentence_lengths:
            length_variance = statistics.stdev(sentence_lengths) if len(sentence_lengths) > 1 else 0
            length_score = min(1.0, length_variance / 10)  # Normalize
        else:
            length_score = 0.5
        
        coherence_score = (repetition_ratio + length_score) / 2
        return min(1.0, max(0.0, coherence_score))
    
    def _assess_relevance(self, text: str, prompt: str) -> float:
        """Assess relevance to prompt."""
        if not text or not prompt:
            return 0.0
        
        # Simple keyword overlap
        prompt_words = set(prompt.lower().split())
        text_words = set(text.lower().split())
        
        if not prompt_words:
            return 0.5
        
        overlap = len(prompt_words.intersection(text_words))
        relevance_score = overlap / len(prompt_words)
        
        return min(1.0, relevance_score)
    
    def _assess_completeness(self, text: str, prompt: str) -> float:
        """Assess completeness of response."""
        if not text:
            return 0.0
        
        # Simple length-based assessment
        prompt_length = len(prompt.split())
        text_length = len(text.split())
        
        # Expect response to be at least as long as prompt for completeness
        if prompt_length == 0:
            return 0.7
        
        length_ratio = text_length / prompt_length
        completeness_score = min(1.0, length_ratio / 2)  # Normalize
        
        return completeness_score
    
    def _assess_consistency(self, text: str) -> float:
        """Assess internal consistency."""
        if not text:
            return 0.0
        
        # Check for contradictions (simple approach)
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        
        if len(sentences) < 2:
            return 1.0  # Single sentence, assume consistent
        
        # Simple contradiction detection
        contradiction_indicators = ['but', 'however', 'although', 'despite', 'nevertheless']
        contradiction_count = sum(
            1 for sentence in sentences 
            for indicator in contradiction_indicators 
            if indicator in sentence.lower()
        )
        
        # Penalize excessive contradictions
        consistency_score = max(0.0, 1.0 - (contradiction_count / len(sentences)))
        
        return consistency_score


class ModelEnsemblingEngine:
    """Engine for combining multiple model outputs."""
    
    def __init__(self):
        self.quality_assessor = QualityAssessor()
        self.ensemble_history: List[EnsembleResult] = []
        self.model_performance: Dict[str, Dict[str, float]] = {}
        
        # Strategy weights (learned from performance)
        self.strategy_weights = {
            EnsemblingStrategy.VOTING: 0.8,
            EnsemblingStrategy.WEIGHTED_AVERAGE: 0.9,
            EnsemblingStrategy.QUALITY_SELECTION: 0.85,
            EnsemblingStrategy.CONSENSUS: 0.75,
            EnsemblingStrategy.HYBRID: 0.9
        }
        
        logger.info("Model Ensembling Engine initialized")
    
    async def ensemble_models(
        self,
        request: EnsembleRequest,
        model_outputs: List[ModelOutput]
    ) -> EnsembleResult:
        """Ensemble multiple model outputs."""
        start_time = time.time()
        
        try:
            # Filter valid outputs
            valid_outputs = [
                output for output in model_outputs
                if output.confidence >= request.min_confidence
            ]
            
            if len(valid_outputs) < request.min_models:
                # Not enough valid outputs, return best single output
                if model_outputs:
                    best_output = max(model_outputs, key=lambda x: x.confidence)
                    return self._create_single_output_result(request, best_output, start_time)
                else:
                    raise ValueError("No valid model outputs available")
            
            # Assess quality of all outputs
            for output in valid_outputs:
                if request.output_type == OutputType.TEXT:
                    output.quality_scores = self.quality_assessor.assess_text_quality(
                        str(output.content), request.prompt
                    )
            
            # Apply ensembling strategy
            if request.strategy == EnsemblingStrategy.VOTING:
                result = await self._voting_ensemble(request, valid_outputs)
            elif request.strategy == EnsemblingStrategy.WEIGHTED_AVERAGE:
                result = await self._weighted_average_ensemble(request, valid_outputs)
            elif request.strategy == EnsemblingStrategy.QUALITY_SELECTION:
                result = await self._quality_selection_ensemble(request, valid_outputs)
            elif request.strategy == EnsemblingStrategy.CONSENSUS:
                result = await self._consensus_ensemble(request, valid_outputs)
            elif request.strategy == EnsemblingStrategy.HYBRID:
                result = await self._hybrid_ensemble(request, valid_outputs)
            elif request.strategy == EnsemblingStrategy.DYNAMIC:
                result = await self._dynamic_ensemble(request, valid_outputs)
            else:
                # Default to quality selection
                result = await self._quality_selection_ensemble(request, valid_outputs)
            
            # Calculate final metrics
            result.total_response_time = time.time() - start_time
            result.total_cost = sum(output.cost for output in valid_outputs)
            result.models_used = len(valid_outputs)
            
            # Store result for learning
            self.ensemble_history.append(result)
            self._update_model_performance(valid_outputs, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in model ensembling: {e}")
            # Return best single output as fallback
            if model_outputs:
                best_output = max(model_outputs, key=lambda x: x.confidence)
                return self._create_single_output_result(request, best_output, start_time)
            else:
                raise
    
    async def _voting_ensemble(self, request: EnsembleRequest, outputs: List[ModelOutput]) -> EnsembleResult:
        """Ensemble using majority voting."""
        if request.output_type == OutputType.TEXT:
            # For text, find most similar outputs
            similarity_matrix = self._calculate_text_similarities(outputs)
            
            # Find output with highest average similarity to others
            avg_similarities = []
            for i, output in enumerate(outputs):
                avg_sim = statistics.mean([similarity_matrix[i][j] for j in range(len(outputs)) if i != j])
                avg_similarities.append(avg_sim)
            
            best_idx = avg_similarities.index(max(avg_similarities))
            selected_output = outputs[best_idx]
            
            consensus_score = max(avg_similarities)
            
        else:
            # For other types, select most confident
            selected_output = max(outputs, key=lambda x: x.confidence)
            consensus_score = selected_output.confidence
        
        return EnsembleResult(
            request_id=request.request_id,
            final_output=selected_output.content,
            strategy_used=EnsemblingStrategy.VOTING,
            model_outputs=outputs,
            ensemble_confidence=selected_output.confidence,
            quality_scores=selected_output.quality_scores,
            consensus_score=consensus_score,
            selection_reasoning=f"Selected output from {selected_output.model_id} via voting",
            quality_analysis={"voting_similarities": avg_similarities}
        )
    
    async def _weighted_average_ensemble(self, request: EnsembleRequest, outputs: List[ModelOutput]) -> EnsembleResult:
        """Ensemble using weighted average based on model performance."""
        # Calculate weights based on historical performance
        weights = []
        for output in outputs:
            model_perf = self.model_performance.get(output.model_id, {"quality": 0.8})
            weight = model_perf.get("quality", 0.8) * output.confidence
            weights.append(weight)
        
        # Normalize weights
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(outputs)] * len(outputs)
        
        if request.output_type == OutputType.TEXT:
            # For text, select highest weighted output (can't average text)
            best_idx = weights.index(max(weights))
            selected_output = outputs[best_idx]
            final_output = selected_output.content
            
        elif request.output_type == OutputType.EMBEDDINGS:
            # Average embeddings
            embeddings = [output.content for output in outputs if isinstance(output.content, list)]
            if embeddings:
                avg_embedding = [
                    sum(emb[i] * weights[j] for j, emb in enumerate(embeddings)) 
                    for i in range(len(embeddings[0]))
                ]
                final_output = avg_embedding
                selected_output = outputs[0]  # Use first for metadata
            else:
                selected_output = outputs[0]
                final_output = selected_output.content
        else:
            # Default to highest weighted
            best_idx = weights.index(max(weights))
            selected_output = outputs[best_idx]
            final_output = selected_output.content
        
        ensemble_confidence = sum(w * o.confidence for w, o in zip(weights, outputs))
        
        return EnsembleResult(
            request_id=request.request_id,
            final_output=final_output,
            strategy_used=EnsemblingStrategy.WEIGHTED_AVERAGE,
            model_outputs=outputs,
            ensemble_confidence=ensemble_confidence,
            quality_scores=selected_output.quality_scores,
            consensus_score=max(weights),
            selection_reasoning=f"Weighted average with weights: {weights}",
            quality_analysis={"weights": weights, "total_weight": total_weight}
        )
    
    async def _quality_selection_ensemble(self, request: EnsembleRequest, outputs: List[ModelOutput]) -> EnsembleResult:
        """Ensemble by selecting highest quality output."""
        # Calculate overall quality score for each output
        quality_scores = []
        for output in outputs:
            if output.quality_scores:
                # Weight quality metrics
                weights = request.quality_weights or {
                    QualityMetric.COHERENCE: 0.3,
                    QualityMetric.RELEVANCE: 0.4,
                    QualityMetric.COMPLETENESS: 0.2,
                    QualityMetric.CONSISTENCY: 0.1
                }
                
                overall_quality = sum(
                    output.quality_scores.get(metric, 0.5) * weight
                    for metric, weight in weights.items()
                )
            else:
                overall_quality = output.confidence
            
            quality_scores.append(overall_quality)
        
        # Select highest quality output
        best_idx = quality_scores.index(max(quality_scores))
        selected_output = outputs[best_idx]
        
        return EnsembleResult(
            request_id=request.request_id,
            final_output=selected_output.content,
            strategy_used=EnsemblingStrategy.QUALITY_SELECTION,
            model_outputs=outputs,
            ensemble_confidence=selected_output.confidence,
            quality_scores=selected_output.quality_scores,
            consensus_score=max(quality_scores),
            selection_reasoning=f"Selected highest quality output from {selected_output.model_id}",
            quality_analysis={"quality_scores": quality_scores}
        )
    
    async def _consensus_ensemble(self, request: EnsembleRequest, outputs: List[ModelOutput]) -> EnsembleResult:
        """Ensemble requiring consensus between models."""
        if request.output_type == OutputType.TEXT:
            # Calculate text similarities
            similarity_matrix = self._calculate_text_similarities(outputs)
            
            # Find outputs with high consensus
            consensus_threshold = request.required_consensus
            consensus_groups = []
            
            for i, output in enumerate(outputs):
                similar_outputs = [j for j in range(len(outputs)) 
                                if similarity_matrix[i][j] >= consensus_threshold]
                if len(similar_outputs) >= request.min_models:
                    consensus_groups.append((i, similar_outputs))
            
            if consensus_groups:
                # Select group with highest average quality
                best_group = max(consensus_groups, key=lambda x: statistics.mean([
                    sum(outputs[j].quality_scores.values()) / len(outputs[j].quality_scores)
                    if outputs[j].quality_scores else outputs[j].confidence
                    for j in x[1]
                ]))
                
                selected_output = outputs[best_group[0]]
                consensus_score = statistics.mean([
                    similarity_matrix[best_group[0]][j] for j in best_group[1]
                ])
            else:
                # No consensus, fall back to quality selection
                return await self._quality_selection_ensemble(request, outputs)
        else:
            # For non-text, use confidence consensus
            high_conf_outputs = [o for o in outputs if o.confidence >= request.required_consensus]
            if len(high_conf_outputs) >= request.min_models:
                selected_output = max(high_conf_outputs, key=lambda x: x.confidence)
                consensus_score = statistics.mean([o.confidence for o in high_conf_outputs])
            else:
                selected_output = max(outputs, key=lambda x: x.confidence)
                consensus_score = selected_output.confidence
        
        return EnsembleResult(
            request_id=request.request_id,
            final_output=selected_output.content,
            strategy_used=EnsemblingStrategy.CONSENSUS,
            model_outputs=outputs,
            ensemble_confidence=selected_output.confidence,
            quality_scores=selected_output.quality_scores,
            consensus_score=consensus_score,
            selection_reasoning=f"Consensus-based selection from {selected_output.model_id}",
            quality_analysis={"consensus_threshold": consensus_threshold}
        )
    
    async def _hybrid_ensemble(self, request: EnsembleRequest, outputs: List[ModelOutput]) -> EnsembleResult:
        """Hybrid ensemble combining multiple strategies."""
        # Try different strategies and combine results
        voting_result = await self._voting_ensemble(request, outputs)
        quality_result = await self._quality_selection_ensemble(request, outputs)
        
        # Compare strategies and select best
        if voting_result.consensus_score > quality_result.consensus_score:
            final_result = voting_result
            final_result.strategy_used = EnsemblingStrategy.HYBRID
            final_result.selection_reasoning = "Hybrid: Voting strategy selected"
        else:
            final_result = quality_result
            final_result.strategy_used = EnsemblingStrategy.HYBRID
            final_result.selection_reasoning = "Hybrid: Quality strategy selected"
        
        return final_result
    
    async def _dynamic_ensemble(self, request: EnsembleRequest, outputs: List[ModelOutput]) -> EnsembleResult:
        """Dynamic ensemble using ML to select best strategy."""
        # For now, use simple heuristics to select strategy
        # In production, this would use ML model
        
        if len(outputs) >= 4:
            # Use consensus for many models
            return await self._consensus_ensemble(request, outputs)
        elif request.output_type == OutputType.TEXT:
            # Use quality selection for text
            return await self._quality_selection_ensemble(request, outputs)
        else:
            # Use weighted average for other types
            return await self._weighted_average_ensemble(request, outputs)
    
    def _calculate_text_similarities(self, outputs: List[ModelOutput]) -> List[List[float]]:
        """Calculate similarity matrix for text outputs."""
        texts = [str(output.content) for output in outputs]
        n = len(texts)
        similarity_matrix = [[0.0] * n for _ in range(n)]
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    similarity_matrix[i][j] = 1.0
                else:
                    # Simple similarity using difflib
                    similarity = difflib.SequenceMatcher(None, texts[i], texts[j]).ratio()
                    similarity_matrix[i][j] = similarity
        
        return similarity_matrix
    
    def _create_single_output_result(
        self,
        request: EnsembleRequest,
        output: ModelOutput,
        start_time: float
    ) -> EnsembleResult:
        """Create result from single output (fallback)."""
        return EnsembleResult(
            request_id=request.request_id,
            final_output=output.content,
            strategy_used=EnsemblingStrategy.QUALITY_SELECTION,
            model_outputs=[output],
            ensemble_confidence=output.confidence,
            quality_scores=output.quality_scores,
            consensus_score=output.confidence,
            total_response_time=time.time() - start_time,
            total_cost=output.cost,
            models_used=1,
            selection_reasoning=f"Single output from {output.model_id} (fallback)",
            quality_analysis={"fallback": True}
        )
    
    def _update_model_performance(self, outputs: List[ModelOutput], result: EnsembleResult):
        """Update model performance metrics."""
        for output in outputs:
            model_id = output.model_id
            
            if model_id not in self.model_performance:
                self.model_performance[model_id] = {
                    "quality": 0.8,
                    "reliability": 0.8,
                    "cost_effectiveness": 0.8,
                    "usage_count": 0
                }
            
            perf = self.model_performance[model_id]
            
            # Update quality score
            if output.quality_scores:
                avg_quality = statistics.mean(output.quality_scores.values())
                perf["quality"] = (perf["quality"] * 0.9) + (avg_quality * 0.1)
            
            # Update reliability (based on confidence)
            perf["reliability"] = (perf["reliability"] * 0.9) + (output.confidence * 0.1)
            
            # Update cost effectiveness
            if output.cost > 0:
                cost_eff = output.confidence / output.cost
                perf["cost_effectiveness"] = (perf["cost_effectiveness"] * 0.9) + (cost_eff * 0.1)
            
            perf["usage_count"] += 1
    
    def get_ensembling_status(self) -> Dict[str, Any]:
        """Get ensembling engine status."""
        return {
            "total_ensembles": len(self.ensemble_history),
            "strategy_weights": {s.value: w for s, w in self.strategy_weights.items()},
            "model_performance": self.model_performance,
            "recent_results": len([
                r for r in self.ensemble_history 
                if time.time() - r.timestamp < 3600
            ]),
            "average_consensus": statistics.mean([
                r.consensus_score for r in self.ensemble_history[-100:]
            ]) if self.ensemble_history else 0.0,
            "average_models_used": statistics.mean([
                r.models_used for r in self.ensemble_history[-100:]
            ]) if self.ensemble_history else 0.0
        }


# Global model ensembling engine instance
model_ensembling_engine = ModelEnsemblingEngine()
