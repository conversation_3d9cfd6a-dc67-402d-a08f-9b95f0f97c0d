"""
Template Manager for Structured Output Generation.
Provides Jinja2-based template system for formatting AI responses.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from jinja2 import Environment, BaseLoader, Template, TemplateError
import re

logger = logging.getLogger(__name__)


@dataclass
class TemplateInfo:
    """Information about a registered template."""
    name: str
    description: str
    variables: List[str]
    category: str
    example_data: Optional[Dict[str, Any]] = None


class TemplateManager:
    """Enhanced template management system."""
    
    def __init__(self):
        self.templates: Dict[str, Template] = {}
        self.template_info: Dict[str, TemplateInfo] = {}
        self.env = Environment(
            loader=BaseLoader(),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Add custom filters
        self._register_custom_filters()
        
        # Register common templates
        self._register_common_templates()
        
        logger.info("Template Manager initialized")
    
    def register_template(
        self,
        name: str,
        template_str: str,
        description: str = "",
        category: str = "general",
        example_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Register a Jinja2 template.
        
        Args:
            name: Template name
            template_str: Jinja2 template string
            description: Template description
            category: Template category
            example_data: Example data for testing
            
        Returns:
            True if registration successful
        """
        try:
            # Parse and validate template
            template = self.env.from_string(template_str)
            
            # Extract variables from template
            variables = self._extract_variables(template_str)
            
            # Store template and info
            self.templates[name] = template
            self.template_info[name] = TemplateInfo(
                name=name,
                description=description,
                variables=variables,
                category=category,
                example_data=example_data
            )
            
            logger.info(f"Template '{name}' registered successfully")
            return True
            
        except TemplateError as e:
            logger.error(f"Failed to register template '{name}': {e}")
            raise ValueError(f"Invalid template: {e}")
    
    def render_template(self, name: str, data: Dict[str, Any], validate: bool = True) -> str:
        """Render template with data.
        
        Args:
            name: Template name
            data: Data to render with
            validate: Whether to validate required variables
            
        Returns:
            Rendered template string
        """
        if name not in self.templates:
            raise ValueError(f"Template '{name}' not found")
        
        template = self.templates[name]
        template_info = self.template_info[name]
        
        # Validate required variables if requested
        if validate:
            missing_vars = [var for var in template_info.variables if var not in data]
            if missing_vars:
                logger.warning(f"Missing variables for template '{name}': {missing_vars}")
        
        try:
            # Render template
            rendered = template.render(**data)
            
            # Try to parse as JSON to validate structure
            if self._looks_like_json(rendered):
                try:
                    json.loads(rendered)
                except json.JSONDecodeError as e:
                    logger.warning(f"Template '{name}' produced invalid JSON: {e}")
            
            return rendered
            
        except TemplateError as e:
            logger.error(f"Template rendering failed for '{name}': {e}")
            raise ValueError(f"Template rendering failed: {e}")
    
    def _extract_variables(self, template_str: str) -> List[str]:
        """Extract variable names from template string."""
        # Find all {{ variable }} patterns
        variable_pattern = r'\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\}\}'
        matches = re.findall(variable_pattern, template_str)
        
        # Extract base variable names (before dots)
        variables = set()
        for match in matches:
            base_var = match.split('.')[0]
            variables.add(base_var)
        
        return sorted(list(variables))
    
    def _looks_like_json(self, text: str) -> bool:
        """Check if text looks like JSON."""
        text = text.strip()
        return (text.startswith('{') and text.endswith('}')) or \
               (text.startswith('[') and text.endswith(']'))
    
    def _register_custom_filters(self):
        """Register custom Jinja2 filters."""
        
        def json_format(value, indent=2):
            """Format value as JSON with indentation."""
            if isinstance(value, str):
                try:
                    value = json.loads(value)
                except:
                    pass
            return json.dumps(value, indent=indent, ensure_ascii=False)
        
        def truncate_words(value, length=50):
            """Truncate text to specified word count."""
            if not isinstance(value, str):
                return value
            words = value.split()
            if len(words) <= length:
                return value
            return ' '.join(words[:length]) + '...'
        
        def capitalize_words(value):
            """Capitalize each word."""
            if not isinstance(value, str):
                return value
            return ' '.join(word.capitalize() for word in value.split())
        
        def list_to_bullets(value, bullet_char="•"):
            """Convert list to bullet points."""
            if not isinstance(value, list):
                return value
            return '\n'.join(f"{bullet_char} {item}" for item in value)
        
        # Register filters
        self.env.filters['json_format'] = json_format
        self.env.filters['truncate_words'] = truncate_words
        self.env.filters['capitalize_words'] = capitalize_words
        self.env.filters['list_to_bullets'] = list_to_bullets
    
    def _register_common_templates(self):
        """Register commonly used templates."""
        
        # Article summary template
        article_summary_template = '''
{
  "title": "{{ title }}",
  "summary": "{{ summary | truncate_words(100) }}",
  "key_points": [
    {% for point in key_points -%}
    "{{ point }}"{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "sentiment": "{{ sentiment }}",
  "word_count": {{ word_count }},
  "reading_time_minutes": {{ (word_count / 200) | round }}
}'''.strip()
        
        # Product review template
        product_review_template = '''
{
  "product_name": "{{ product_name }}",
  "rating": {{ rating }},
  "pros": [
    {% for pro in pros -%}
    "{{ pro }}"{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "cons": [
    {% for con in cons -%}
    "{{ con }}"{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "recommendation": "{{ recommendation }}",
  "review_summary": "{{ review_summary | truncate_words(50) }}",
  "value_for_money": "{{ value_for_money | default('not_specified') }}"
}'''.strip()
        
        # Data analysis template
        data_analysis_template = '''
{
  "dataset_name": "{{ dataset_name }}",
  "analysis_type": "{{ analysis_type }}",
  "findings": {
    "key_insights": [
      {% for insight in key_insights -%}
      "{{ insight }}"{% if not loop.last %},{% endif %}
      {% endfor %}
    ],
    "statistics": {
      {% for key, value in statistics.items() -%}
      "{{ key }}": {{ value }}{% if not loop.last %},{% endif %}
      {% endfor %}
    }
  },
  "recommendations": [
    {% for rec in recommendations -%}
    "{{ rec }}"{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "confidence_level": "{{ confidence_level | default('medium') }}"
}'''.strip()
        
        # Meeting notes template
        meeting_notes_template = '''
{
  "meeting_title": "{{ meeting_title }}",
  "date": "{{ date }}",
  "participants": [
    {% for participant in participants -%}
    "{{ participant }}"{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "agenda_items": [
    {% for item in agenda_items -%}
    {
      "topic": "{{ item.topic }}",
      "discussion": "{{ item.discussion | truncate_words(30) }}",
      "action_items": [
        {% for action in item.action_items -%}
        "{{ action }}"{% if not loop.last %},{% endif %}
        {% endfor %}
      ]
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "next_meeting": "{{ next_meeting | default('TBD') }}"
}'''.strip()
        
        # Email response template
        email_response_template = '''
{
  "subject": "{{ subject }}",
  "tone": "{{ tone | default('professional') }}",
  "response": "{{ response }}",
  "action_required": {{ action_required | lower }},
  "priority": "{{ priority | default('medium') }}",
  "suggested_follow_up": "{{ suggested_follow_up | default('none') }}"
}'''.strip()
        
        # Code review template
        code_review_template = '''
{
  "file_name": "{{ file_name }}",
  "language": "{{ language }}",
  "overall_quality": "{{ overall_quality }}",
  "issues": [
    {% for issue in issues -%}
    {
      "type": "{{ issue.type }}",
      "severity": "{{ issue.severity }}",
      "line": {{ issue.line | default(0) }},
      "description": "{{ issue.description }}",
      "suggestion": "{{ issue.suggestion | default('') }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "strengths": [
    {% for strength in strengths -%}
    "{{ strength }}"{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "recommendations": [
    {% for rec in recommendations -%}
    "{{ rec }}"{% if not loop.last %},{% endif %}
    {% endfor %}
  ]
}'''.strip()
        
        # Register templates with example data
        self.register_template(
            "article_summary",
            article_summary_template,
            "Template for article summarization",
            "content",
            {
                "title": "AI Breakthrough in Natural Language Processing",
                "summary": "Recent advances in AI have led to significant improvements...",
                "key_points": ["Improved accuracy", "Faster processing", "Better understanding"],
                "sentiment": "positive",
                "word_count": 500
            }
        )
        
        self.register_template(
            "product_review",
            product_review_template,
            "Template for product review analysis",
            "ecommerce",
            {
                "product_name": "Wireless Headphones XYZ",
                "rating": 4.5,
                "pros": ["Great sound quality", "Comfortable fit", "Long battery life"],
                "cons": ["Expensive", "Limited color options"],
                "recommendation": "recommended",
                "review_summary": "Excellent headphones with minor drawbacks",
                "value_for_money": "good"
            }
        )
        
        self.register_template(
            "data_analysis",
            data_analysis_template,
            "Template for data analysis results",
            "analytics",
            {
                "dataset_name": "Sales Data Q4 2023",
                "analysis_type": "descriptive",
                "key_insights": ["Revenue increased 15%", "Customer retention improved"],
                "statistics": {"mean_revenue": 50000, "total_customers": 1200},
                "recommendations": ["Focus on high-value customers", "Expand marketing"],
                "confidence_level": "high"
            }
        )
        
        self.register_template(
            "meeting_notes",
            meeting_notes_template,
            "Template for meeting notes",
            "business",
            {
                "meeting_title": "Weekly Team Standup",
                "date": "2024-01-15",
                "participants": ["Alice", "Bob", "Charlie"],
                "agenda_items": [
                    {
                        "topic": "Project Updates",
                        "discussion": "Discussed current progress and blockers",
                        "action_items": ["Complete feature X", "Review code"]
                    }
                ],
                "next_meeting": "2024-01-22"
            }
        )
        
        self.register_template(
            "email_response",
            email_response_template,
            "Template for email response generation",
            "communication",
            {
                "subject": "Re: Project Proposal",
                "tone": "professional",
                "response": "Thank you for your proposal. We will review it and get back to you.",
                "action_required": True,
                "priority": "high",
                "suggested_follow_up": "Schedule meeting"
            }
        )
        
        self.register_template(
            "code_review",
            code_review_template,
            "Template for code review analysis",
            "development",
            {
                "file_name": "user_service.py",
                "language": "python",
                "overall_quality": "good",
                "issues": [
                    {
                        "type": "style",
                        "severity": "low",
                        "line": 25,
                        "description": "Line too long",
                        "suggestion": "Break into multiple lines"
                    }
                ],
                "strengths": ["Good error handling", "Clear variable names"],
                "recommendations": ["Add more comments", "Consider refactoring"]
            }
        )
        
        logger.info("Common templates registered successfully")
    
    def get_template_info(self, name: str) -> TemplateInfo:
        """Get information about a template."""
        if name not in self.template_info:
            raise ValueError(f"Template '{name}' not found")
        return self.template_info[name]
    
    def list_templates(self, category: Optional[str] = None) -> List[TemplateInfo]:
        """List all registered templates, optionally filtered by category."""
        templates = list(self.template_info.values())
        if category:
            templates = [t for t in templates if t.category == category]
        return templates
    
    def get_categories(self) -> List[str]:
        """Get all template categories."""
        return sorted(set(info.category for info in self.template_info.values()))
    
    def test_template(self, name: str) -> str:
        """Test template with example data."""
        if name not in self.template_info:
            raise ValueError(f"Template '{name}' not found")
        
        template_info = self.template_info[name]
        if not template_info.example_data:
            raise ValueError(f"No example data available for template '{name}'")
        
        return self.render_template(name, template_info.example_data)


# Global template manager instance
template_manager = TemplateManager()
