"""
Enhanced error handling system for AI service.
"""
import logging
import traceback
import time
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Error categories for classification."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    RATE_LIMIT = "rate_limit"
    MODEL_ERROR = "model_error"
    NETWORK = "network"
    TIMEOUT = "timeout"
    RESOURCE = "resource"
    INTERNAL = "internal"
    EXTERNAL_API = "external_api"
    STREAMING = "streaming"
    FUNCTION_CALLING = "function_calling"


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorContext:
    """Context information for errors."""
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    endpoint: Optional[str] = None
    model: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StandardError:
    """Standardized error structure."""
    code: str
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    context: ErrorContext
    details: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None
    retry_after: Optional[int] = None
    trace_id: Optional[str] = None


class ErrorHandler:
    """Enhanced error handler with standardized responses."""
    
    def __init__(self):
        self.error_stats: Dict[str, int] = {}
        self.error_history: List[StandardError] = []
        self.max_history = 1000
    
    def create_error(
        self,
        code: str,
        message: str,
        category: ErrorCategory,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[ErrorContext] = None,
        details: Optional[Dict[str, Any]] = None,
        suggestions: Optional[List[str]] = None,
        retry_after: Optional[int] = None
    ) -> StandardError:
        """Create a standardized error."""
        if context is None:
            context = ErrorContext()
        
        error = StandardError(
            code=code,
            message=message,
            category=category,
            severity=severity,
            context=context,
            details=details,
            suggestions=suggestions,
            retry_after=retry_after,
            trace_id=f"trace_{int(time.time() * 1000)}"
        )
        
        # Track error statistics
        self.error_stats[code] = self.error_stats.get(code, 0) + 1
        
        # Add to history
        self.error_history.append(error)
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
        
        # Log error
        self._log_error(error)
        
        return error
    
    def _log_error(self, error: StandardError):
        """Log error with appropriate level."""
        log_data = {
            "code": error.code,
            "category": error.category.value,
            "severity": error.severity.value,
            "trace_id": error.trace_id,
            "context": {
                "request_id": error.context.request_id,
                "endpoint": error.context.endpoint,
                "model": error.context.model
            }
        }
        
        if error.severity == ErrorSeverity.CRITICAL:
            logger.critical(f"CRITICAL ERROR: {error.message}", extra=log_data)
        elif error.severity == ErrorSeverity.HIGH:
            logger.error(f"HIGH SEVERITY: {error.message}", extra=log_data)
        elif error.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"MEDIUM SEVERITY: {error.message}", extra=log_data)
        else:
            logger.info(f"LOW SEVERITY: {error.message}", extra=log_data)
    
    def to_http_response(self, error: StandardError) -> Dict[str, Any]:
        """Convert error to HTTP response format."""
        response = {
            "error": {
                "code": error.code,
                "message": error.message,
                "category": error.category.value,
                "severity": error.severity.value,
                "trace_id": error.trace_id,
                "timestamp": error.context.timestamp.isoformat()
            }
        }
        
        if error.details:
            response["error"]["details"] = error.details
        
        if error.suggestions:
            response["error"]["suggestions"] = error.suggestions
        
        if error.retry_after:
            response["error"]["retry_after"] = error.retry_after
        
        if error.context.request_id:
            response["error"]["request_id"] = error.context.request_id
        
        return response
    
    def get_http_status_code(self, error: StandardError) -> int:
        """Get appropriate HTTP status code for error."""
        status_map = {
            ErrorCategory.VALIDATION: 400,
            ErrorCategory.AUTHENTICATION: 401,
            ErrorCategory.AUTHORIZATION: 403,
            ErrorCategory.RATE_LIMIT: 429,
            ErrorCategory.MODEL_ERROR: 422,
            ErrorCategory.NETWORK: 503,
            ErrorCategory.TIMEOUT: 408,
            ErrorCategory.RESOURCE: 507,
            ErrorCategory.INTERNAL: 500,
            ErrorCategory.EXTERNAL_API: 502,
            ErrorCategory.STREAMING: 500,
            ErrorCategory.FUNCTION_CALLING: 422
        }
        
        return status_map.get(error.category, 500)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return {
            "total_errors": sum(self.error_stats.values()),
            "error_counts": self.error_stats.copy(),
            "recent_errors": len(self.error_history),
            "categories": {
                category.value: sum(
                    1 for error in self.error_history[-100:]  # Last 100 errors
                    if error.category == category
                )
                for category in ErrorCategory
            }
        }


# Global error handler instance
error_handler = ErrorHandler()


# Common error creators
def validation_error(
    message: str,
    field: Optional[str] = None,
    context: Optional[ErrorContext] = None,
    suggestions: Optional[List[str]] = None
) -> StandardError:
    """Create a validation error."""
    details = {"field": field} if field else None
    return error_handler.create_error(
        code="VALIDATION_ERROR",
        message=message,
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.LOW,
        context=context,
        details=details,
        suggestions=suggestions
    )


def model_error(
    message: str,
    model: Optional[str] = None,
    context: Optional[ErrorContext] = None,
    retry_after: Optional[int] = None
) -> StandardError:
    """Create a model error."""
    details = {"model": model} if model else None
    return error_handler.create_error(
        code="MODEL_ERROR",
        message=message,
        category=ErrorCategory.MODEL_ERROR,
        severity=ErrorSeverity.MEDIUM,
        context=context,
        details=details,
        retry_after=retry_after
    )


def streaming_error(
    message: str,
    stream_id: Optional[str] = None,
    context: Optional[ErrorContext] = None
) -> StandardError:
    """Create a streaming error."""
    details = {"stream_id": stream_id} if stream_id else None
    return error_handler.create_error(
        code="STREAMING_ERROR",
        message=message,
        category=ErrorCategory.STREAMING,
        severity=ErrorSeverity.HIGH,
        context=context,
        details=details,
        suggestions=["Check network connection", "Retry the request", "Use non-streaming mode"]
    )


def rate_limit_error(
    message: str,
    retry_after: int,
    context: Optional[ErrorContext] = None
) -> StandardError:
    """Create a rate limit error."""
    return error_handler.create_error(
        code="RATE_LIMIT_EXCEEDED",
        message=message,
        category=ErrorCategory.RATE_LIMIT,
        severity=ErrorSeverity.MEDIUM,
        context=context,
        retry_after=retry_after,
        suggestions=[f"Wait {retry_after} seconds before retrying", "Reduce request frequency"]
    )


def internal_error(
    message: str,
    context: Optional[ErrorContext] = None,
    details: Optional[Dict[str, Any]] = None
) -> StandardError:
    """Create an internal server error."""
    return error_handler.create_error(
        code="INTERNAL_SERVER_ERROR",
        message=message,
        category=ErrorCategory.INTERNAL,
        severity=ErrorSeverity.HIGH,
        context=context,
        details=details,
        suggestions=["Try again later", "Contact support if the problem persists"]
    )
