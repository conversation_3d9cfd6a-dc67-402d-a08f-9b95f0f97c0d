"""
Main application module with production readiness features.
"""
import logging
import time
import uvicorn
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from ai_service.api import ai_routes, health_routes, streaming_routes, security_routes
from ai_service.config.settings import settings
from ai_service.core.logging_config import (
    setup_logging, RequestTracker, performance_logger,
    set_request_context, generate_request_id, clear_request_context
)
from ai_service.core.security import security_manager, SecurityMiddleware
from ai_service.core.metrics import metrics_collector
from ai_service.core.health_checker import health_checker
from ai_service.core.performance_monitor import performance_monitor
from ai_service.core.cache_manager import cache_manager
from ai_service.core.batch_processor import batch_processor
from ai_service.core.streaming_manager import streaming_manager
from ai_service.core.websocket_manager import websocket_manager
from ai_service.core.sse_manager import sse_manager

# Import enterprise security components
try:
    from ai_service.core.auth_manager import auth_manager
    from ai_service.core.rate_limiter import rate_limiter
    from ai_service.core.audit_logger import audit_logger
    from ai_service.core.tenant_manager import tenant_manager
    from ai_service.core.monitoring_system import monitoring_system
    ENTERPRISE_SECURITY_AVAILABLE = True
except ImportError:
    ENTERPRISE_SECURITY_AVAILABLE = False
    logger.warning("Enterprise security features not available")
from ai_service.utils.error_handlers import (
    validation_exception_handler,
    general_exception_handler
)
from ai_service.core.ai_client import AIClient
from ai_service.core.resource_manager import resource_tracker, memory_monitor

# Set up logging
setup_logging()
logger = logging.getLogger(__name__)

# Global AI client for function registration
global_ai_client = None

def register_default_functions(ai_client: AIClient):
    """Register default functions for function calling."""

    def add_numbers(a: float, b: float) -> float:
        """Add two numbers together."""
        return a + b

    def multiply_numbers(x: float, y: float) -> float:
        """Multiply two numbers together."""
        return x * y

    def subtract_numbers(a: float, b: float) -> float:
        """Subtract second number from first number."""
        return a - b

    def divide_numbers(a: float, b: float) -> float:
        """Divide first number by second number."""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b

    def get_current_time() -> str:
        """Get current date and time."""
        import time
        return time.strftime("%Y-%m-%d %H:%M:%S")

    def get_weather(city: str, country: str = "US") -> str:
        """Get weather information for a city (mock function)."""
        return f"Weather in {city}, {country}: 22°C, Sunny, Light breeze"

    def calculate_power(base: float, exponent: float) -> float:
        """Calculate base raised to the power of exponent."""
        return base ** exponent

    def get_string_length(text: str) -> int:
        """Get the length of a string."""
        return len(text)

    # Register all functions
    ai_client.register_function("add_numbers", add_numbers)
    ai_client.register_function("multiply_numbers", multiply_numbers)
    ai_client.register_function("subtract_numbers", subtract_numbers)
    ai_client.register_function("divide_numbers", divide_numbers)
    ai_client.register_function("get_current_time", get_current_time)
    ai_client.register_function("get_weather", get_weather)
    ai_client.register_function("calculate_power", calculate_power)
    ai_client.register_function("get_string_length", get_string_length)

    logger.info("Registered 8 default functions for function calling")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application with production readiness.

    Returns:
        Configured FastAPI application
    """
    app = FastAPI(
        title=settings.app_name,
        description="Production-ready AI service with comprehensive monitoring and security",
        version=settings.app_version,
        debug=settings.debug,
        docs_url="/docs" if not settings.is_production else None,
        redoc_url="/redoc" if not settings.is_production else None
    )

    # Add security middleware first
    if settings.enable_rate_limiting:
        security_middleware = SecurityMiddleware(security_manager)
        app.middleware("http")(security_middleware)

    # Add CORS middleware with environment-specific origins
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.get_cors_origins(),
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
        allow_headers=["*"],
        max_age=3600 if settings.is_production else 0
    )

    # Add exception handlers
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    # Add comprehensive monitoring and logging middleware
    @app.middleware("http")
    async def monitoring_middleware(request: Request, call_next):
        # Generate request ID and set context
        request_id = generate_request_id()
        correlation_id = request.headers.get("x-correlation-id", request_id)
        set_request_context(request_id=request_id, correlation_id=correlation_id)

        start_time = time.time()
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        # Add request ID to response headers
        def add_request_id_header(response):
            response.headers["x-request-id"] = request_id
            response.headers["x-correlation-id"] = correlation_id
            return response

        # Log request start with structured logging
        logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                "event_type": "request_start",
                "method": request.method,
                "path": str(request.url.path),
                "query_params": dict(request.query_params),
                "client_ip": client_ip,
                "user_agent": user_agent,
                "content_length": request.headers.get("content-length", 0)
            }
        )

        try:
            # Track request with metrics collector
            async with metrics_collector.track_request(str(request.url.path), request.method):
                response = await call_next(request)

            duration_ms = (time.time() - start_time) * 1000

            # Add headers to response
            response = add_request_id_header(response)

            # Record metrics
            from ai_service.core.metrics import RequestMetrics
            request_metrics = RequestMetrics(
                endpoint=str(request.url.path),
                method=request.method,
                status_code=response.status_code,
                duration_ms=duration_ms,
                timestamp=time.time(),
                user_agent=user_agent,
                client_ip=client_ip
            )
            metrics_collector.record_request(request_metrics)

            # Log successful response
            logger.info(
                f"Request completed: {response.status_code} ({duration_ms:.2f}ms)",
                extra={
                    "event_type": "request_completed",
                    "status_code": response.status_code,
                    "duration_ms": duration_ms,
                    "response_size": response.headers.get("content-length", 0)
                }
            )

            return response

        except HTTPException as e:
            duration_ms = (time.time() - start_time) * 1000

            # Create error response with request ID
            error_response = JSONResponse(
                status_code=e.status_code,
                content={
                    "error": e.detail,
                    "request_id": request_id,
                    "timestamp": time.time()
                }
            )
            error_response = add_request_id_header(error_response)

            # Log HTTP exception
            logger.warning(
                f"Request failed with HTTP {e.status_code}: {e.detail} ({duration_ms:.2f}ms)",
                extra={
                    "event_type": "request_http_error",
                    "status_code": e.status_code,
                    "error_detail": str(e.detail),
                    "duration_ms": duration_ms
                }
            )

            return error_response

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000

            # Create error response with request ID
            error_response = JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id,
                    "timestamp": time.time()
                }
            )
            error_response = add_request_id_header(error_response)

            # Log unexpected error
            logger.error(
                f"Request failed with unexpected error: {str(e)} ({duration_ms:.2f}ms)",
                extra={
                    "event_type": "request_error",
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "duration_ms": duration_ms
                },
                exc_info=True
            )

            return error_response

        finally:
            # Clear request context
            clear_request_context()

    # Add startup event with comprehensive initialization
    @app.on_event("startup")
    async def startup_event():
        """Initialize application with production readiness features."""
        global global_ai_client

        logger.info(f"Starting {settings.app_name} v{settings.app_version} in {settings.app_env} mode")

        # Initialize AI client and register functions
        global_ai_client = AIClient()
        register_default_functions(global_ai_client)

        # Start resource monitoring
        await memory_monitor.start_monitoring()

        # Start performance monitoring
        await performance_monitor.start_monitoring()

        # Log startup configuration
        logger.info(
            "Application startup completed",
            extra={
                "event_type": "application_startup",
                "app_version": settings.app_version,
                "environment": settings.app_env,
                "debug_mode": settings.debug,
                "rate_limiting_enabled": settings.enable_rate_limiting,
                "metrics_enabled": settings.enable_metrics,
                "caching_enabled": settings.enable_caching
            }
        )

        # Run initial health check
        try:
            service_health = await health_checker.get_service_health()
            logger.info(
                f"Initial health check: {service_health.status.value}",
                extra={
                    "event_type": "initial_health_check",
                    "status": service_health.status.value,
                    "healthy_checks": service_health.summary["healthy_checks"],
                    "total_checks": service_health.summary["total_checks"]
                }
            )
        except Exception as e:
            logger.error(f"Initial health check failed: {e}")

    # Add shutdown event with comprehensive cleanup
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup resources and log shutdown."""
        logger.info("Application shutdown initiated")

        try:
            # Stop monitoring systems
            await memory_monitor.stop_monitoring()
            await performance_monitor.stop_monitoring()

            # Shutdown batch processor
            await batch_processor.shutdown()

            # Shutdown streaming managers
            await streaming_manager.shutdown()
            await websocket_manager.shutdown()
            await sse_manager.shutdown()

            # Shutdown enterprise security managers if available
            if ENTERPRISE_SECURITY_AVAILABLE:
                await auth_manager.shutdown()
                await rate_limiter.shutdown()
                await audit_logger.shutdown()
                await tenant_manager.shutdown()
                await monitoring_system.shutdown()

            # Cleanup all resources
            resource_tracker.cleanup_all()

            # Log final metrics
            metrics_summary = metrics_collector.get_metrics_summary()
            logger.info(
                "Application shutdown completed",
                extra={
                    "event_type": "application_shutdown",
                    "total_requests": metrics_summary["requests"]["total"],
                    "uptime_hours": metrics_summary["system"].get("uptime_hours", 0),
                    "final_memory_mb": metrics_summary["system"].get("process_memory_mb", 0)
                }
            )

        except Exception as e:
            logger.error(f"Error during shutdown: {e}", exc_info=True)

    # Include routers
    app.include_router(health_routes.router)
    app.include_router(ai_routes.router)
    app.include_router(streaming_routes.router)

    # Include enterprise security routes if available
    if ENTERPRISE_SECURITY_AVAILABLE:
        app.include_router(security_routes.router)

    return app


app = create_app()


if __name__ == "__main__":
    logger.info(f"Starting server on {settings.host}:{settings.port}")
    uvicorn.run(
        "ai_service.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.app_env == "development"
    )