"""
Phase 7 API Routes: Global Scale & Enterprise Integration.
Includes global deployment, SSO, compliance, and white-label management.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from pydantic import BaseModel, Field

from ai_service.core.global_deployment_manager import (
    global_deployment_manager, Region, LoadBalancingStrategy, GlobalRequest
)
from ai_service.core.enterprise_sso_manager import (
    enterprise_sso_manager, SSOProvider, AuthenticationMethod, SSOConfig
)
from ai_service.core.compliance_manager import (
    compliance_manager, ComplianceFramework, ProcessingPurpose, DataClassification, ConsentStatus
)
from ai_service.core.white_label_manager import (
    white_label_manager, FeatureSet, BrandingLevel, DeploymentType, BrandingConfig
)
from ai_service.core.enhanced_auth_manager import enhanced_auth_manager, Permission
from ai_service.api.phase5_routes import require_permission

logger = logging.getLogger(__name__)

# Router
router = APIRouter(prefix="/api/v1/enterprise", tags=["Phase 7 - Global Enterprise"])


# Pydantic Models
class GlobalDeploymentStatusResponse(BaseModel):
    """Global deployment status response."""
    total_regions: int = Field(..., description="Total number of regions")
    active_regions: int = Field(..., description="Number of active regions")
    global_health_score: float = Field(..., description="Global health score")
    total_capacity: int = Field(..., description="Total capacity")
    total_load: int = Field(..., description="Current total load")
    utilization: float = Field(..., description="Global utilization percentage")
    load_balancing_strategy: str = Field(..., description="Current load balancing strategy")
    regions: Dict[str, Any] = Field(..., description="Region details")


class RouteRequestModel(BaseModel):
    """Route request model."""
    capability: str = Field(..., description="Required capability")
    priority: str = Field("normal", description="Request priority")
    max_latency_ms: Optional[int] = Field(None, description="Maximum acceptable latency")
    preferred_regions: Optional[List[str]] = Field(None, description="Preferred regions")
    excluded_regions: Optional[List[str]] = Field(None, description="Excluded regions")
    cost_budget: Optional[float] = Field(None, description="Cost budget")


class SSOConfigRequest(BaseModel):
    """SSO configuration request."""
    provider: str = Field(..., description="SSO provider type")
    name: str = Field(..., description="Provider name")
    client_id: str = Field(..., description="Client ID")
    client_secret: str = Field(..., description="Client secret")
    authorization_url: str = Field(..., description="Authorization URL")
    token_url: str = Field(..., description="Token URL")
    userinfo_url: Optional[str] = Field(None, description="User info URL")
    redirect_uri: str = Field(..., description="Redirect URI")
    scopes: List[str] = Field(default=["openid", "profile", "email"], description="OAuth scopes")
    is_enabled: bool = Field(True, description="Enable provider")


class ComplianceConsentRequest(BaseModel):
    """Compliance consent request."""
    subject_id: str = Field(..., description="Data subject ID")
    purpose: str = Field(..., description="Processing purpose")
    status: str = Field(..., description="Consent status")
    legal_basis: Optional[str] = Field(None, description="Legal basis")


class DataProcessingRequest(BaseModel):
    """Data processing request."""
    subject_id: str = Field(..., description="Data subject ID")
    purpose: str = Field(..., description="Processing purpose")
    data_types: List[str] = Field(..., description="Types of data processed")
    classification: str = Field(..., description="Data classification")
    processor: str = Field(..., description="Processor name")
    legal_basis: str = Field(..., description="Legal basis for processing")
    processing_location: str = Field("US", description="Processing location")


class WhiteLabelTenantRequest(BaseModel):
    """White-label tenant creation request."""
    tenant_name: str = Field(..., description="Tenant name")
    feature_set: str = Field(..., description="Feature set")
    branding_level: str = Field(..., description="Branding level")
    deployment_type: str = Field(..., description="Deployment type")
    
    # Branding
    company_name: Optional[str] = Field(None, description="Company name")
    primary_color: Optional[str] = Field(None, description="Primary color")
    logo_url: Optional[str] = Field(None, description="Logo URL")
    
    # Custom settings
    custom_settings: Optional[Dict[str, Any]] = Field(None, description="Custom settings")


# Global Deployment Endpoints
@router.get("/global/status", response_model=GlobalDeploymentStatusResponse)
async def get_global_deployment_status(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get global deployment status."""
    try:
        status = global_deployment_manager.get_global_status()
        
        return GlobalDeploymentStatusResponse(
            total_regions=status["total_regions"],
            active_regions=status["active_regions"],
            global_health_score=status["global_health_score"],
            total_capacity=status["total_capacity"],
            total_load=status["total_load"],
            utilization=status["utilization"],
            load_balancing_strategy=status["load_balancing_strategy"],
            regions=status["regions"]
        )
        
    except Exception as e:
        logger.error(f"Error getting global deployment status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get global status")


@router.post("/global/route")
async def route_global_request(
    request: RouteRequestModel,
    client_request: Request,
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """Route request to optimal global region."""
    try:
        # Get client IP
        client_ip = client_request.client.host
        
        # Get client location
        client_location = global_deployment_manager.get_client_location(client_ip)
        
        # Create global request
        global_request = GlobalRequest(
            request_id=f"req_{int(time.time())}",
            client_ip=client_ip,
            client_location=client_location,
            capability=request.capability,
            priority=request.priority,
            max_latency_ms=request.max_latency_ms,
            preferred_regions=[Region(r) for r in request.preferred_regions] if request.preferred_regions else None,
            excluded_regions=[Region(r) for r in request.excluded_regions] if request.excluded_regions else None,
            cost_budget=request.cost_budget
        )
        
        # Route request
        selected_region = await global_deployment_manager.route_request(global_request)
        
        if not selected_region:
            raise HTTPException(status_code=503, detail="No available regions")
        
        return {
            "request_id": global_request.request_id,
            "selected_region": {
                "region": selected_region.region.value,
                "name": selected_region.name,
                "endpoint_url": selected_region.endpoint_url,
                "latency_ms": selected_region.latency_ms,
                "cost_multiplier": selected_region.cost_multiplier
            },
            "client_location": client_location,
            "routing_reason": "optimal_selection"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error routing global request: {e}")
        raise HTTPException(status_code=500, detail="Failed to route request")


@router.post("/global/load-balancing/strategy")
async def set_load_balancing_strategy(
    strategy: str,
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Set global load balancing strategy."""
    try:
        strategy_enum = LoadBalancingStrategy(strategy)
        global_deployment_manager.load_balancing_strategy = strategy_enum
        
        return {
            "message": f"Load balancing strategy set to {strategy}",
            "strategy": strategy
        }
        
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid load balancing strategy: {strategy}"
        )
    except Exception as e:
        logger.error(f"Error setting load balancing strategy: {e}")
        raise HTTPException(status_code=500, detail="Failed to set strategy")


# SSO Management Endpoints
@router.post("/sso/configure")
async def configure_sso_provider(
    config: SSOConfigRequest,
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Configure SSO provider."""
    try:
        provider_enum = SSOProvider(config.provider)
        
        sso_config = SSOConfig(
            provider=provider_enum,
            name=config.name,
            client_id=config.client_id,
            client_secret=config.client_secret,
            authorization_url=config.authorization_url,
            token_url=config.token_url,
            userinfo_url=config.userinfo_url,
            redirect_uri=config.redirect_uri,
            scopes=config.scopes,
            is_enabled=config.is_enabled
        )
        
        success = await enterprise_sso_manager.configure_provider(sso_config)
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to configure SSO provider")
        
        return {
            "message": f"SSO provider {config.name} configured successfully",
            "provider": config.provider
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error configuring SSO provider: {e}")
        raise HTTPException(status_code=500, detail="Failed to configure SSO provider")


@router.post("/sso/authenticate")
async def initiate_sso_authentication(
    provider: str,
    method: str = "redirect",
    redirect_uri: Optional[str] = None
):
    """Initiate SSO authentication."""
    try:
        provider_enum = SSOProvider(provider)
        method_enum = AuthenticationMethod(method)
        
        auth_data = await enterprise_sso_manager.initiate_authentication(
            provider=provider_enum,
            method=method_enum,
            redirect_uri=redirect_uri
        )
        
        return auth_data
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error initiating SSO authentication: {e}")
        raise HTTPException(status_code=500, detail="Failed to initiate authentication")


@router.get("/sso/status")
async def get_sso_status(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get SSO system status."""
    try:
        status = enterprise_sso_manager.get_sso_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting SSO status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get SSO status")


# Compliance Management Endpoints
@router.post("/compliance/frameworks/{framework}/enable")
async def enable_compliance_framework(
    framework: str,
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Enable compliance framework."""
    try:
        framework_enum = ComplianceFramework(framework)
        compliance_manager.enable_framework(framework_enum)
        
        return {
            "message": f"Compliance framework {framework} enabled",
            "framework": framework
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid framework: {framework}")
    except Exception as e:
        logger.error(f"Error enabling compliance framework: {e}")
        raise HTTPException(status_code=500, detail="Failed to enable framework")


@router.post("/compliance/consent")
async def record_consent(
    request: ComplianceConsentRequest,
    user_session = Depends(require_permission(Permission.USER_MANAGEMENT))
):
    """Record user consent for data processing."""
    try:
        purpose_enum = ProcessingPurpose(request.purpose)
        status_enum = ConsentStatus(request.status)
        
        success = await compliance_manager.record_consent(
            subject_id=request.subject_id,
            purpose=purpose_enum,
            status=status_enum,
            legal_basis=request.legal_basis
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to record consent")
        
        return {
            "message": "Consent recorded successfully",
            "subject_id": request.subject_id,
            "purpose": request.purpose,
            "status": request.status
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error recording consent: {e}")
        raise HTTPException(status_code=500, detail="Failed to record consent")


@router.post("/compliance/data-processing")
async def record_data_processing(
    request: DataProcessingRequest,
    user_session = Depends(require_permission(Permission.USER_MANAGEMENT))
):
    """Record data processing activity."""
    try:
        purpose_enum = ProcessingPurpose(request.purpose)
        classification_enum = DataClassification(request.classification)
        
        record_id = await compliance_manager.record_data_processing(
            subject_id=request.subject_id,
            purpose=purpose_enum,
            data_types=request.data_types,
            classification=classification_enum,
            processor=request.processor,
            legal_basis=request.legal_basis,
            processing_location=request.processing_location
        )
        
        return {
            "message": "Data processing recorded successfully",
            "record_id": record_id,
            "subject_id": request.subject_id
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error recording data processing: {e}")
        raise HTTPException(status_code=500, detail="Failed to record data processing")


@router.get("/compliance/subject/{subject_id}/access")
async def handle_subject_access_request(
    subject_id: str,
    user_session = Depends(require_permission(Permission.USER_MANAGEMENT))
):
    """Handle data subject access request."""
    try:
        response = await compliance_manager.handle_subject_access_request(subject_id)
        return response
        
    except Exception as e:
        logger.error(f"Error handling subject access request: {e}")
        raise HTTPException(status_code=500, detail="Failed to handle access request")


@router.delete("/compliance/subject/{subject_id}")
async def handle_deletion_request(
    subject_id: str,
    user_session = Depends(require_permission(Permission.USER_MANAGEMENT))
):
    """Handle data subject deletion request."""
    try:
        success = await compliance_manager.handle_deletion_request(subject_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="Deletion request denied")
        
        return {
            "message": "Deletion request processed successfully",
            "subject_id": subject_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling deletion request: {e}")
        raise HTTPException(status_code=500, detail="Failed to handle deletion request")


@router.get("/compliance/status")
async def get_compliance_status(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get compliance system status."""
    try:
        status = compliance_manager.get_compliance_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting compliance status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get compliance status")


# White-Label Management Endpoints
@router.post("/white-label/tenants")
async def create_white_label_tenant(
    request: WhiteLabelTenantRequest,
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Create white-label tenant."""
    try:
        feature_set = FeatureSet(request.feature_set)
        branding_level = BrandingLevel(request.branding_level)
        deployment_type = DeploymentType(request.deployment_type)
        
        # Create branding config
        branding_config = BrandingConfig(
            company_name=request.company_name or request.tenant_name,
            primary_color=request.primary_color or "#007bff",
            logo_url=request.logo_url
        )
        
        tenant = await white_label_manager.create_tenant(
            tenant_name=request.tenant_name,
            feature_set=feature_set,
            branding_level=branding_level,
            deployment_type=deployment_type,
            branding_config=branding_config,
            custom_settings=request.custom_settings
        )
        
        return {
            "message": "White-label tenant created successfully",
            "tenant_id": tenant.tenant_id,
            "tenant_name": tenant.tenant_name,
            "api_base_url": tenant.api_config.api_base_url,
            "status": tenant.status
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating white-label tenant: {e}")
        raise HTTPException(status_code=500, detail="Failed to create tenant")


@router.get("/white-label/tenants/{tenant_id}")
async def get_tenant_status(
    tenant_id: str,
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get white-label tenant status."""
    try:
        status = white_label_manager.get_tenant_status(tenant_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Tenant not found")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tenant status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get tenant status")


@router.get("/white-label/tenants")
async def list_all_tenants(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """List all white-label tenants."""
    try:
        status = white_label_manager.get_all_tenants_status()
        return status
        
    except Exception as e:
        logger.error(f"Error listing tenants: {e}")
        raise HTTPException(status_code=500, detail="Failed to list tenants")


@router.post("/white-label/tenants/{tenant_id}/suspend")
async def suspend_tenant(
    tenant_id: str,
    reason: str = "Administrative action",
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Suspend white-label tenant."""
    try:
        success = await white_label_manager.suspend_tenant(tenant_id, reason)
        
        if not success:
            raise HTTPException(status_code=404, detail="Tenant not found")
        
        return {
            "message": "Tenant suspended successfully",
            "tenant_id": tenant_id,
            "reason": reason
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error suspending tenant: {e}")
        raise HTTPException(status_code=500, detail="Failed to suspend tenant")


# System Status
@router.get("/status")
async def get_enterprise_status():
    """Get overall enterprise system status."""
    try:
        # Get status from all components
        global_status = global_deployment_manager.get_global_status()
        sso_status = enterprise_sso_manager.get_sso_status()
        compliance_status = compliance_manager.get_compliance_status()
        white_label_status = white_label_manager.get_all_tenants_status()
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "global_deployment": {
                "active_regions": global_status["active_regions"],
                "global_health_score": global_status["global_health_score"],
                "utilization": global_status["utilization"]
            },
            "sso": {
                "configured_providers": sso_status["configured_providers"],
                "active_sessions": sso_status["active_sessions"]
            },
            "compliance": {
                "enabled_frameworks": compliance_status["enabled_frameworks"],
                "total_subjects": compliance_status["metrics"]["total_subjects"]
            },
            "white_label": {
                "total_tenants": white_label_status["total_tenants"],
                "active_tenants": white_label_status["active_tenants"]
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting enterprise status: {e}")
        return {
            "status": "error",
            "timestamp": time.time(),
            "error": str(e)
        }
