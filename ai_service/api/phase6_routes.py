"""
Phase 6 API Routes: Multi-Provider AI & Advanced Analytics.
Includes provider management, intelligent routing, and comprehensive analytics.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from ai_service.core.multi_provider_manager import (
    multi_provider_manager, AIProvider, ModelCapability, RoutingStrategy
)
from ai_service.core.ai_analytics_engine import ai_analytics_engine, TimeWindow, AnalyticsMetric
from ai_service.core.enhanced_auth_manager import enhanced_auth_manager, Permission
from ai_service.api.phase5_routes import require_permission

logger = logging.getLogger(__name__)

# Router
router = APIRouter(prefix="/api/v1/multi-ai", tags=["Phase 6 - Multi-Provider AI"])


# Pydantic Models
class ProviderStatusResponse(BaseModel):
    """Provider status response."""
    providers: Dict[str, Dict[str, Any]] = Field(..., description="Provider status information")
    total_providers: int = Field(..., description="Total number of providers")
    active_providers: int = Field(..., description="Number of active providers")
    routing_strategy: str = Field(..., description="Current routing strategy")


class ModelListResponse(BaseModel):
    """Model list response."""
    models: List[Dict[str, Any]] = Field(..., description="Available models")
    total_models: int = Field(..., description="Total number of models")
    capabilities: List[str] = Field(..., description="Available capabilities")
    providers: List[str] = Field(..., description="Available providers")


class MultiProviderGenerationRequest(BaseModel):
    """Multi-provider generation request."""
    prompt: str = Field(..., description="Input prompt", max_length=8000)
    capability: str = Field(..., description="Required capability")
    
    # Provider preferences
    preferred_providers: Optional[List[str]] = Field(None, description="Preferred providers")
    excluded_providers: Optional[List[str]] = Field(None, description="Excluded providers")
    
    # Generation parameters
    max_tokens: Optional[int] = Field(None, description="Maximum tokens", ge=1, le=8192)
    temperature: float = Field(0.7, description="Temperature", ge=0.0, le=2.0)
    top_p: float = Field(0.95, description="Top-p", ge=0.0, le=1.0)
    
    # Routing preferences
    routing_strategy: Optional[str] = Field(None, description="Routing strategy override")
    enable_fallback: bool = Field(True, description="Enable fallback to other providers")
    max_fallbacks: int = Field(3, description="Maximum fallback attempts", ge=1, le=5)
    
    # Quality requirements
    min_quality_score: Optional[float] = Field(None, description="Minimum quality score", ge=0.0, le=1.0)
    max_cost_per_request: Optional[float] = Field(None, description="Maximum cost per request")
    max_response_time: Optional[float] = Field(None, description="Maximum response time in seconds")


class MultiProviderGenerationResponse(BaseModel):
    """Multi-provider generation response."""
    content: str = Field(..., description="Generated content")
    provider_used: str = Field(..., description="Provider that generated the response")
    model_used: str = Field(..., description="Model that generated the response")
    
    # Performance metrics
    response_time: float = Field(..., description="Response time in seconds")
    tokens_used: int = Field(..., description="Total tokens used")
    cost: float = Field(..., description="Request cost")
    
    # Routing information
    routing_strategy: str = Field(..., description="Routing strategy used")
    fallbacks_used: int = Field(..., description="Number of fallbacks used")
    alternative_providers: List[str] = Field(..., description="Other providers that could handle this request")
    
    # Quality metrics
    quality_score: Optional[float] = Field(None, description="Quality score")
    confidence: Optional[float] = Field(None, description="Confidence score")


class AnalyticsRequest(BaseModel):
    """Analytics request."""
    metrics: List[str] = Field(..., description="Requested metrics")
    time_window: str = Field("daily", description="Time window for analysis")
    capability: Optional[str] = Field(None, description="Filter by capability")
    provider: Optional[str] = Field(None, description="Filter by provider")
    include_trends: bool = Field(True, description="Include trend analysis")
    include_comparisons: bool = Field(True, description="Include model comparisons")


class AnalyticsResponse(BaseModel):
    """Analytics response."""
    summary: Dict[str, Any] = Field(..., description="Analytics summary")
    usage_patterns: Dict[str, Any] = Field(..., description="Usage patterns")
    performance_trends: Dict[str, Any] = Field(..., description="Performance trends")
    model_comparisons: Dict[str, Any] = Field(..., description="Model comparisons")
    recommendations: List[str] = Field(..., description="Optimization recommendations")


# Provider Management Endpoints
@router.get("/providers/status", response_model=ProviderStatusResponse)
async def get_provider_status(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get status of all AI providers."""
    try:
        providers_info = {}
        active_count = 0
        
        for provider_type, provider in multi_provider_manager.providers.items():
            health_status = await provider.health_check()
            provider_info = provider.get_provider_info()
            
            providers_info[provider_type.value] = {
                **provider_info,
                "health_status": "healthy" if health_status else "unhealthy",
                "models_available": len(provider.get_available_models()),
                "performance_stats": provider.get_performance_stats()
            }
            
            if health_status and provider.config.is_enabled:
                active_count += 1
        
        return ProviderStatusResponse(
            providers=providers_info,
            total_providers=len(multi_provider_manager.providers),
            active_providers=active_count,
            routing_strategy=multi_provider_manager.routing_strategy.value
        )
        
    except Exception as e:
        logger.error(f"Error getting provider status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get provider status")


@router.get("/models", response_model=ModelListResponse)
async def list_available_models(
    capability: Optional[str] = Query(None, description="Filter by capability"),
    provider: Optional[str] = Query(None, description="Filter by provider"),
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """List all available models across providers."""
    try:
        all_models = []
        capabilities = set()
        providers = set()
        
        for provider_type, provider in multi_provider_manager.providers.items():
            if provider_filter and provider_type.value != provider_filter:
                continue
                
            models = provider.get_available_models()
            
            for model in models:
                # Filter by capability if specified
                if capability:
                    try:
                        cap_filter = ModelCapability(capability)
                        if cap_filter not in model.capabilities:
                            continue
                    except ValueError:
                        continue
                
                model_info = {
                    "provider": model.provider.value,
                    "model_id": model.model_id,
                    "name": model.name,
                    "capabilities": [cap.value for cap in model.capabilities],
                    "max_tokens": model.max_tokens,
                    "cost_per_1k_tokens": model.cost_per_1k_tokens,
                    "quality_score": model.quality_score,
                    "speed_score": model.speed_score,
                    "reliability_score": model.reliability_score
                }
                
                all_models.append(model_info)
                capabilities.update(cap.value for cap in model.capabilities)
                providers.add(model.provider.value)
        
        return ModelListResponse(
            models=all_models,
            total_models=len(all_models),
            capabilities=sorted(list(capabilities)),
            providers=sorted(list(providers))
        )
        
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        raise HTTPException(status_code=500, detail="Failed to list models")


@router.post("/routing/strategy")
async def set_routing_strategy(
    strategy: str,
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Set the routing strategy for provider selection."""
    try:
        routing_strategy = RoutingStrategy(strategy)
        multi_provider_manager.set_routing_strategy(routing_strategy)
        
        return {
            "message": f"Routing strategy set to {strategy}",
            "strategy": strategy
        }
        
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid routing strategy: {strategy}"
        )
    except Exception as e:
        logger.error(f"Error setting routing strategy: {e}")
        raise HTTPException(status_code=500, detail="Failed to set routing strategy")


# Multi-Provider Generation Endpoints
@router.post("/generate", response_model=MultiProviderGenerationResponse)
async def multi_provider_generate(
    request: MultiProviderGenerationRequest,
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """Generate content using intelligent provider routing."""
    try:
        # Parse capability
        try:
            capability = ModelCapability(request.capability)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid capability: {request.capability}"
            )
        
        # Set routing strategy if specified
        original_strategy = multi_provider_manager.routing_strategy
        if request.routing_strategy:
            try:
                strategy = RoutingStrategy(request.routing_strategy)
                multi_provider_manager.set_routing_strategy(strategy)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid routing strategy: {request.routing_strategy}"
                )
        
        # Prepare requirements
        requirements = {}
        if request.preferred_providers:
            requirements["preferred_providers"] = request.preferred_providers
        if request.excluded_providers:
            requirements["excluded_providers"] = request.excluded_providers
        if request.min_quality_score:
            requirements["min_quality_score"] = request.min_quality_score
        if request.max_cost_per_request:
            requirements["max_cost_per_request"] = request.max_cost_per_request
        if request.max_response_time:
            requirements["max_response_time"] = request.max_response_time
        
        # Define operation function based on capability
        async def operation_func(provider, model_id):
            if capability == ModelCapability.TEXT_GENERATION:
                return await provider.generate_text(
                    prompt=request.prompt,
                    model_id=model_id,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p
                )
            elif capability == ModelCapability.CHAT_COMPLETION:
                messages = [{"role": "user", "content": request.prompt}]
                return await provider.generate_chat(
                    messages=messages,
                    model_id=model_id,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p
                )
            elif capability == ModelCapability.EMBEDDINGS:
                return await provider.generate_embeddings(
                    texts=[request.prompt],
                    model_id=model_id
                )
            else:
                raise ValueError(f"Capability {capability.value} not supported in this endpoint")
        
        # Execute with fallback
        import time
        start_time = time.time()
        
        result = await multi_provider_manager.execute_with_fallback(
            capability=capability,
            operation_func=operation_func,
            requirements=requirements,
            max_fallbacks=request.max_fallbacks if request.enable_fallback else 1
        )
        
        response_time = time.time() - start_time
        
        # Extract content based on capability
        if capability == ModelCapability.EMBEDDINGS:
            content = f"Generated {len(result.get('embeddings', []))} embeddings"
        else:
            content = result.get("text") or result.get("message", {}).get("content", "")
        
        # Get alternative providers
        alternative_providers = []
        for provider_type, provider in multi_provider_manager.providers.items():
            if provider.config.is_enabled:
                models = provider.get_models_by_capability(capability)
                if models:
                    alternative_providers.append(provider_type.value)
        
        # Restore original routing strategy
        if request.routing_strategy:
            multi_provider_manager.set_routing_strategy(original_strategy)
        
        return MultiProviderGenerationResponse(
            content=content,
            provider_used=result.get("provider", "unknown"),
            model_used=result.get("model", "unknown"),
            response_time=response_time,
            tokens_used=result.get("usage", {}).get("total_tokens", 0),
            cost=0.0,  # Would calculate based on provider pricing
            routing_strategy=multi_provider_manager.routing_strategy.value,
            fallbacks_used=0,  # Would track in execute_with_fallback
            alternative_providers=alternative_providers,
            quality_score=None,  # Would calculate based on response quality
            confidence=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in multi-provider generation: {e}")
        raise HTTPException(status_code=500, detail="Generation failed")


# Analytics Endpoints
@router.post("/analytics", response_model=AnalyticsResponse)
async def get_analytics(
    request: AnalyticsRequest,
    user_session = Depends(require_permission(Permission.ANALYTICS_READ))
):
    """Get comprehensive AI analytics and insights."""
    try:
        # Parse time window
        try:
            time_window = TimeWindow(request.time_window)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid time window: {request.time_window}"
            )
        
        # Get analytics summary
        summary = ai_analytics_engine.get_analytics_summary()
        
        # Get usage patterns
        usage_patterns = {}
        if "usage_patterns" in request.metrics:
            patterns = await ai_analytics_engine.analyze_usage_patterns(time_window)
            usage_patterns = {
                name: {
                    "provider": pattern.provider.value,
                    "model": pattern.model_id,
                    "capability": pattern.capability.value,
                    "requests": pattern.request_count,
                    "avg_response_time": pattern.avg_response_time,
                    "success_rate": pattern.success_rate,
                    "cost_per_request": pattern.cost_per_request,
                    "quality_score": pattern.quality_score,
                    "peak_hours": pattern.peak_hours
                }
                for name, pattern in patterns.items()
            }
        
        # Get performance trends
        performance_trends = {}
        if request.include_trends and "performance_trends" in request.metrics:
            for metric in ["response_time", "cost", "success"]:
                trend = await ai_analytics_engine.analyze_performance_trends(metric, time_window)
                performance_trends[metric] = {
                    "trend_direction": trend.trend_direction,
                    "trend_strength": trend.trend_strength,
                    "anomalies_count": len(trend.anomalies),
                    "data_points": len(trend.time_series)
                }
        
        # Get model comparisons
        model_comparisons = {}
        if request.include_comparisons and "model_comparisons" in request.metrics:
            if request.capability:
                try:
                    capability = ModelCapability(request.capability)
                    comparison = await ai_analytics_engine.compare_models(capability, time_window)
                    model_comparisons[capability.value] = {
                        "models": comparison.models,
                        "rankings": comparison.rankings,
                        "recommendations": comparison.recommendations
                    }
                except ValueError:
                    pass
        
        # Generate recommendations
        recommendations = [
            "Consider using cost-optimized routing for non-critical requests",
            "Monitor response time trends for performance optimization",
            "Evaluate model performance regularly for quality improvements"
        ]
        
        return AnalyticsResponse(
            summary=summary,
            usage_patterns=usage_patterns,
            performance_trends=performance_trends,
            model_comparisons=model_comparisons,
            recommendations=recommendations
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get analytics")


@router.get("/analytics/real-time")
async def get_real_time_analytics(
    user_session = Depends(require_permission(Permission.ANALYTICS_READ))
):
    """Get real-time analytics and metrics."""
    try:
        # Get real-time metrics from analytics engine
        real_time_data = ai_analytics_engine.real_time_metrics
        
        # Get provider status
        provider_health = {}
        for provider_type, provider in multi_provider_manager.providers.items():
            provider_health[provider_type.value] = await provider.health_check()
        
        # Get global analytics
        global_analytics = multi_provider_manager.get_global_analytics()
        
        return {
            "timestamp": time.time(),
            "real_time_metrics": real_time_data,
            "provider_health": provider_health,
            "global_analytics": global_analytics,
            "active_providers": len([
                p for p in multi_provider_manager.providers.values()
                if p.config.is_enabled
            ]),
            "routing_strategy": multi_provider_manager.routing_strategy.value
        }
        
    except Exception as e:
        logger.error(f"Error getting real-time analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get real-time analytics")


# System Status
@router.get("/status")
async def get_multi_ai_status():
    """Get overall multi-AI system status."""
    try:
        # Get provider status
        provider_status = {}
        for provider_type, provider in multi_provider_manager.providers.items():
            health = await provider.health_check()
            stats = provider.get_performance_stats()
            
            provider_status[provider_type.value] = {
                "health": "healthy" if health else "unhealthy",
                "enabled": provider.config.is_enabled,
                "models_count": len(provider.get_available_models()),
                "success_rate": stats.get("success_rate", 0),
                "avg_response_time": stats.get("avg_response_time", 0)
            }
        
        # Get analytics status
        analytics_summary = ai_analytics_engine.get_analytics_summary()
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "providers": provider_status,
            "routing_strategy": multi_provider_manager.routing_strategy.value,
            "analytics": {
                "patterns_tracked": len(analytics_summary.get("usage_patterns", {})),
                "trends_tracked": len(analytics_summary.get("performance_trends", {})),
                "real_time_requests": analytics_summary.get("real_time_metrics", {}).get("total_requests_5min", 0)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting multi-AI status: {e}")
        return {
            "status": "error",
            "timestamp": time.time(),
            "error": str(e)
        }
