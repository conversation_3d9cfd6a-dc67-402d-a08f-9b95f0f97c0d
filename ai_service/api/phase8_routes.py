"""
Phase 8 API Routes: AI Intelligence & Automation.
Includes ML-based routing, predictive scaling, model ensembling, and semantic caching.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from ai_service.core.ai_intelligence_engine import (
    ai_intelligence_engine, PredictionType, FeatureVector
)
from ai_service.core.predictive_scaling_manager import (
    predictive_scaling_manager, ScalingAction, ScalingMetrics, ResourceType
)
from ai_service.core.model_ensembling_engine import (
    model_ensembling_engine, EnsemblingStrategy, OutputType, EnsembleRequest, ModelOutput
)
from ai_service.core.semantic_caching_engine import (
    semantic_caching_engine, SimilarityMetric, CacheStrategy
)
from ai_service.core.enhanced_auth_manager import enhanced_auth_manager, Permission
from ai_service.api.phase5_routes import require_permission

logger = logging.getLogger(__name__)

# Router
router = APIRouter(prefix="/api/v1/ai-intelligence", tags=["Phase 8 - AI Intelligence"])


# Pydantic Models
class IntelligentRoutingRequest(BaseModel):
    """Intelligent routing request."""
    prompt: str = Field(..., description="Input prompt", max_length=8000)
    capability: str = Field(..., description="Required capability")
    
    # Context
    user_tier: str = Field("standard", description="User tier")
    priority: str = Field("normal", description="Request priority")
    region: str = Field("us-east-1", description="Request region")
    
    # Performance requirements
    max_response_time: Optional[float] = Field(None, description="Maximum response time")
    min_quality_score: Optional[float] = Field(None, description="Minimum quality score")
    max_cost: Optional[float] = Field(None, description="Maximum cost")


class PredictiveScalingRequest(BaseModel):
    """Predictive scaling request."""
    # Current metrics
    cpu_utilization: float = Field(..., description="CPU utilization", ge=0.0, le=1.0)
    memory_utilization: float = Field(..., description="Memory utilization", ge=0.0, le=1.0)
    request_rate: float = Field(..., description="Current request rate")
    queue_length: int = Field(..., description="Current queue length", ge=0)
    
    # Performance metrics
    avg_response_time: float = Field(..., description="Average response time")
    error_rate: float = Field(..., description="Error rate", ge=0.0, le=1.0)
    cache_hit_rate: float = Field(..., description="Cache hit rate", ge=0.0, le=1.0)
    
    # Business metrics
    active_users: int = Field(..., description="Active users", ge=0)
    revenue_per_hour: float = Field(0.0, description="Revenue per hour")
    cost_per_request: float = Field(0.0, description="Cost per request")


class ModelEnsemblingRequest(BaseModel):
    """Model ensembling request."""
    prompt: str = Field(..., description="Input prompt", max_length=8000)
    capability: str = Field(..., description="Required capability")
    output_type: str = Field("text", description="Output type")
    
    # Ensembling configuration
    strategy: str = Field("quality_selection", description="Ensembling strategy")
    models: List[str] = Field(..., description="Models to ensemble")
    min_models: int = Field(2, description="Minimum models", ge=1, le=10)
    max_models: int = Field(5, description="Maximum models", ge=1, le=10)
    
    # Quality requirements
    min_confidence: float = Field(0.7, description="Minimum confidence", ge=0.0, le=1.0)
    required_consensus: float = Field(0.6, description="Required consensus", ge=0.0, le=1.0)
    
    # Performance constraints
    max_response_time: float = Field(10.0, description="Maximum response time")
    max_cost: float = Field(0.1, description="Maximum cost")


class SemanticCacheRequest(BaseModel):
    """Semantic cache request."""
    prompt: str = Field(..., description="Input prompt", max_length=8000)
    provider: Optional[str] = Field(None, description="AI provider")
    model_id: Optional[str] = Field(None, description="Model ID")
    similarity_threshold: Optional[float] = Field(None, description="Similarity threshold", ge=0.0, le=1.0)


class CacheResponseRequest(BaseModel):
    """Cache response request."""
    prompt: str = Field(..., description="Input prompt", max_length=8000)
    response_content: Any = Field(..., description="Response content")
    response_metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")
    provider: str = Field(..., description="AI provider")
    model_id: str = Field(..., description="Model ID")
    quality_score: float = Field(0.8, description="Quality score", ge=0.0, le=1.0)
    confidence: float = Field(0.8, description="Confidence score", ge=0.0, le=1.0)
    ttl: Optional[float] = Field(None, description="Time to live in seconds")


# AI Intelligence Endpoints
@router.post("/routing/predict")
async def predict_optimal_routing(
    request: IntelligentRoutingRequest,
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """Predict optimal routing using ML."""
    try:
        # Prepare request data for ML model
        request_data = {
            "prompt": request.prompt,
            "capability": request.capability,
            "user_tier": request.user_tier,
            "priority": request.priority,
            "region": request.region,
            "provider_health": {"gemini": 0.9, "openai": 0.8, "anthropic": 0.85},
            "provider_load": {"gemini": 0.3, "openai": 0.5, "anthropic": 0.4},
            "provider_performance": {"gemini": 0.85, "openai": 0.80, "anthropic": 0.82},
            "global_load": 0.4,
            "cache_hit_rate": 0.7,
            "error_rate": 0.02,
            "recent_latency": 0.5,
            "recent_success_rate": 0.95,
            "recent_cost": 0.001
        }
        
        # Get ML prediction
        prediction = await ai_intelligence_engine.predict_optimal_routing(request_data)
        
        if not prediction:
            return {
                "prediction_available": False,
                "message": "ML model not trained yet, using fallback routing"
            }
        
        return {
            "prediction_available": True,
            "recommended_provider": prediction.value,
            "confidence": prediction.confidence,
            "model_version": prediction.model_version,
            "features_used": prediction.features_used,
            "reasoning": f"ML model recommends {prediction.value} with {prediction.confidence:.2%} confidence"
        }
        
    except Exception as e:
        logger.error(f"Error in intelligent routing prediction: {e}")
        raise HTTPException(status_code=500, detail="Failed to predict optimal routing")


@router.post("/quality/predict")
async def predict_quality_score(
    request: IntelligentRoutingRequest,
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """Predict quality score for request."""
    try:
        request_data = {
            "prompt": request.prompt,
            "capability": request.capability,
            "user_tier": request.user_tier,
            "priority": request.priority,
            "region": request.region,
            "provider_health": {"gemini": 0.9, "openai": 0.8, "anthropic": 0.85},
            "provider_load": {"gemini": 0.3, "openai": 0.5, "anthropic": 0.4},
            "global_load": 0.4
        }
        
        prediction = await ai_intelligence_engine.predict_quality(request_data)
        
        if not prediction:
            return {
                "prediction_available": False,
                "estimated_quality": 0.8,
                "message": "Quality prediction model not available"
            }
        
        return {
            "prediction_available": True,
            "estimated_quality": prediction.value,
            "confidence": prediction.confidence,
            "model_version": prediction.model_version,
            "factors": prediction.features_used
        }
        
    except Exception as e:
        logger.error(f"Error in quality prediction: {e}")
        raise HTTPException(status_code=500, detail="Failed to predict quality")


@router.post("/anomaly/detect")
async def detect_anomalies(
    request: IntelligentRoutingRequest,
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Detect anomalies in system behavior."""
    try:
        request_data = {
            "prompt": request.prompt,
            "capability": request.capability,
            "recent_latency": 2.5,  # Simulated high latency
            "error_rate": 0.08,     # Simulated high error rate
            "provider_health": {"gemini": 0.6, "openai": 0.9, "anthropic": 0.8},
            "global_load": 0.8
        }
        
        prediction = await ai_intelligence_engine.detect_anomalies(request_data)
        
        if not prediction:
            return {
                "anomaly_detected": False,
                "message": "Anomaly detection model not available"
            }
        
        anomaly_probability = prediction.value
        is_anomaly = anomaly_probability > 0.5
        
        return {
            "anomaly_detected": is_anomaly,
            "anomaly_probability": anomaly_probability,
            "confidence": prediction.confidence,
            "severity": "high" if anomaly_probability > 0.8 else "medium" if anomaly_probability > 0.5 else "low",
            "factors": prediction.features_used,
            "recommendations": [
                "Check provider health" if anomaly_probability > 0.7 else None,
                "Scale up resources" if anomaly_probability > 0.6 else None,
                "Review error logs" if anomaly_probability > 0.5 else None
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in anomaly detection: {e}")
        raise HTTPException(status_code=500, detail="Failed to detect anomalies")


@router.get("/intelligence/status")
async def get_intelligence_status(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get AI intelligence engine status."""
    try:
        status = ai_intelligence_engine.get_intelligence_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting intelligence status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get intelligence status")


# Predictive Scaling Endpoints
@router.post("/scaling/predict")
async def predict_scaling_needs(
    request: PredictiveScalingRequest,
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Predict scaling needs based on metrics."""
    try:
        # Create scaling metrics
        metrics = ScalingMetrics(
            cpu_utilization=request.cpu_utilization,
            memory_utilization=request.memory_utilization,
            request_rate=request.request_rate,
            queue_length=request.queue_length,
            avg_response_time=request.avg_response_time,
            error_rate=request.error_rate,
            cache_hit_rate=request.cache_hit_rate,
            active_users=request.active_users,
            revenue_per_hour=request.revenue_per_hour,
            cost_per_request=request.cost_per_request,
            predicted_load_1h=request.request_rate * 1.2,  # Simple prediction
            predicted_load_6h=request.request_rate * 1.5,
            predicted_load_24h=request.request_rate * 1.1
        )
        
        # Record metrics
        predictive_scaling_manager.record_metrics(metrics)
        
        # Get load prediction
        load_prediction = await predictive_scaling_manager.predict_load()
        
        # Evaluate scaling decisions for all pools
        scaling_recommendations = {}
        for pool_id in predictive_scaling_manager.resource_pools.keys():
            action = await predictive_scaling_manager.evaluate_scaling_decision(pool_id)
            if action and action != ScalingAction.MAINTAIN:
                scaling_recommendations[pool_id] = {
                    "action": action.value,
                    "pool": predictive_scaling_manager.resource_pools[pool_id].resource_type.value,
                    "current_capacity": predictive_scaling_manager.resource_pools[pool_id].current_capacity,
                    "utilization": predictive_scaling_manager.resource_pools[pool_id].utilization
                }
        
        return {
            "load_prediction": load_prediction,
            "scaling_recommendations": scaling_recommendations,
            "current_metrics": {
                "cpu_utilization": request.cpu_utilization,
                "memory_utilization": request.memory_utilization,
                "request_rate": request.request_rate,
                "queue_length": request.queue_length
            },
            "system_health": "healthy" if request.cpu_utilization < 0.8 else "warning"
        }
        
    except Exception as e:
        logger.error(f"Error in scaling prediction: {e}")
        raise HTTPException(status_code=500, detail="Failed to predict scaling needs")


@router.post("/scaling/execute/{pool_id}")
async def execute_scaling_action(
    pool_id: str,
    action: str,
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Execute scaling action for resource pool."""
    try:
        scaling_action = ScalingAction(action)
        
        success = await predictive_scaling_manager.execute_scaling_action(
            pool_id=pool_id,
            action=scaling_action
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to execute scaling action")
        
        return {
            "message": f"Successfully executed {action} for {pool_id}",
            "pool_id": pool_id,
            "action": action,
            "timestamp": time.time()
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid scaling action: {action}")
    except Exception as e:
        logger.error(f"Error executing scaling action: {e}")
        raise HTTPException(status_code=500, detail="Failed to execute scaling action")


@router.get("/scaling/status")
async def get_scaling_status(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get predictive scaling status."""
    try:
        status = predictive_scaling_manager.get_scaling_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting scaling status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get scaling status")


# Model Ensembling Endpoints
@router.post("/ensemble/generate")
async def ensemble_model_generation(
    request: ModelEnsemblingRequest,
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """Generate content using model ensembling."""
    try:
        # Parse enums
        strategy = EnsemblingStrategy(request.strategy)
        output_type = OutputType(request.output_type)
        
        # Create ensemble request
        ensemble_request = EnsembleRequest(
            request_id=f"ensemble_{int(time.time())}",
            prompt=request.prompt,
            capability=request.capability,
            output_type=output_type,
            strategy=strategy,
            models=request.models,
            min_models=request.min_models,
            max_models=request.max_models,
            min_confidence=request.min_confidence,
            required_consensus=request.required_consensus,
            max_response_time=request.max_response_time,
            max_cost=request.max_cost
        )
        
        # Mock model outputs (in production, would call actual models)
        model_outputs = []
        for i, model in enumerate(request.models[:request.max_models]):
            output = ModelOutput(
                model_id=model,
                provider=model.split(':')[0] if ':' in model else 'unknown',
                content=f"Generated response from {model}: {request.prompt[:50]}...",
                confidence=0.7 + (i * 0.1),
                response_time=0.5 + (i * 0.2),
                tokens_used=100 + (i * 20),
                cost=0.001 * (100 + (i * 20))
            )
            model_outputs.append(output)
        
        # Ensemble the outputs
        result = await model_ensembling_engine.ensemble_models(ensemble_request, model_outputs)
        
        return {
            "request_id": result.request_id,
            "final_output": result.final_output,
            "strategy_used": result.strategy_used.value,
            "ensemble_confidence": result.ensemble_confidence,
            "consensus_score": result.consensus_score,
            "models_used": result.models_used,
            "total_response_time": result.total_response_time,
            "total_cost": result.total_cost,
            "selection_reasoning": result.selection_reasoning,
            "individual_outputs": [
                {
                    "model_id": output.model_id,
                    "provider": output.provider,
                    "confidence": output.confidence,
                    "response_time": output.response_time,
                    "cost": output.cost
                }
                for output in result.model_outputs
            ]
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in model ensembling: {e}")
        raise HTTPException(status_code=500, detail="Failed to ensemble models")


@router.get("/ensemble/status")
async def get_ensembling_status(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get model ensembling status."""
    try:
        status = model_ensembling_engine.get_ensembling_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting ensembling status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get ensembling status")


# Semantic Caching Endpoints
@router.post("/cache/get")
async def get_semantic_cache(
    request: SemanticCacheRequest,
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """Get cached response using semantic similarity."""
    try:
        cache_hit = await semantic_caching_engine.get_cached_response(
            prompt=request.prompt,
            provider=request.provider,
            model_id=request.model_id,
            similarity_threshold=request.similarity_threshold
        )
        
        if cache_hit:
            return {
                "cache_hit": True,
                "response_content": cache_hit.entry.response_content,
                "response_metadata": cache_hit.entry.response_metadata,
                "similarity_score": cache_hit.similarity_score,
                "similarity_metric": cache_hit.similarity_metric.value,
                "cache_age": cache_hit.cache_age,
                "is_exact_match": cache_hit.is_exact_match,
                "original_prompt": cache_hit.entry.original_prompt,
                "provider": cache_hit.entry.provider,
                "model_id": cache_hit.entry.model_id,
                "quality_score": cache_hit.entry.quality_score,
                "confidence": cache_hit.entry.confidence
            }
        else:
            return {
                "cache_hit": False,
                "message": "No semantically similar cached response found"
            }
        
    except Exception as e:
        logger.error(f"Error getting semantic cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cached response")


@router.post("/cache/store")
async def store_semantic_cache(
    request: CacheResponseRequest,
    user_session = Depends(require_permission(Permission.AI_GENERATE_TEXT))
):
    """Store response in semantic cache."""
    try:
        cache_key = await semantic_caching_engine.cache_response(
            prompt=request.prompt,
            response_content=request.response_content,
            response_metadata=request.response_metadata,
            provider=request.provider,
            model_id=request.model_id,
            quality_score=request.quality_score,
            confidence=request.confidence,
            ttl=request.ttl
        )
        
        return {
            "message": "Response cached successfully",
            "cache_key": cache_key,
            "prompt": request.prompt[:100] + "..." if len(request.prompt) > 100 else request.prompt,
            "provider": request.provider,
            "model_id": request.model_id,
            "quality_score": request.quality_score,
            "confidence": request.confidence
        }
        
    except Exception as e:
        logger.error(f"Error storing semantic cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to store cached response")


@router.get("/cache/stats")
async def get_cache_stats(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get semantic cache statistics."""
    try:
        stats = semantic_caching_engine.get_cache_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cache statistics")


@router.delete("/cache/invalidate")
async def invalidate_cache(
    pattern: Optional[str] = Query(None, description="Pattern to match for invalidation"),
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Invalidate semantic cache entries."""
    try:
        await semantic_caching_engine.invalidate_cache(pattern)
        
        return {
            "message": "Cache invalidated successfully",
            "pattern": pattern or "all entries",
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Error invalidating cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to invalidate cache")


# System Status
@router.get("/status")
async def get_ai_intelligence_status():
    """Get overall AI intelligence system status."""
    try:
        # Get status from all components
        intelligence_status = ai_intelligence_engine.get_intelligence_status()
        scaling_status = predictive_scaling_manager.get_scaling_status()
        ensembling_status = model_ensembling_engine.get_ensembling_status()
        cache_stats = semantic_caching_engine.get_cache_stats()
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "ai_intelligence": {
                "models_trained": sum(1 for model in intelligence_status["models"].values() if model["is_trained"]),
                "total_predictions": intelligence_status["total_predictions"],
                "auto_training_enabled": intelligence_status["auto_training_enabled"]
            },
            "predictive_scaling": {
                "resource_pools": len(scaling_status["resource_pools"]),
                "recent_events": scaling_status["recent_events"],
                "prediction_accuracy": scaling_status["prediction_accuracy"]
            },
            "model_ensembling": {
                "total_ensembles": ensembling_status["total_ensembles"],
                "average_consensus": ensembling_status["average_consensus"],
                "average_models_used": ensembling_status["average_models_used"]
            },
            "semantic_caching": {
                "hit_rate": cache_stats["hit_rate"],
                "total_entries": cache_stats["total_entries"],
                "storage_size_mb": cache_stats["storage_size_mb"]
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting AI intelligence status: {e}")
        return {
            "status": "error",
            "timestamp": time.time(),
            "error": str(e)
        }
