"""
API routes for AI operations.
"""
import logging
import os
from typing import Dict, List, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Form, Response
from fastapi.responses import StreamingResponse

from ai_service.models.schemas import (
    TextGenerationRequest,
    TextGenerationResponse,
    StreamChunk,
    ChatRequest,
    ChatResponse,
    MultimodalGenerationRequest,
    FunctionCallingRequest,
    FunctionCallingResponse,
    FunctionCall,
    FunctionResponse,
    EmbeddingRequest,
    EmbeddingResponse,
    TokenCountRequest,
    TokenCountResponse,
    TTSRequest,
    TTSResponse,
    ErrorResponse,
    JSONSchema,
    # Context Caching schemas
    CacheCreateRequest,
    CacheUpdateRequest,
    CacheResponse,
    CacheListResponse,
    CachedGenerationRequest,
    # Enhanced Function Calling schemas
    EnhancedFunctionCallingRequest,
    EnhancedFunctionCallingResponse
)
from ai_service.services.ai_service import AIService
from ai_service.config.settings import settings
from ai_service.core.validators import RequestValidator
from ai_service.core.exceptions import AIServiceException, ValidationError, log_error

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/ai", tags=["AI"])


def get_ai_service() -> AIService:
    """Dependency for getting the AI service instance."""
    return AIService()


@router.post(
    "/generate",
    response_model=TextGenerationResponse,
    responses={
        200: {"model": TextGenerationResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_text(
    request: TextGenerationRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate text using the AI model."""
    request_id = f"gen_{id(request)}"

    try:
        # Validate request
        validated_prompt = RequestValidator.validate_prompt(request.prompt)
        validated_model = RequestValidator.validate_model(request.model or settings.default_model)
        validated_params = RequestValidator.validate_generation_params(
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            max_tokens=request.max_tokens
        )

        # Update request with validated values
        request.prompt = validated_prompt
        request.model = validated_model

        logger.info(f"[{request_id}] Processing text generation request for model {validated_model}")

        if request.stream:
            return StreamingResponse(
                ai_service.generate_text(request, stream=True),
                media_type="application/json"
            )
        else:
            return await ai_service.generate_text(request)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error generating text: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/chat",
    response_model=ChatResponse,
    responses={
        200: {"model": ChatResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def chat_completion(
    request: ChatRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate chat completion using the AI model."""
    try:
        if request.stream:
            return StreamingResponse(
                ai_service.chat_completion(request, stream=True),
                media_type="application/json"
            )
        else:
            return await ai_service.chat_completion(request)
    except Exception as e:
        logger.error(f"Error generating chat completion: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to generate chat completion", "details": {"message": str(e)}}
        )


@router.post(
    "/generate-json",
    response_model=TextGenerationResponse,
    responses={
        200: {"model": TextGenerationResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_json(
    request: TextGenerationRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate structured JSON output using the AI model."""
    request_id = f"json_{id(request)}"

    try:
        # Validate request
        validated_prompt = RequestValidator.validate_prompt(request.prompt)
        request.prompt = validated_prompt

        validated_model = RequestValidator.validate_model(request.model or settings.default_model)
        validated_params = RequestValidator.validate_generation_params(
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            max_tokens=request.max_tokens
        )

        # Ensure JSON mode is enabled
        request.json_mode = True

        # Update request with validated values
        request.model = validated_model

        logger.info(f"[{request_id}] Processing JSON generation request for model {validated_model}")

        if request.stream:
            return StreamingResponse(
                ai_service.generate_text(request, stream=True),
                media_type="application/json"
            )
        else:
            return await ai_service.generate_text(request)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error generating JSON: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/multimodal",
    response_model=TextGenerationResponse,
    responses={
        200: {"model": TextGenerationResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def multimodal_generation(
    request: MultimodalGenerationRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate content using multimodal inputs (text + images)."""
    request_id = f"multimodal_{id(request)}"

    try:
        # Validate request - must have either text or images
        if not request.text and not request.image_parts:
            raise ValidationError("Either text or image_parts must be provided", field="request")

        # Validate text if provided
        if request.text:
            validated_text = RequestValidator.validate_prompt(request.text)
            request.text = validated_text

        # Validate that we have at least some content
        if not request.text and not request.image_parts:
            raise ValidationError("Request must contain either text or images", field="content")

        validated_model = RequestValidator.validate_model(request.model or settings.default_model)
        validated_params = RequestValidator.validate_generation_params(
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            max_tokens=request.max_tokens
        )

        # Convert image parts to the format expected by the AI client
        image_parts = []
        for img_part in request.image_parts:
            if img_part.type == "url":
                image_parts.append(img_part.data)  # URL string
            elif img_part.type == "base64":
                image_parts.append({
                    "type": "base64",
                    "data": img_part.data,
                    "mime_type": img_part.mime_type
                })
            elif img_part.type == "file_uri":
                image_parts.append({
                    "file_uri": img_part.data,
                    "mime_type": img_part.mime_type
                })

        # Update request with validated values
        request.model = validated_model

        logger.info(f"[{request_id}] Processing multimodal generation request for model {validated_model}")

        if request.stream:
            # Get the async generator from the service
            async_generator = await ai_service.multimodal_generation(request, image_parts=image_parts, stream=True)
            return StreamingResponse(
                async_generator,
                media_type="application/json"
            )
        else:
            return await ai_service.multimodal_generation(request, image_parts=image_parts)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error generating multimodal content: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/multimodal/upload",
    responses={
        200: {"model": Dict[str, Any]},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def upload_image(
    file: UploadFile = File(...),
    ai_service: AIService = Depends(get_ai_service)
):
    """Upload an image for multimodal generation."""
    try:
        # Save the uploaded file temporarily
        temp_file_path = os.path.join(settings.temp_file_dir, f"temp_{file.filename}")
        with open(temp_file_path, "wb") as f:
            f.write(await file.read())

        # Upload the file to the API
        result = await ai_service.upload_file(
            file_path=temp_file_path,
            mime_type=file.content_type
        )

        # Clean up the temporary file
        os.remove(temp_file_path)

        return result
    except Exception as e:
        logger.error(f"Error uploading image: {str(e)}")
        # Clean up the temporary file if it exists
        temp_file_path = os.path.join(settings.temp_file_dir, f"temp_{file.filename}")
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to upload image", "details": {"message": str(e)}}
        )


@router.post(
    "/function-calling",
    response_model=FunctionCallingResponse,
    responses={
        200: {"model": FunctionCallingResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def function_calling(
    request: FunctionCallingRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate content with function calling capabilities."""
    try:
        return await ai_service.function_calling(request)
    except Exception as e:
        logger.error(f"Error with function calling: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to process function calling", "details": {"message": str(e)}}
        )


@router.post(
    "/function-response",
    response_model=TextGenerationResponse,
    responses={
        200: {"model": TextGenerationResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def function_response(
    function_call: FunctionCall,
    function_response: FunctionResponse,
    chat_history: List[Dict[str, Any]],
    ai_service: AIService = Depends(get_ai_service)
):
    """Process function response and continue the conversation."""
    try:
        return await ai_service.function_response(
            function_call=function_call,
            function_response=function_response,
            chat_history=chat_history
        )
    except Exception as e:
        logger.error(f"Error processing function response: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to process function response", "details": {"message": str(e)}}
        )


@router.post(
    "/embeddings",
    response_model=EmbeddingResponse,
    responses={
        200: {"model": EmbeddingResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_embeddings(
    request: EmbeddingRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate embeddings for text."""
    try:
        return await ai_service.generate_embeddings(request)
    except Exception as e:
        logger.error(f"Error generating embeddings: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to generate embeddings", "details": {"message": str(e)}}
        )


@router.post(
    "/token-count",
    response_model=TokenCountResponse,
    responses={
        200: {"model": TokenCountResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def count_tokens(
    request: TokenCountRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Count tokens in text."""
    try:
        return await ai_service.count_tokens(request)
    except Exception as e:
        logger.error(f"Error counting tokens: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to count tokens", "details": {"message": str(e)}}
        )


@router.post(
    "/files",
    responses={
        200: {"model": Dict[str, Any]},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def upload_file(
    file: UploadFile = File(...),
    ai_service: AIService = Depends(get_ai_service)
):
    """Upload a file to the API."""
    try:
        # Save the uploaded file temporarily
        temp_file_path = os.path.join(settings.temp_file_dir, f"temp_{file.filename}")
        with open(temp_file_path, "wb") as f:
            f.write(await file.read())

        # Upload the file to the API
        result = await ai_service.upload_file(
            file_path=temp_file_path,
            mime_type=file.content_type
        )

        # Clean up the temporary file
        os.remove(temp_file_path)

        return result
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        # Clean up the temporary file if it exists
        temp_file_path = os.path.join(settings.temp_file_dir, f"temp_{file.filename}")
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to upload file", "details": {"message": str(e)}}
        )


@router.get(
    "/files",
    responses={
        200: {"model": Dict[str, Any]},
        500: {"model": ErrorResponse}
    }
)
async def list_files(
    ai_service: AIService = Depends(get_ai_service)
):
    """List all files."""
    try:
        return await ai_service.list_files()
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to list files", "details": {"message": str(e)}}
        )


@router.get(
    "/files/{file_id}",
    responses={
        200: {"model": Dict[str, Any]},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_file(
    file_id: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """Get information about a file."""
    try:
        return await ai_service.get_file(file_id)
    except Exception as e:
        logger.error(f"Error getting file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to get file", "details": {"message": str(e)}}
        )


@router.delete(
    "/files/{file_id}",
    responses={
        200: {"model": Dict[str, Any]},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def delete_file(
    file_id: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """Delete a file."""
    try:
        return await ai_service.delete_file(file_id)
    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to delete file", "details": {"message": str(e)}}
        )


@router.post(
    "/tts",
    responses={
        200: {"content": {"audio/mpeg": {}, "audio/wav": {}, "audio/ogg": {}}},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def text_to_speech(
    request: TTSRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Convert text to speech and return audio file."""
    request_id = f"tts_{id(request)}"

    try:
        # Validate request
        validated_text = RequestValidator.validate_prompt(request.text)
        request.text = validated_text

        logger.info(f"[{request_id}] Processing TTS request: {request.text[:50]}... (voice: {request.voice})")

        # Generate speech
        tts_response = await ai_service.text_to_speech(request)

        # Determine content type based on audio format
        content_type_map = {
            "mp3": "audio/mpeg",
            "wav": "audio/wav",
            "ogg": "audio/ogg"
        }
        content_type = content_type_map.get(request.audio_format.lower(), "audio/mpeg")

        # Create filename (safe for HTTP headers)
        import re
        import unicodedata

        # Normalize and remove diacritics for safe filename
        normalized_text = unicodedata.normalize('NFD', request.text[:30])
        ascii_text = ''.join(c for c in normalized_text if unicodedata.category(c) != 'Mn')
        safe_text = re.sub(r'[^\w\s-]', '', ascii_text).strip()
        safe_text = re.sub(r'[-\s]+', '_', safe_text)
        filename = f"tts_{safe_text[:20]}.{request.audio_format}" if safe_text else f"tts_audio.{request.audio_format}"

        # Return audio as streaming response
        def generate_audio():
            yield tts_response.audio_content

        return StreamingResponse(
            generate_audio(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "X-Audio-Duration": str(tts_response.duration_seconds or 0),
                "X-Voice-Used": tts_response.voice_used,
                "X-Language-Code": tts_response.language_code,
                "X-Sample-Rate": str(tts_response.sample_rate),
                "X-Text-Length": str(tts_response.text_length)
            }
        )

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error in TTS: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


# Context Caching Endpoints
@router.post(
    "/cache",
    response_model=CacheResponse,
    responses={
        200: {"model": CacheResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def create_cache(
    request: CacheCreateRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Create a new context cache."""
    request_id = f"cache_create_{id(request)}"

    try:
        logger.info(f"[{request_id}] Creating context cache for model {request.model}")

        # Validate model
        validated_model = RequestValidator.validate_model(request.model)
        request.model = validated_model

        return await ai_service.create_cache(request)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error creating cache: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.get(
    "/cache/{cache_name}",
    response_model=CacheResponse,
    responses={
        200: {"model": CacheResponse},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_cache(
    cache_name: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """Get cache information."""
    try:
        logger.info(f"Getting cache: {cache_name}")
        return await ai_service.get_cache(cache_name)

    except AIServiceException as e:
        log_error(e, f"cache_get_{cache_name}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Unexpected error getting cache {cache_name}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.get(
    "/cache",
    response_model=CacheListResponse,
    responses={
        200: {"model": CacheListResponse},
        500: {"model": ErrorResponse}
    }
)
async def list_caches(
    page_size: int = 10,
    page_token: Optional[str] = None,
    ai_service: AIService = Depends(get_ai_service)
):
    """List all context caches."""
    try:
        logger.info("Listing context caches")
        return await ai_service.list_caches(page_size, page_token)

    except AIServiceException as e:
        log_error(e, "cache_list")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Unexpected error listing caches: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.patch(
    "/cache/{cache_name}",
    response_model=CacheResponse,
    responses={
        200: {"model": CacheResponse},
        400: {"model": ErrorResponse},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def update_cache(
    cache_name: str,
    request: CacheUpdateRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Update an existing context cache."""
    try:
        logger.info(f"Updating cache: {cache_name}")
        return await ai_service.update_cache(cache_name, request)

    except AIServiceException as e:
        log_error(e, f"cache_update_{cache_name}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Unexpected error updating cache {cache_name}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.delete(
    "/cache/{cache_name}",
    responses={
        200: {"model": Dict[str, Any]},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def delete_cache(
    cache_name: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """Delete a context cache."""
    try:
        logger.info(f"Deleting cache: {cache_name}")
        return await ai_service.delete_cache(cache_name)

    except AIServiceException as e:
        log_error(e, f"cache_delete_{cache_name}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Unexpected error deleting cache {cache_name}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/generate-cached",
    response_model=TextGenerationResponse,
    responses={
        200: {"model": TextGenerationResponse},
        400: {"model": ErrorResponse},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_with_cache(
    request: CachedGenerationRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate content using cached context."""
    request_id = f"cached_gen_{id(request)}"

    try:
        # Validate request
        validated_prompt = RequestValidator.validate_prompt(request.prompt)
        request.prompt = validated_prompt

        if request.model:
            validated_model = RequestValidator.validate_model(request.model)
            request.model = validated_model

        validated_params = RequestValidator.validate_generation_params(
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            max_tokens=request.max_tokens
        )

        logger.info(f"[{request_id}] Processing cached generation request for cache {request.cache_name}")

        if request.stream:
            return StreamingResponse(
                ai_service.generate_with_cache(request, stream=True),
                media_type="application/json"
            )
        else:
            return await ai_service.generate_with_cache(request)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error in cached generation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


# Enhanced Function Calling Endpoints
@router.post(
    "/enhanced-function-calling",
    response_model=EnhancedFunctionCallingResponse,
    responses={
        200: {"model": EnhancedFunctionCallingResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def enhanced_function_calling(
    request: EnhancedFunctionCallingRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Enhanced function calling with chaining and parallel execution."""
    request_id = f"enhanced_func_{id(request)}"

    try:
        # Validate request
        validated_prompt = RequestValidator.validate_prompt(request.prompt)
        request.prompt = validated_prompt

        validated_model = RequestValidator.validate_model(request.model or settings.default_model)
        request.model = validated_model

        validated_params = RequestValidator.validate_generation_params(
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            max_tokens=request.max_tokens
        )

        logger.info(f"[{request_id}] Processing enhanced function calling request")

        return await ai_service.enhanced_function_calling(request)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error in enhanced function calling: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/functions/register",
    responses={
        200: {"model": Dict[str, Any]},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def register_function(
    function_data: Dict[str, Any],
    ai_service: AIService = Depends(get_ai_service)
):
    """Register a function for enhanced function calling."""
    try:
        # This is a simplified endpoint for demonstration
        # In production, you would implement proper function registration
        function_name = function_data.get("name")
        if not function_name:
            raise HTTPException(status_code=400, detail={"error": "Function name is required"})

        logger.info(f"Function registration request for: {function_name}")

        # For demo purposes, return success
        return {
            "success": True,
            "message": f"Function registration endpoint available for '{function_name}'",
            "note": "This is a demo endpoint. Implement actual function registration logic."
        }

    except Exception as e:
        logger.error(f"Error in function registration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.get(
    "/functions",
    responses={
        200: {"model": Dict[str, Any]},
        500: {"model": ErrorResponse}
    }
)
async def list_functions(
    ai_service: AIService = Depends(get_ai_service)
):
    """List all registered functions."""
    try:
        return await ai_service.list_registered_functions()

    except AIServiceException as e:
        log_error(e, "list_functions")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Unexpected error listing functions: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.delete(
    "/functions/{function_name}",
    responses={
        200: {"model": Dict[str, Any]},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def unregister_function(
    function_name: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """Unregister a function."""
    try:
        return await ai_service.unregister_function(function_name)

    except AIServiceException as e:
        log_error(e, f"unregister_function_{function_name}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Unexpected error unregistering function {function_name}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


# Performance Optimization Endpoints
@router.post(
    "/generate-cached",
    response_model=TextGenerationResponse,
    responses={
        200: {"model": TextGenerationResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_text_cached(
    request: TextGenerationRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate text with caching optimization."""
    request_id = f"cached_gen_{id(request)}"

    try:
        # Validate request
        validated_prompt = RequestValidator.validate_prompt(request.prompt)
        request.prompt = validated_prompt

        validated_model = RequestValidator.validate_model(request.model or settings.default_model)
        request.model = validated_model

        validated_params = RequestValidator.validate_generation_params(
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            max_tokens=request.max_tokens
        )

        logger.info(f"[{request_id}] Processing cached text generation request")

        return await ai_service.generate_with_cache(request)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error in cached generation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/embeddings-cached",
    response_model=EmbeddingResponse,
    responses={
        200: {"model": EmbeddingResponse},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_embeddings_cached(
    request: EmbeddingRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Generate embeddings with caching optimization."""
    request_id = f"cached_emb_{id(request)}"

    try:
        # Validate request
        if not request.texts or len(request.texts) == 0:
            raise HTTPException(status_code=400, detail={"error": "No texts provided"})

        if len(request.texts) > 100:  # Limit batch size
            raise HTTPException(status_code=400, detail={"error": "Too many texts (max 100)"})

        validated_model = RequestValidator.validate_model(request.model or settings.default_embedding_model)
        request.model = validated_model

        logger.info(f"[{request_id}] Processing cached embedding request for {len(request.texts)} texts")

        return await ai_service.embeddings_with_cache(request)

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error in cached embeddings: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/batch/generate",
    responses={
        200: {"model": Dict[str, Any]},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def batch_generate_text(
    batch_request: Dict[str, Any],
    ai_service: AIService = Depends(get_ai_service)
):
    """Process multiple text generation requests in batch."""
    request_id = f"batch_gen_{id(batch_request)}"

    try:
        prompts = batch_request.get("prompts", [])
        if not prompts:
            raise HTTPException(status_code=400, detail={"error": "No prompts provided"})

        if len(prompts) > 50:  # Limit batch size
            raise HTTPException(status_code=400, detail={"error": "Too many prompts (max 50)"})

        model = batch_request.get("model")
        batch_config = batch_request.get("batch_config", {})

        logger.info(f"[{request_id}] Processing batch generation for {len(prompts)} prompts")

        result = await ai_service.batch_generate(
            prompts=prompts,
            model=model,
            batch_config=batch_config,
            **batch_request.get("generation_params", {})
        )

        return result

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error in batch generation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.post(
    "/batch/embeddings",
    responses={
        200: {"model": Dict[str, Any]},
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def batch_generate_embeddings(
    batch_request: Dict[str, Any],
    ai_service: AIService = Depends(get_ai_service)
):
    """Process multiple embedding requests in batch."""
    request_id = f"batch_emb_{id(batch_request)}"

    try:
        texts = batch_request.get("texts", [])
        if not texts:
            raise HTTPException(status_code=400, detail={"error": "No texts provided"})

        if len(texts) > 200:  # Limit batch size
            raise HTTPException(status_code=400, detail={"error": "Too many texts (max 200)"})

        model = batch_request.get("model")
        batch_config = batch_request.get("batch_config", {})

        logger.info(f"[{request_id}] Processing batch embeddings for {len(texts)} texts")

        result = await ai_service.batch_embeddings(
            texts=texts,
            model=model,
            batch_config=batch_config
        )

        return result

    except AIServiceException as e:
        log_error(e, request_id)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"[{request_id}] Unexpected error in batch embeddings: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )


@router.get(
    "/batch/{batch_id}/status",
    responses={
        200: {"model": Dict[str, Any]},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_batch_status(
    batch_id: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """Get status of a batch job."""
    try:
        result = await ai_service.get_batch_status(batch_id)

        if result.get("status") == "not_found":
            raise HTTPException(status_code=404, detail={"error": "Batch job not found"})

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting batch status: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "details": {"message": "An unexpected error occurred"}}
        )
