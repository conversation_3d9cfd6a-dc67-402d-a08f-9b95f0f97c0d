"""
Phase 5 API Routes: Performance Optimization & Enterprise Features.
Includes advanced caching, WebSocket, authentication, and monitoring endpoints.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from ai_service.core.smart_cache_manager import smart_cache_manager
from ai_service.core.advanced_websocket_manager import advanced_websocket_manager, MessageType
from ai_service.core.enhanced_auth_manager import enhanced_auth_manager, Permission, UserRole
from ai_service.core.enhanced_exceptions import EnhancedAIException, ValidationException
from ai_service.utils.error_handler import log_error

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Router
router = APIRouter(prefix="/api/v1/enterprise", tags=["Phase 5 - Enterprise Features"])


# Pydantic Models
class CacheStatsResponse(BaseModel):
    """Cache statistics response."""
    stats: Dict[str, Any] = Field(..., description="Cache statistics")
    tier_distribution: Dict[str, int] = Field(..., description="Cache tier distribution")
    performance: Dict[str, Any] = Field(..., description="Performance metrics")
    recent_performance: List[Dict[str, Any]] = Field(..., description="Recent performance history")


class WebSocketStatsResponse(BaseModel):
    """WebSocket statistics response."""
    connections: Dict[str, int] = Field(..., description="Connection statistics")
    rooms: Dict[str, int] = Field(..., description="Room statistics")
    messages: Dict[str, Any] = Field(..., description="Message statistics")
    performance: Dict[str, Any] = Field(..., description="Performance metrics")


class AuthStatsResponse(BaseModel):
    """Authentication statistics response."""
    users: Dict[str, int] = Field(..., description="User statistics")
    sessions: Dict[str, int] = Field(..., description="Session statistics")
    api_keys: Dict[str, int] = Field(..., description="API key statistics")
    security: Dict[str, Any] = Field(..., description="Security metrics")


class LoginRequest(BaseModel):
    """User login request."""
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")


class LoginResponse(BaseModel):
    """User login response."""
    token: str = Field(..., description="JWT authentication token")
    user_id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    roles: List[str] = Field(..., description="User roles")
    expires_at: float = Field(..., description="Token expiration timestamp")


class CreateAPIKeyRequest(BaseModel):
    """Create API key request."""
    name: str = Field(..., description="API key name")
    permissions: Optional[List[str]] = Field(None, description="Requested permissions")
    expires_in_days: Optional[int] = Field(None, description="Expiration in days")
    rate_limit_per_minute: Optional[int] = Field(None, description="Rate limit per minute")
    rate_limit_per_hour: Optional[int] = Field(None, description="Rate limit per hour")
    allowed_ips: Optional[List[str]] = Field(None, description="Allowed IP addresses")


class CreateAPIKeyResponse(BaseModel):
    """Create API key response."""
    api_key: str = Field(..., description="Generated API key")
    key_id: str = Field(..., description="API key ID")
    name: str = Field(..., description="API key name")
    permissions: List[str] = Field(..., description="Granted permissions")
    expires_at: Optional[float] = Field(None, description="Expiration timestamp")


# Dependency Functions
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated user."""
    token = credentials.credentials
    session = await enhanced_auth_manager.verify_session(token)
    
    if not session:
        raise HTTPException(status_code=401, detail="Invalid or expired token")
    
    user = await enhanced_auth_manager.get_user_by_id(session.user_id)
    if not user or not user.is_active:
        raise HTTPException(status_code=401, detail="User not found or inactive")
    
    return user, session


async def require_permission(permission: Permission):
    """Dependency to require specific permission."""
    async def check_permission(user_session = Depends(get_current_user)):
        user, session = user_session
        if not await enhanced_auth_manager.check_permission(session, permission):
            raise HTTPException(
                status_code=403, 
                detail=f"Permission required: {permission.value}"
            )
        return user, session
    return check_permission


# Authentication Endpoints
@router.post("/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest, http_request: Request):
    """Authenticate user and create session."""
    try:
        # Get client IP
        client_ip = http_request.client.host
        
        # Authenticate user
        session = await enhanced_auth_manager.authenticate_user(
            request.username, 
            request.password, 
            client_ip
        )
        
        if not session:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        user = await enhanced_auth_manager.get_user_by_id(session.user_id)
        
        return LoginResponse(
            token=session.token,
            user_id=session.user_id,
            username=user.username,
            roles=[role.value for role in user.roles],
            expires_at=session.expires_at
        )
        
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Authentication failed")


@router.post("/auth/logout")
async def logout(user_session = Depends(get_current_user)):
    """Logout user and revoke session."""
    try:
        user, session = user_session
        await enhanced_auth_manager.revoke_session(session.session_id)
        return {"message": "Logged out successfully"}
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(status_code=500, detail="Logout failed")


# API Key Management
@router.post("/api-keys", response_model=CreateAPIKeyResponse)
async def create_api_key(
    request: CreateAPIKeyRequest,
    user_session = Depends(require_permission(Permission.API_KEY_CREATE))
):
    """Create new API key."""
    try:
        user, session = user_session
        
        # Convert permission strings to Permission enums
        permissions = None
        if request.permissions:
            try:
                permissions = [Permission(p) for p in request.permissions]
            except ValueError as e:
                raise HTTPException(status_code=400, detail=f"Invalid permission: {e}")
        
        # Create API key
        api_key_value, api_key = await enhanced_auth_manager.create_api_key(
            user_id=user.user_id,
            name=request.name,
            permissions=permissions,
            expires_in_days=request.expires_in_days,
            rate_limit_per_minute=request.rate_limit_per_minute,
            rate_limit_per_hour=request.rate_limit_per_hour,
            allowed_ips=request.allowed_ips
        )
        
        return CreateAPIKeyResponse(
            api_key=api_key_value,
            key_id=api_key.key_id,
            name=api_key.name,
            permissions=[p.value for p in api_key.permissions],
            expires_at=api_key.expires_at
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"API key creation error: {e}")
        raise HTTPException(status_code=500, detail="API key creation failed")


@router.get("/api-keys")
async def list_api_keys(user_session = Depends(get_current_user)):
    """List user's API keys."""
    try:
        user, session = user_session
        api_keys = await enhanced_auth_manager.list_api_keys(user.user_id)
        return {"api_keys": api_keys}
        
    except Exception as e:
        logger.error(f"API key listing error: {e}")
        raise HTTPException(status_code=500, detail="Failed to list API keys")


@router.delete("/api-keys/{key_id}")
async def revoke_api_key(
    key_id: str,
    user_session = Depends(get_current_user)
):
    """Revoke API key."""
    try:
        user, session = user_session
        success = await enhanced_auth_manager.revoke_api_key(key_id, user.user_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="API key not found")
        
        return {"message": "API key revoked successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API key revocation error: {e}")
        raise HTTPException(status_code=500, detail="API key revocation failed")


# Cache Management
@router.get("/cache/stats", response_model=CacheStatsResponse)
async def get_cache_stats(
    user_session = Depends(require_permission(Permission.CACHE_READ))
):
    """Get cache statistics and analytics."""
    try:
        analytics = await smart_cache_manager.get_analytics()
        return CacheStatsResponse(**analytics)
        
    except Exception as e:
        logger.error(f"Cache stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cache statistics")


@router.post("/cache/clear")
async def clear_cache(
    tier: Optional[str] = None,
    user_session = Depends(require_permission(Permission.CACHE_ADMIN))
):
    """Clear cache entries."""
    try:
        from ai_service.core.smart_cache_manager import CacheTier
        
        cache_tier = None
        if tier:
            try:
                cache_tier = CacheTier(tier)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid cache tier: {tier}")
        
        await smart_cache_manager.clear_cache(cache_tier)
        
        return {
            "message": f"Cache cleared: {tier if tier else 'all tiers'}",
            "tier": tier
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cache clear error: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")


@router.post("/cache/evict-expired")
async def evict_expired_cache(
    user_session = Depends(require_permission(Permission.CACHE_ADMIN))
):
    """Manually evict expired cache entries."""
    try:
        count = await smart_cache_manager.evict_expired()
        return {
            "message": f"Evicted {count} expired entries",
            "evicted_count": count
        }
        
    except Exception as e:
        logger.error(f"Cache eviction error: {e}")
        raise HTTPException(status_code=500, detail="Failed to evict expired entries")


# WebSocket Management
@router.get("/websocket/stats", response_model=WebSocketStatsResponse)
async def get_websocket_stats(
    user_session = Depends(require_permission(Permission.SYSTEM_METRICS))
):
    """Get WebSocket statistics."""
    try:
        stats = advanced_websocket_manager.get_stats()
        return WebSocketStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"WebSocket stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get WebSocket statistics")


# Security & Monitoring
@router.get("/security/stats", response_model=AuthStatsResponse)
async def get_security_stats(
    user_session = Depends(require_permission(Permission.SYSTEM_ADMIN))
):
    """Get comprehensive security statistics."""
    try:
        stats = enhanced_auth_manager.get_security_stats()
        return AuthStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Security stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security statistics")


# WebSocket Endpoint
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: Optional[str] = None):
    """WebSocket endpoint for real-time communication."""
    connection_id = None
    
    try:
        # Authenticate WebSocket connection
        user_id = None
        if token:
            session = await enhanced_auth_manager.verify_session(token)
            if session:
                user_id = session.user_id
        
        # Connect to WebSocket manager
        connection_id = await advanced_websocket_manager.connect(websocket, user_id)
        
        logger.info(f"WebSocket connected: {connection_id}")
        
        # Handle messages
        while True:
            try:
                message = await websocket.receive_text()
                await advanced_websocket_manager.handle_message(connection_id, message)
                
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        
    finally:
        if connection_id:
            await advanced_websocket_manager.disconnect(connection_id)


# System Status
@router.get("/status")
async def get_system_status():
    """Get overall system status for Phase 5 features."""
    try:
        # Get status from all Phase 5 components
        cache_analytics = await smart_cache_manager.get_analytics()
        websocket_stats = advanced_websocket_manager.get_stats()
        security_stats = enhanced_auth_manager.get_security_stats()
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "components": {
                "smart_cache": {
                    "status": "active",
                    "hit_rate": cache_analytics["stats"]["hit_rate"],
                    "size_mb": cache_analytics["stats"]["total_size_mb"],
                    "entries": cache_analytics["stats"]["entry_count"]
                },
                "websocket": {
                    "status": "active",
                    "active_connections": websocket_stats["connections"]["active"],
                    "total_rooms": websocket_stats["rooms"]["total"],
                    "messages_per_second": websocket_stats["messages"]["per_second"]
                },
                "authentication": {
                    "status": "active",
                    "active_sessions": security_stats["sessions"]["active"],
                    "active_api_keys": security_stats["api_keys"]["active"],
                    "total_users": security_stats["users"]["total"]
                }
            }
        }
        
    except Exception as e:
        logger.error(f"System status error: {e}")
        return {
            "status": "error",
            "timestamp": time.time(),
            "error": str(e)
        }
