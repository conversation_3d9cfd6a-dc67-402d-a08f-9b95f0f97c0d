"""
Enterprise security API routes.
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from pydantic import BaseModel

from ai_service.core.auth_manager import auth_manager, UserRole, Permission
from ai_service.core.rate_limiter import rate_limiter, RateLimitRule, RateLimit, RateLimitType, ThrottleStrategy
from ai_service.core.audit_logger import audit_logger, AuditQuery, AuditEventType, AuditLevel, ComplianceStandard
from ai_service.core.tenant_manager import tenant_manager, TenantTier, TenantStatus
from ai_service.core.monitoring_system import monitoring_system, AlertRule, MetricThreshold, AlertSeverity, NotificationChannel
from ai_service.core.security import security_manager
from ai_service.models.schemas import ErrorResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/security", tags=["security"])


# Request Models
class CreateUserRequest(BaseModel):
    """Create user request."""
    username: str
    email: str
    password: str
    roles: Optional[List[str]] = None
    tenant_id: Optional[str] = None


class CreateAPIKeyRequest(BaseModel):
    """Create API key request."""
    name: str
    permissions: Optional[List[str]] = None
    expires_days: Optional[int] = None
    rate_limit: Optional[int] = None


class CreateTenantRequest(BaseModel):
    """Create tenant request."""
    name: str
    display_name: str
    tier: str = "free"
    owner_user_id: Optional[str] = None
    contact_email: Optional[str] = None
    expires_days: Optional[int] = None


class UpdateTenantRequest(BaseModel):
    """Update tenant request."""
    display_name: Optional[str] = None
    status: Optional[str] = None
    tier: Optional[str] = None
    contact_email: Optional[str] = None


class CreateRateLimitRuleRequest(BaseModel):
    """Create rate limit rule request."""
    name: str
    applies_to: str
    limits: List[Dict[str, Any]]
    priority: int = 0


class CreateAlertRuleRequest(BaseModel):
    """Create alert rule request."""
    name: str
    description: str
    thresholds: List[Dict[str, Any]]
    notification_channels: List[str]
    cooldown_seconds: int = 3600


# Authentication & Authorization Endpoints
@router.post("/users", summary="Create User")
async def create_user(request: CreateUserRequest):
    """Create a new user."""
    try:
        # Convert role strings to enums
        roles = set()
        if request.roles:
            for role_str in request.roles:
                try:
                    roles.add(UserRole(role_str))
                except ValueError:
                    raise HTTPException(status_code=400, detail=f"Invalid role: {role_str}")
        
        user = await auth_manager.create_user(
            username=request.username,
            email=request.email,
            password=request.password,
            roles=roles,
            tenant_id=request.tenant_id
        )
        
        return {
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "roles": [role.value for role in user.roles],
            "permissions": [perm.value for perm in user.permissions],
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail="Failed to create user")


@router.post("/users/{user_id}/api-keys", summary="Create API Key")
async def create_api_key(user_id: str, request: CreateAPIKeyRequest):
    """Create API key for user."""
    try:
        # Convert permission strings to enums
        permissions = set()
        if request.permissions:
            for perm_str in request.permissions:
                try:
                    permissions.add(Permission(perm_str))
                except ValueError:
                    raise HTTPException(status_code=400, detail=f"Invalid permission: {perm_str}")
        
        # Calculate expiration
        expires_at = None
        if request.expires_days:
            expires_at = datetime.utcnow() + timedelta(days=request.expires_days)
        
        api_key, api_key_entity = await auth_manager.create_api_key(
            user_id=user_id,
            name=request.name,
            permissions=permissions,
            expires_at=expires_at,
            rate_limit=request.rate_limit
        )
        
        return {
            "api_key": api_key,  # Only returned once
            "key_id": api_key_entity.key_id,
            "name": api_key_entity.name,
            "permissions": [perm.value for perm in api_key_entity.permissions],
            "expires_at": api_key_entity.expires_at.isoformat() if api_key_entity.expires_at else None,
            "created_at": api_key_entity.created_at.isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating API key: {e}")
        raise HTTPException(status_code=500, detail="Failed to create API key")


@router.post("/auth/login", summary="User Login")
async def login(username: str, password: str, request: Request):
    """Authenticate user and create session."""
    try:
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        session = await auth_manager.authenticate_user(
            username=username,
            password=password,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        if not session:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Create JWT token
        jwt_token = await auth_manager.create_jwt_token(session.user_id)
        
        return {
            "access_token": jwt_token,
            "token_type": "bearer",
            "session_id": session.session_id,
            "expires_at": session.expires_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during login: {e}")
        raise HTTPException(status_code=500, detail="Login failed")


@router.get("/auth/stats", summary="Authentication Statistics")
async def get_auth_stats():
    """Get authentication statistics."""
    try:
        return auth_manager.get_auth_stats()
    except Exception as e:
        logger.error(f"Error getting auth stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get authentication statistics")


# Rate Limiting Endpoints
@router.post("/rate-limits", summary="Create Rate Limit Rule")
async def create_rate_limit_rule(request: CreateRateLimitRuleRequest):
    """Create custom rate limiting rule."""
    try:
        # Convert limit dictionaries to RateLimit objects
        limits = []
        for limit_data in request.limits:
            limit_type = RateLimitType(limit_data["limit_type"])
            strategy = ThrottleStrategy(limit_data.get("strategy", "reject"))
            
            rate_limit = RateLimit(
                limit_type=limit_type,
                limit=limit_data["limit"],
                window_seconds=limit_data["window_seconds"],
                strategy=strategy,
                burst_limit=limit_data.get("burst_limit"),
                queue_size=limit_data.get("queue_size"),
                delay_seconds=limit_data.get("delay_seconds")
            )
            limits.append(rate_limit)
        
        # Create rule
        rule = RateLimitRule(
            name=request.name,
            limits=limits,
            applies_to=request.applies_to,
            priority=request.priority
        )
        
        rate_limiter.add_rule(rule)
        
        return {
            "rule_name": rule.name,
            "applies_to": rule.applies_to,
            "limits_count": len(rule.limits),
            "priority": rule.priority,
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating rate limit rule: {e}")
        raise HTTPException(status_code=500, detail="Failed to create rate limit rule")


@router.get("/rate-limits/stats", summary="Rate Limiting Statistics")
async def get_rate_limit_stats():
    """Get rate limiting statistics."""
    try:
        return rate_limiter.get_rate_limit_stats()
    except Exception as e:
        logger.error(f"Error getting rate limit stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get rate limiting statistics")


# Audit Logging Endpoints
@router.get("/audit/events", summary="Query Audit Events")
async def query_audit_events(
    start_time: Optional[str] = Query(None),
    end_time: Optional[str] = Query(None),
    event_types: Optional[str] = Query(None),
    user_ids: Optional[str] = Query(None),
    limit: int = Query(100, le=1000)
):
    """Query audit events."""
    try:
        # Parse parameters
        query = AuditQuery(limit=limit)
        
        if start_time:
            query.start_time = datetime.fromisoformat(start_time)
        if end_time:
            query.end_time = datetime.fromisoformat(end_time)
        if event_types:
            query.event_types = [AuditEventType(et) for et in event_types.split(",")]
        if user_ids:
            query.user_ids = user_ids.split(",")
        
        events = await audit_logger.query_events(query)
        
        return {
            "events": [event.to_dict() for event in events],
            "total_events": len(events),
            "query_parameters": {
                "start_time": start_time,
                "end_time": end_time,
                "event_types": event_types,
                "user_ids": user_ids,
                "limit": limit
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error querying audit events: {e}")
        raise HTTPException(status_code=500, detail="Failed to query audit events")


@router.get("/audit/compliance/{standard}", summary="Generate Compliance Report")
async def generate_compliance_report(
    standard: str,
    start_date: str,
    end_date: str
):
    """Generate compliance report."""
    try:
        compliance_standard = ComplianceStandard(standard)
        start_time = datetime.fromisoformat(start_date)
        end_time = datetime.fromisoformat(end_date)
        
        report = await audit_logger.generate_compliance_report(
            compliance_standard=compliance_standard,
            start_time=start_time,
            end_time=end_time
        )
        
        return report
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating compliance report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate compliance report")


@router.get("/audit/stats", summary="Audit Statistics")
async def get_audit_stats():
    """Get audit logging statistics."""
    try:
        return audit_logger.get_audit_stats()
    except Exception as e:
        logger.error(f"Error getting audit stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get audit statistics")


# Multi-tenant Management Endpoints
@router.post("/tenants", summary="Create Tenant")
async def create_tenant(request: CreateTenantRequest):
    """Create a new tenant."""
    try:
        tier = TenantTier(request.tier)
        
        expires_at = None
        if request.expires_days:
            expires_at = datetime.utcnow() + timedelta(days=request.expires_days)
        
        tenant = await tenant_manager.create_tenant(
            name=request.name,
            display_name=request.display_name,
            tier=tier,
            owner_user_id=request.owner_user_id,
            contact_email=request.contact_email,
            expires_at=expires_at
        )
        
        return {
            "tenant_id": tenant.id,
            "name": tenant.name,
            "display_name": tenant.display_name,
            "tier": tenant.tier.value,
            "status": tenant.status.value,
            "created_at": tenant.created_at.isoformat(),
            "quotas": {
                "requests_per_minute": tenant.quotas.requests_per_minute,
                "requests_per_hour": tenant.quotas.requests_per_hour,
                "concurrent_requests": tenant.quotas.concurrent_requests,
                "storage_mb": tenant.quotas.storage_mb,
                "users_limit": tenant.quotas.users_limit
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating tenant: {e}")
        raise HTTPException(status_code=500, detail="Failed to create tenant")


@router.put("/tenants/{tenant_id}", summary="Update Tenant")
async def update_tenant(tenant_id: str, request: UpdateTenantRequest):
    """Update tenant configuration."""
    try:
        updates = {}
        
        if request.display_name:
            updates["display_name"] = request.display_name
        if request.status:
            updates["status"] = TenantStatus(request.status)
        if request.tier:
            updates["tier"] = TenantTier(request.tier)
        if request.contact_email:
            updates["contact_email"] = request.contact_email
        
        success = await tenant_manager.update_tenant(tenant_id, updates)
        
        if not success:
            raise HTTPException(status_code=404, detail="Tenant not found")
        
        return {"tenant_id": tenant_id, "status": "updated"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating tenant: {e}")
        raise HTTPException(status_code=500, detail="Failed to update tenant")


@router.get("/tenants/{tenant_id}/usage", summary="Get Tenant Usage")
async def get_tenant_usage(tenant_id: str):
    """Get tenant usage statistics."""
    try:
        usage = await tenant_manager.get_tenant_usage(tenant_id)
        
        if not usage:
            raise HTTPException(status_code=404, detail="Tenant not found")
        
        return {
            "tenant_id": tenant_id,
            "usage": {
                "requests_today": usage.requests_today,
                "requests_this_hour": usage.requests_this_hour,
                "tokens_today": usage.tokens_today,
                "current_concurrent_requests": usage.current_concurrent_requests,
                "storage_used_mb": usage.storage_used_mb,
                "active_users": usage.active_users,
                "last_updated": usage.last_updated.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tenant usage: {e}")
        raise HTTPException(status_code=500, detail="Failed to get tenant usage")


@router.get("/tenants/stats", summary="Tenant Statistics")
async def get_tenant_stats():
    """Get tenant management statistics."""
    try:
        return tenant_manager.get_tenant_stats()
    except Exception as e:
        logger.error(f"Error getting tenant stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get tenant statistics")


# Monitoring & Alerting Endpoints
@router.post("/monitoring/alerts", summary="Create Alert Rule")
async def create_alert_rule(request: CreateAlertRuleRequest):
    """Create monitoring alert rule."""
    try:
        # Convert threshold dictionaries to MetricThreshold objects
        thresholds = []
        for threshold_data in request.thresholds:
            severity = AlertSeverity(threshold_data["severity"])
            
            threshold = MetricThreshold(
                metric_name=threshold_data["metric_name"],
                operator=threshold_data["operator"],
                value=threshold_data["value"],
                duration_seconds=threshold_data.get("duration_seconds", 300),
                severity=severity
            )
            thresholds.append(threshold)
        
        # Convert notification channel strings to enums
        channels = []
        for channel_str in request.notification_channels:
            channels.append(NotificationChannel(channel_str))
        
        # Create alert rule
        rule = AlertRule(
            id=f"custom_{request.name.lower().replace(' ', '_')}",
            name=request.name,
            description=request.description,
            thresholds=thresholds,
            notification_channels=channels,
            cooldown_seconds=request.cooldown_seconds
        )
        
        await monitoring_system.add_alert_rule(rule)
        
        return {
            "rule_id": rule.id,
            "name": rule.name,
            "thresholds_count": len(rule.thresholds),
            "notification_channels": [ch.value for ch in rule.notification_channels],
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating alert rule: {e}")
        raise HTTPException(status_code=500, detail="Failed to create alert rule")


@router.get("/monitoring/alerts/active", summary="Get Active Alerts")
async def get_active_alerts():
    """Get active monitoring alerts."""
    try:
        alerts = monitoring_system.get_active_alerts()
        
        return {
            "active_alerts": [
                {
                    "alert_id": alert.id,
                    "rule_id": alert.rule_id,
                    "name": alert.name,
                    "description": alert.description,
                    "severity": alert.severity.value,
                    "status": alert.status.value,
                    "created_at": alert.created_at.isoformat(),
                    "metric_values": alert.metric_values,
                    "tags": alert.tags
                }
                for alert in alerts
            ],
            "total_alerts": len(alerts)
        }
        
    except Exception as e:
        logger.error(f"Error getting active alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get active alerts")


@router.post("/monitoring/alerts/{alert_id}/acknowledge", summary="Acknowledge Alert")
async def acknowledge_alert(alert_id: str, acknowledged_by: str):
    """Acknowledge an alert."""
    try:
        success = await monitoring_system.acknowledge_alert(alert_id, acknowledged_by)
        
        if not success:
            raise HTTPException(status_code=404, detail="Alert not found or already acknowledged")
        
        return {"alert_id": alert_id, "status": "acknowledged", "acknowledged_by": acknowledged_by}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error acknowledging alert: {e}")
        raise HTTPException(status_code=500, detail="Failed to acknowledge alert")


@router.get("/monitoring/stats", summary="Monitoring Statistics")
async def get_monitoring_stats():
    """Get monitoring system statistics."""
    try:
        return monitoring_system.get_monitoring_stats()
    except Exception as e:
        logger.error(f"Error getting monitoring stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring statistics")


# Security Summary Endpoint
@router.get("/summary", summary="Security Summary")
async def get_security_summary():
    """Get comprehensive security summary."""
    try:
        return {
            "authentication": auth_manager.get_auth_stats(),
            "rate_limiting": rate_limiter.get_rate_limit_stats(),
            "audit_logging": audit_logger.get_audit_stats(),
            "tenant_management": tenant_manager.get_tenant_stats(),
            "monitoring": monitoring_system.get_monitoring_stats(),
            "basic_security": security_manager.get_security_summary(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting security summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security summary")
