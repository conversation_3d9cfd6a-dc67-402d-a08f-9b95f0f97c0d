"""
Health check API endpoints for production monitoring.
"""
import logging
from typing import Dict, Any, List
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel

from ai_service.core.health_checker import health_checker, HealthStatus, ServiceHealth, HealthCheckResult
from ai_service.core.metrics import metrics_collector
from ai_service.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/health", tags=["Health"])


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    version: str
    uptime_seconds: float
    checks: List[Dict[str, Any]]
    summary: Dict[str, Any]


class ReadinessResponse(BaseModel):
    """Readiness check response model."""
    ready: bool
    timestamp: datetime
    message: str
    details: Dict[str, Any]


class LivenessResponse(BaseModel):
    """Liveness check response model."""
    alive: bool
    timestamp: datetime
    uptime_seconds: float


@router.get(
    "/",
    response_model=HealthResponse,
    summary="Comprehensive Health Check",
    description="Get comprehensive health status of the AI service including all subsystems"
)
async def health_check():
    """Comprehensive health check endpoint."""
    try:
        service_health = await health_checker.get_service_health()

        # Convert to response format
        checks_data = []
        for check in service_health.checks:
            checks_data.append({
                "name": check.name,
                "status": check.status.value,
                "message": check.message,
                "duration_ms": check.duration_ms,
                "timestamp": check.timestamp,
                "details": check.details or {}
            })

        response = HealthResponse(
            status=service_health.status.value,
            timestamp=service_health.timestamp,
            version=service_health.version,
            uptime_seconds=service_health.uptime_seconds,
            checks=checks_data,
            summary=service_health.summary
        )

        # Set appropriate HTTP status code
        if service_health.status == HealthStatus.UNHEALTHY:
            raise HTTPException(status_code=503, detail=response.dict())
        elif service_health.status == HealthStatus.DEGRADED:
            raise HTTPException(status_code=200, detail=response.dict())

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Health check failed: {str(e)}",
                "timestamp": datetime.utcnow()
            }
        )


@router.get("/ping")
async def ping():
    """Simple ping endpoint."""
    return {"ping": "pong", "timestamp": datetime.utcnow()}


@router.get(
    "/ready",
    response_model=ReadinessResponse,
    summary="Readiness Check",
    description="Check if the service is ready to accept requests"
)
async def readiness_check():
    """Kubernetes readiness probe endpoint."""
    try:
        # Run critical checks for readiness
        critical_checks = ["gemini_api", "service_dependencies"]

        ready = True
        failed_checks = []
        check_details = {}

        for check_name in critical_checks:
            result = await health_checker.run_check(check_name)
            check_details[check_name] = {
                "status": result.status.value,
                "message": result.message,
                "duration_ms": result.duration_ms
            }

            if result.status == HealthStatus.UNHEALTHY:
                ready = False
                failed_checks.append(check_name)

        response = ReadinessResponse(
            ready=ready,
            timestamp=datetime.utcnow(),
            message="Service is ready" if ready else f"Service not ready: {', '.join(failed_checks)}",
            details=check_details
        )

        if not ready:
            raise HTTPException(status_code=503, detail=response.dict())

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "ready": False,
                "message": f"Readiness check failed: {str(e)}",
                "timestamp": datetime.utcnow(),
                "details": {"error": str(e)}
            }
        )


@router.get(
    "/live",
    response_model=LivenessResponse,
    summary="Liveness Check",
    description="Check if the service is alive and responding"
)
async def liveness_check():
    """Kubernetes liveness probe endpoint."""
    try:
        # Simple liveness check - just verify the service is responding
        uptime_seconds = health_checker.start_time
        current_time = datetime.utcnow().timestamp()
        actual_uptime = current_time - uptime_seconds

        return LivenessResponse(
            alive=True,
            timestamp=datetime.utcnow(),
            uptime_seconds=actual_uptime
        )

    except Exception as e:
        logger.error(f"Liveness check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "alive": False,
                "message": f"Liveness check failed: {str(e)}",
                "timestamp": datetime.utcnow()
            }
        )


@router.get(
    "/status",
    summary="Service Status Summary",
    description="Get a quick service status summary"
)
async def service_status():
    """Get service status summary."""
    try:
        service_health = await health_checker.get_service_health()

        return {
            "status": service_health.status.value,
            "version": service_health.version,
            "uptime_hours": round(service_health.uptime_seconds / 3600, 2),
            "healthy_checks": service_health.summary["healthy_checks"],
            "total_checks": service_health.summary["total_checks"],
            "average_response_time_ms": round(service_health.summary["average_response_time_ms"], 2),
            "timestamp": service_health.timestamp
        }

    except Exception as e:
        logger.error(f"Failed to get service status: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to get service status",
                "message": str(e),
                "timestamp": datetime.utcnow()
            }
        )


@router.get(
    "/metrics",
    summary="Prometheus Metrics",
    description="Get Prometheus-compatible metrics"
)
async def get_metrics():
    """Get Prometheus-compatible metrics."""
    try:
        prometheus_metrics = metrics_collector.get_prometheus_metrics()

        from fastapi import Response
        return Response(
            content=prometheus_metrics,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )

    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to get metrics",
                "message": str(e),
                "timestamp": datetime.utcnow()
            }
        )


@router.get(
    "/metrics/summary",
    summary="Metrics Summary",
    description="Get comprehensive metrics summary"
)
async def get_metrics_summary():
    """Get comprehensive metrics summary."""
    try:
        return metrics_collector.get_metrics_summary()

    except Exception as e:
        logger.error(f"Failed to get metrics summary: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to get metrics summary",
                "message": str(e),
                "timestamp": datetime.utcnow()
            }
        )


# Performance & Cache Management Endpoints
@router.get(
    "/performance",
    summary="Performance Statistics",
    description="Get comprehensive performance statistics"
)
async def get_performance_stats():
    """Get performance statistics."""
    try:
        from ai_service.services.ai_service import AIService
        ai_service = AIService()

        return await ai_service.get_performance_stats()

    except Exception as e:
        logger.error(f"Failed to get performance stats: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to get performance statistics",
                "message": str(e),
                "timestamp": datetime.utcnow()
            }
        )


@router.get(
    "/cache/stats",
    summary="Cache Statistics",
    description="Get cache performance statistics"
)
async def get_cache_stats():
    """Get cache statistics."""
    try:
        from ai_service.services.ai_service import AIService
        ai_service = AIService()

        return await ai_service.get_cache_stats()

    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to get cache statistics",
                "message": str(e),
                "timestamp": datetime.utcnow()
            }
        )


@router.post(
    "/cache/clear",
    summary="Clear Cache",
    description="Clear cache entries"
)
async def clear_cache(cache_type: Optional[str] = None):
    """Clear cache entries."""
    try:
        from ai_service.services.ai_service import AIService
        ai_service = AIService()

        return await ai_service.clear_cache(cache_type)

    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to clear cache",
                "message": str(e),
                "timestamp": datetime.utcnow()
            }
        )
