"""
Streaming API routes for WebSocket and SSE endpoints.
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Request, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from ai_service.core.websocket_manager import websocket_manager, MessageType
from ai_service.core.sse_manager import sse_manager, SSEEventType
from ai_service.core.streaming_manager import streaming_manager, StreamType, collaboration_manager
from ai_service.services.ai_service import AIService, get_ai_service
from ai_service.models.schemas import ErrorResponse
from ai_service.utils.request_validator import RequestValidator
from ai_service.config.settings import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/streaming", tags=["streaming"])


# Request Models
class StreamRequest(BaseModel):
    """Stream creation request."""
    stream_type: str
    client_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class CollaborationSessionRequest(BaseModel):
    """Collaboration session request."""
    session_name: str
    creator_client_id: str
    session_config: Optional[Dict[str, Any]] = None


class JoinSessionRequest(BaseModel):
    """Join collaboration session request."""
    session_id: str
    client_id: str


class UpdateContextRequest(BaseModel):
    """Update shared context request."""
    session_id: str
    client_id: str
    updates: Dict[str, Any]


# WebSocket Endpoints
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, client_id: Optional[str] = Query(None)):
    """Main WebSocket endpoint for real-time communication."""
    try:
        # Connect client
        actual_client_id = await websocket_manager.connect_client(websocket, client_id)
        
        logger.info(f"WebSocket client {actual_client_id} connected")
        
        try:
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                
                # Handle message
                await websocket_manager.handle_client_message(actual_client_id, data)
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket client {actual_client_id} disconnected")
        except Exception as e:
            logger.error(f"WebSocket error for client {actual_client_id}: {e}")
        finally:
            # Disconnect client
            await websocket_manager.disconnect_client(actual_client_id)
            
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        await websocket.close()


@router.websocket("/ws/ai/{stream_type}")
async def websocket_ai_stream(
    websocket: WebSocket,
    stream_type: str,
    client_id: Optional[str] = Query(None)
):
    """WebSocket endpoint for AI streaming operations."""
    try:
        # Validate stream type
        try:
            stream_type_enum = StreamType(stream_type)
        except ValueError:
            await websocket.close(code=1003, reason="Invalid stream type")
            return
        
        # Connect client
        actual_client_id = await websocket_manager.connect_client(websocket, client_id)
        
        # Start AI stream for client
        stream_id = await websocket_manager.start_stream_for_client(
            actual_client_id,
            stream_type_enum
        )
        
        logger.info(f"Started AI stream {stream_id} for WebSocket client {actual_client_id}")
        
        try:
            while True:
                # Receive AI requests from client
                data = await websocket.receive_text()
                
                try:
                    request_data = json.loads(data)
                    
                    # Handle different AI operations based on stream type
                    if stream_type_enum == StreamType.TEXT_GENERATION:
                        await _handle_websocket_text_generation(
                            actual_client_id, stream_id, request_data
                        )
                    elif stream_type_enum == StreamType.MULTIMODAL:
                        await _handle_websocket_multimodal(
                            actual_client_id, stream_id, request_data
                        )
                    elif stream_type_enum == StreamType.FUNCTION_CALLING:
                        await _handle_websocket_function_calling(
                            actual_client_id, stream_id, request_data
                        )
                    
                except json.JSONDecodeError:
                    await websocket_manager.send_message(actual_client_id, {
                        "type": MessageType.ERROR.value,
                        "data": {"error": "Invalid JSON format"}
                    })
                
        except WebSocketDisconnect:
            logger.info(f"AI WebSocket client {actual_client_id} disconnected")
        except Exception as e:
            logger.error(f"AI WebSocket error for client {actual_client_id}: {e}")
        finally:
            await websocket_manager.disconnect_client(actual_client_id)
            
    except Exception as e:
        logger.error(f"AI WebSocket connection error: {e}")
        await websocket.close()


# Server-Sent Events Endpoints
@router.get("/sse")
async def sse_endpoint(
    request: Request,
    client_id: Optional[str] = Query(None),
    topics: Optional[str] = Query(None)
):
    """Server-Sent Events endpoint for web clients."""
    try:
        # Parse topics
        topic_list = topics.split(",") if topics else []
        
        # Create SSE stream
        return await sse_manager.create_sse_stream(
            request=request,
            client_id=client_id,
            topics=topic_list
        )
        
    except Exception as e:
        logger.error(f"SSE endpoint error: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to create SSE stream"})


@router.get("/sse/ai/{stream_type}")
async def sse_ai_stream(
    request: Request,
    stream_type: str,
    client_id: Optional[str] = Query(None)
):
    """SSE endpoint for AI streaming operations."""
    try:
        # Validate stream type
        try:
            stream_type_enum = StreamType(stream_type)
        except ValueError:
            raise HTTPException(status_code=400, detail={"error": "Invalid stream type"})
        
        # Create SSE stream
        response = await sse_manager.create_sse_stream(
            request=request,
            client_id=client_id,
            topics=[f"ai_{stream_type}"]
        )
        
        # Start AI stream for client
        if client_id:
            await sse_manager.start_stream_for_client(
                client_id,
                stream_type_enum
            )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"SSE AI stream error: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to create AI SSE stream"})


# Stream Management Endpoints
@router.post("/create")
async def create_stream(
    request: StreamRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """Create a new stream."""
    try:
        # Validate stream type
        try:
            stream_type_enum = StreamType(request.stream_type)
        except ValueError:
            raise HTTPException(status_code=400, detail={"error": "Invalid stream type"})
        
        # Create stream
        stream_id = await streaming_manager.create_stream(
            stream_type=stream_type_enum,
            client_id=request.client_id,
            metadata=request.metadata
        )
        
        await streaming_manager.start_stream(stream_id)
        
        return {
            "stream_id": stream_id,
            "stream_type": request.stream_type,
            "status": "created",
            "created_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating stream: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to create stream"})


@router.get("/status/{stream_id}")
async def get_stream_status(stream_id: str):
    """Get stream status."""
    try:
        session = streaming_manager.get_stream_session(stream_id)
        
        if not session:
            raise HTTPException(status_code=404, detail={"error": "Stream not found"})
        
        return {
            "stream_id": session.id,
            "stream_type": session.stream_type.value,
            "status": session.status.value,
            "created_at": session.created_at.isoformat(),
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "client_id": session.client_id,
            "chunks_sent": session.chunks_sent,
            "bytes_sent": session.bytes_sent,
            "metadata": session.metadata,
            "error_message": session.error_message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stream status: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to get stream status"})


@router.post("/cancel/{stream_id}")
async def cancel_stream(stream_id: str):
    """Cancel a stream."""
    try:
        success = await streaming_manager.cancel_stream(stream_id)
        
        if not success:
            raise HTTPException(status_code=404, detail={"error": "Stream not found"})
        
        return {
            "stream_id": stream_id,
            "status": "cancelled",
            "cancelled_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling stream: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to cancel stream"})


# Collaboration Endpoints
@router.post("/collaboration/create")
async def create_collaboration_session(request: CollaborationSessionRequest):
    """Create collaboration session."""
    try:
        session_id = await collaboration_manager.create_collaboration_session(
            session_name=request.session_name,
            creator_client_id=request.creator_client_id,
            session_config=request.session_config
        )
        
        return {
            "session_id": session_id,
            "session_name": request.session_name,
            "creator": request.creator_client_id,
            "status": "created",
            "created_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error creating collaboration session: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to create collaboration session"})


@router.post("/collaboration/join")
async def join_collaboration_session(request: JoinSessionRequest):
    """Join collaboration session."""
    try:
        success = await collaboration_manager.join_collaboration_session(
            session_id=request.session_id,
            client_id=request.client_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail={"error": "Session not found"})
        
        return {
            "session_id": request.session_id,
            "client_id": request.client_id,
            "status": "joined",
            "joined_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error joining collaboration session: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to join collaboration session"})


@router.post("/collaboration/leave/{client_id}")
async def leave_collaboration_session(client_id: str):
    """Leave collaboration session."""
    try:
        success = await collaboration_manager.leave_collaboration_session(client_id)
        
        if not success:
            raise HTTPException(status_code=404, detail={"error": "Client not in any session"})
        
        return {
            "client_id": client_id,
            "status": "left",
            "left_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error leaving collaboration session: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to leave collaboration session"})


@router.post("/collaboration/update-context")
async def update_shared_context(request: UpdateContextRequest):
    """Update shared context."""
    try:
        success = await collaboration_manager.update_shared_context(
            session_id=request.session_id,
            client_id=request.client_id,
            updates=request.updates
        )
        
        if not success:
            raise HTTPException(status_code=404, detail={"error": "Session not found"})
        
        return {
            "session_id": request.session_id,
            "client_id": request.client_id,
            "status": "updated",
            "updated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating shared context: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to update shared context"})


@router.get("/collaboration/context/{session_id}")
async def get_shared_context(session_id: str):
    """Get shared context."""
    try:
        context = await collaboration_manager.get_shared_context(session_id)
        
        if not context:
            raise HTTPException(status_code=404, detail={"error": "Session not found"})
        
        return context
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting shared context: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to get shared context"})


# Statistics Endpoints
@router.get("/stats")
async def get_streaming_stats():
    """Get streaming statistics."""
    try:
        streaming_stats = streaming_manager.get_streaming_stats()
        websocket_stats = websocket_manager.get_websocket_stats()
        sse_stats = sse_manager.get_sse_stats()
        collaboration_stats = collaboration_manager.get_collaboration_stats()
        
        return {
            "streaming": streaming_stats,
            "websocket": websocket_stats,
            "sse": sse_stats,
            "collaboration": collaboration_stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting streaming stats: {e}")
        raise HTTPException(status_code=500, detail={"error": "Failed to get streaming statistics"})


# Helper Functions
async def _handle_websocket_text_generation(client_id: str, stream_id: str, request_data: Dict[str, Any]):
    """Handle WebSocket text generation request."""
    # Implementation would integrate with AI service
    pass


async def _handle_websocket_multimodal(client_id: str, stream_id: str, request_data: Dict[str, Any]):
    """Handle WebSocket multimodal request."""
    # Implementation would integrate with AI service
    pass


async def _handle_websocket_function_calling(client_id: str, stream_id: str, request_data: Dict[str, Any]):
    """Handle WebSocket function calling request."""
    # Implementation would integrate with AI service
    pass
