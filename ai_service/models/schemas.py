"""
Pydantic models for request and response schemas.
"""
from enum import Enum
from typing import List, Optional, Dict, Any, Union, Callable, Literal, Type
from pydantic import BaseModel, Field, HttpUrl
import json
from datetime import datetime, timedelta


class AIRequest(BaseModel):
    """Base request model for AI operations."""
    model: Optional[str] = Field(None, description="AI model to use")


class GenerationConfig(BaseModel):
    """Common configuration for generation requests."""
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(0.7, description="Temperature for generation (0.0 to 1.0)")
    top_p: Optional[float] = Field(0.95, description="Top p for nucleus sampling (0.0 to 1.0)")
    top_k: Optional[int] = Field(40, description="Top k for sampling")
    candidate_count: Optional[int] = Field(1, description="Number of candidate responses to generate")
    stop_sequences: Optional[List[str]] = Field(None, description="Sequences that stop generation")
    presence_penalty: Optional[float] = Field(0.0, description="Presence penalty (-2.0 to 2.0)")
    frequency_penalty: Optional[float] = Field(0.0, description="Frequency penalty (-2.0 to 2.0)")


class HarmCategory(str, Enum):
    """Harm categories for safety settings."""
    HARM_CATEGORY_HARASSMENT = "HARM_CATEGORY_HARASSMENT"
    HARM_CATEGORY_HATE_SPEECH = "HARM_CATEGORY_HATE_SPEECH"
    HARM_CATEGORY_SEXUALLY_EXPLICIT = "HARM_CATEGORY_SEXUALLY_EXPLICIT"
    HARM_CATEGORY_DANGEROUS_CONTENT = "HARM_CATEGORY_DANGEROUS_CONTENT"


class HarmBlockThreshold(str, Enum):
    """Harm block thresholds for safety settings."""
    BLOCK_NONE = "BLOCK_NONE"
    BLOCK_ONLY_HIGH = "BLOCK_ONLY_HIGH"
    BLOCK_MEDIUM_AND_ABOVE = "BLOCK_MEDIUM_AND_ABOVE"
    BLOCK_LOW_AND_ABOVE = "BLOCK_LOW_AND_ABOVE"


class SafetySetting(BaseModel):
    """Safety setting for content generation."""
    category: HarmCategory
    threshold: HarmBlockThreshold


class ContentPart(BaseModel):
    """Base class for content parts."""
    pass


class TextPart(ContentPart):
    """Text part for content."""
    text: str = Field(..., description="Text content")


class ImagePart(ContentPart):
    """Image part for content."""
    type: Literal["url", "base64", "file_uri"] = Field(..., description="Type of image input")
    data: str = Field(..., description="Image data (URL, base64 string, or file URI)")
    mime_type: str = Field("image/jpeg", description="MIME type of the image")


class FilePart(ContentPart):
    """File part for content."""
    file_uri: str = Field(..., description="URI of the file")
    mime_type: str = Field(..., description="MIME type of the file")


class FunctionParameter(BaseModel):
    """Parameter for function declaration."""
    name: str = Field(..., description="Parameter name")
    type: str = Field(..., description="Parameter type")
    description: Optional[str] = Field(None, description="Parameter description")
    required: Optional[bool] = Field(False, description="Whether the parameter is required")


class FunctionDeclaration(BaseModel):
    """Function declaration for function calling."""
    name: str = Field(..., description="Function name")
    description: str = Field(..., description="Function description")
    parameters: List[FunctionParameter] = Field([], description="Function parameters")


class FunctionCall(BaseModel):
    """Function call information."""
    name: str = Field(..., description="Function name")
    arguments: Dict[str, Any] = Field(..., description="Function arguments")


class FunctionResponse(BaseModel):
    """Function response information."""
    name: str = Field(..., description="Function name")
    response: Dict[str, Any] = Field(..., description="Function response")


# Enhanced Function Calling Models
class FunctionExecutionConfig(BaseModel):
    """Configuration for function execution."""
    timeout: Optional[int] = Field(30, description="Function execution timeout in seconds")
    max_retries: Optional[int] = Field(3, description="Maximum number of retries on failure")
    retry_delay: Optional[float] = Field(1.0, description="Delay between retries in seconds")
    parallel_execution: Optional[bool] = Field(False, description="Enable parallel execution")
    max_parallel: Optional[int] = Field(4, description="Maximum parallel executions")


class FunctionChainStep(BaseModel):
    """A step in a function chain."""
    function_name: str = Field(..., description="Name of the function to call")
    arguments: Dict[str, Any] = Field(..., description="Arguments for the function")
    depends_on: Optional[List[str]] = Field(None, description="Names of previous steps this depends on")
    condition: Optional[str] = Field(None, description="Condition to execute this step")
    retry_config: Optional[FunctionExecutionConfig] = Field(None, description="Step-specific retry config")


class FunctionChain(BaseModel):
    """A chain of function calls."""
    name: str = Field(..., description="Name of the function chain")
    description: Optional[str] = Field(None, description="Description of the chain")
    steps: List[FunctionChainStep] = Field(..., description="Steps in the chain")
    execution_config: Optional[FunctionExecutionConfig] = Field(None, description="Chain execution config")


class EnhancedFunctionDeclaration(FunctionDeclaration):
    """Enhanced function declaration with additional metadata."""
    category: Optional[str] = Field(None, description="Function category")
    tags: Optional[List[str]] = Field(None, description="Function tags")
    version: Optional[str] = Field("1.0", description="Function version")
    execution_config: Optional[FunctionExecutionConfig] = Field(None, description="Function execution config")
    examples: Optional[List[Dict[str, Any]]] = Field(None, description="Usage examples")


class FunctionExecutionResult(BaseModel):
    """Result of function execution."""
    function_name: str = Field(..., description="Name of the executed function")
    success: bool = Field(..., description="Whether execution was successful")
    result: Optional[Any] = Field(None, description="Function result")
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: float = Field(..., description="Execution time in seconds")
    retry_count: int = Field(0, description="Number of retries performed")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class FunctionChainResult(BaseModel):
    """Result of function chain execution."""
    chain_name: str = Field(..., description="Name of the executed chain")
    success: bool = Field(..., description="Whether chain execution was successful")
    steps_executed: int = Field(..., description="Number of steps executed")
    total_steps: int = Field(..., description="Total number of steps in chain")
    execution_time: float = Field(..., description="Total execution time in seconds")
    step_results: List[FunctionExecutionResult] = Field(..., description="Results of individual steps")
    final_result: Optional[Any] = Field(None, description="Final aggregated result")
    error: Optional[str] = Field(None, description="Error message if chain failed")


class EnhancedFunctionCallingRequest(AIRequest, GenerationConfig):
    """Enhanced request model for function calling."""
    prompt: str = Field(..., description="Text prompt")
    functions: List[EnhancedFunctionDeclaration] = Field(..., description="Available functions")
    function_chains: Optional[List[FunctionChain]] = Field(None, description="Predefined function chains")
    execution_config: Optional[FunctionExecutionConfig] = Field(None, description="Global execution config")
    system_instruction: Optional[str] = Field(None, description="System instruction for the model")
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")
    # JSON Mode support
    json_mode: Optional[bool] = Field(False, description="Enable JSON mode output")
    json_schema: Optional[Union[JSONSchema, Dict[str, Any]]] = Field(None, description="JSON schema for structured output")


class EnhancedFunctionCallingResponse(BaseModel):
    """Enhanced response model for function calling."""
    text: str = Field(..., description="Generated text response")
    model: str = Field(..., description="Model used for generation")
    function_calls: Optional[List[FunctionCall]] = Field(None, description="Function calls made")
    function_results: Optional[List[FunctionExecutionResult]] = Field(None, description="Function execution results")
    chain_results: Optional[List[FunctionChainResult]] = Field(None, description="Chain execution results")
    usage: Optional[UsageInfo] = Field(None, description="Token usage information")
    finish_reason: Optional[str] = Field(None, description="Reason for completion")
    execution_summary: Optional[Dict[str, Any]] = Field(None, description="Execution summary")
    json_mode: Optional[bool] = Field(False, description="Whether JSON mode was used")
    json_data: Optional[Dict[str, Any]] = Field(None, description="Parsed JSON data if JSON mode enabled")


class JSONSchema(BaseModel):
    """JSON Schema definition for structured output."""
    type: str = Field(..., description="Schema type (object, array, string, etc.)")
    properties: Optional[Dict[str, Any]] = Field(default=None, description="Object properties")
    required: Optional[List[str]] = Field(default=None, description="Required properties")
    items: Optional[Dict[str, Any]] = Field(default=None, description="Array item schema")
    enum: Optional[List[str]] = Field(default=None, description="Enum values")
    description: Optional[str] = Field(default=None, description="Schema description")
    property_ordering: Optional[List[str]] = Field(default=None, description="Property ordering for consistent output")
    format: Optional[str] = Field(default=None, description="String format (date-time, email, etc.)")
    minimum: Optional[Union[int, float]] = Field(default=None, description="Minimum value for numbers")
    maximum: Optional[Union[int, float]] = Field(default=None, description="Maximum value for numbers")
    min_items: Optional[int] = Field(default=None, description="Minimum array length")
    max_items: Optional[int] = Field(default=None, description="Maximum array length")
    nullable: Optional[bool] = Field(default=None, description="Whether the value can be null")


class TextGenerationRequest(AIRequest, GenerationConfig):
    """Request model for text generation."""
    prompt: str = Field(..., description="Text prompt for generation")
    system_instruction: Optional[str] = Field(None, description="System instruction for the model")
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    # JSON Mode support
    json_mode: Optional[bool] = Field(False, description="Enable JSON mode output")
    json_schema: Optional[Union[JSONSchema, Dict[str, Any]]] = Field(None, description="JSON schema for structured output")


class MultimodalGenerationRequest(AIRequest, GenerationConfig):
    """Request model for multimodal generation."""
    text: Optional[str] = Field(None, description="Text prompt")
    image_parts: List[ImagePart] = Field(..., description="Image parts")
    system_instruction: Optional[str] = Field(None, description="System instruction for the model")
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    # JSON Mode support
    json_mode: Optional[bool] = Field(False, description="Enable JSON mode output")
    json_schema: Optional[Union[JSONSchema, Dict[str, Any]]] = Field(None, description="JSON schema for structured output")


class FileGenerationRequest(AIRequest, GenerationConfig):
    """Request model for file-based generation."""
    prompt: Optional[str] = Field(None, description="Text prompt")
    file_parts: List[FilePart] = Field(..., description="File parts")
    system_instruction: Optional[str] = Field(None, description="System instruction for the model")
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")


class FunctionCallingRequest(AIRequest, GenerationConfig):
    """Request model for function calling."""
    prompt: str = Field(..., description="Text prompt")
    functions: List[FunctionDeclaration] = Field(..., description="Available functions")
    system_instruction: Optional[str] = Field(None, description="System instruction for the model")
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")


class ChatMessage(BaseModel):
    """Chat message model."""
    role: str = Field(..., description="Role of the message sender (user or assistant)")
    content: str = Field(..., description="Content of the message")
    name: Optional[str] = Field(None, description="Name of the sender")


class ChatRequest(AIRequest, GenerationConfig):
    """Request model for chat completion."""
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    system_instruction: Optional[str] = Field(None, description="System instruction for the model")
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    # JSON Mode support
    json_mode: Optional[bool] = Field(False, description="Enable JSON mode output")
    json_schema: Optional[Union[JSONSchema, Dict[str, Any]]] = Field(None, description="JSON schema for structured output")


class EmbeddingRequest(AIRequest):
    """Request model for embedding generation."""
    text: str = Field(..., description="Text to embed")
    task_type: Optional[str] = Field("RETRIEVAL_QUERY", description="Task type for the embedding")
    title: Optional[str] = Field(None, description="Title for the embedding")
    output_dimensionality: Optional[int] = Field(None, description="Dimensionality of the output embedding")


class TokenCountRequest(AIRequest):
    """Request model for token counting."""
    text: str = Field(..., description="Text to count tokens for")


class VoiceCharacteristics(BaseModel):
    """Voice characteristics for advanced TTS control."""
    characteristic: Optional[str] = Field(
        default=None,
        description="Voice characteristic (soft, firm, breathy, bright, smooth, etc.)"
    )
    emotion: Optional[str] = Field(
        default=None,
        description="Emotional tone (happy, sad, excited, calm, wise, mysterious, etc.)"
    )
    age: Optional[str] = Field(
        default=None,
        description="Age characteristic (youthful, mature, elderly)"
    )
    personality: Optional[str] = Field(
        default=None,
        description="Personality trait (confident, gentle, authoritative, playful, etc.)"
    )

class SpeakerConfig(BaseModel):
    """Configuration for a single speaker in multi-speaker TTS."""
    speaker_name: str = Field(..., description="Name of the speaker (must match text)")
    voice: Optional[str] = Field(default=None, description="Specific voice name (overrides characteristics)")
    voice_characteristics: Optional[VoiceCharacteristics] = Field(
        default=None,
        description="Voice characteristics for automatic voice selection"
    )
    style_prompt: Optional[str] = Field(default=None, description="Style guidance for this speaker")

class TTSRequest(BaseModel):
    """Request model for text-to-speech generation with enhanced Vietnamese support."""
    text: str = Field(..., description="Text to convert to speech", max_length=5000)
    voice: Optional[str] = Field("vi-VN-Standard-A", description="Voice to use for synthesis")
    language_code: Optional[str] = Field("vi-VN", description="Language code (e.g., en-US, vi-VN)")
    audio_format: Optional[str] = Field("mp3", description="Audio format (mp3, wav, ogg) - defaults to MP3 for better compatibility")
    speaking_rate: Optional[float] = Field(0.85, description="Speaking rate optimized for Vietnamese (0.25 to 4.0)", ge=0.25, le=4.0)
    pitch: Optional[float] = Field(0.0, description="Pitch adjustment (-20.0 to 20.0)", ge=-20.0, le=20.0)
    volume_gain_db: Optional[float] = Field(0.0, description="Volume gain in dB (-96.0 to 16.0)", ge=-96.0, le=16.0)
    sample_rate: Optional[int] = Field(24000, description="Audio sample rate in Hz - higher quality for Vietnamese")
    effects_profile_id: Optional[List[str]] = Field(None, description="Audio effects profile IDs")

    # Advanced voice characteristics (Gemini Native TTS)
    voice_characteristics: Optional[VoiceCharacteristics] = Field(
        default=None,
        description="Voice characteristics for automatic voice selection"
    )
    emotional_tone: Optional[str] = Field(
        default=None,
        description="Emotional tone (happy, sad, excited, calm, wise, mysterious, etc.)"
    )
    narrative_style: Optional[str] = Field(
        default=None,
        description="Narrative style (storytelling, conversational, dramatic, educational, etc.)"
    )

    # Multi-speaker support for Gemini Native TTS
    speakers: Optional[List[SpeakerConfig]] = Field(default=None, description="Multi-speaker configuration")
    use_gemini_native: bool = Field(default=True, description="Use Gemini Native TTS (recommended)")
    style_prompt: Optional[str] = Field(default=None, description="Overall style guidance")


class UsageInfo(BaseModel):
    """Token usage information."""
    prompt_tokens: int = Field(0, description="Number of tokens in the prompt")
    completion_tokens: int = Field(0, description="Number of tokens in the completion")
    total_tokens: int = Field(0, description="Total number of tokens")


class TextGenerationResponse(BaseModel):
    """Response model for text generation."""
    text: str = Field(..., description="Generated text")
    model: str = Field(..., description="Model used for generation")
    usage: UsageInfo = Field(..., description="Token usage information")
    finish_reason: Optional[str] = Field(None, description="Reason for finishing generation")


class StreamChunk(BaseModel):
    """Chunk of a streaming response."""
    text: str = Field(..., description="Text in this chunk")
    is_final: bool = Field(False, description="Whether this is the final chunk")


class ChatResponse(BaseModel):
    """Response model for chat completion."""
    message: ChatMessage = Field(..., description="Generated message")
    model: str = Field(..., description="Model used for generation")
    usage: UsageInfo = Field(..., description="Token usage information")
    finish_reason: Optional[str] = Field(None, description="Reason for finishing generation")


class FunctionExecutionResult(BaseModel):
    """Function execution result."""
    success: bool = Field(..., description="Whether function execution was successful")
    result: Optional[Any] = Field(None, description="Function execution result")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    execution_time: Optional[float] = Field(None, description="Function execution time in seconds")
    function_name: str = Field(..., description="Name of the executed function")


class FunctionCallingResponse(BaseModel):
    """Response model for function calling."""
    text: Optional[str] = Field(None, description="Generated text")
    function_call: Optional[FunctionCall] = Field(None, description="Function call information")
    function_execution: Optional[FunctionExecutionResult] = Field(None, description="Function execution result")
    model: str = Field(..., description="Model used for generation")
    usage: UsageInfo = Field(..., description="Token usage information")


class EmbeddingResponse(BaseModel):
    """Response model for embedding generation."""
    embedding: List[float] = Field(..., description="Generated embedding")
    model: str = Field(..., description="Model used for generation")


class TokenCountResponse(BaseModel):
    """Response model for token counting."""
    token_count: int = Field(..., description="Number of tokens")
    model: str = Field(..., description="Model used for counting")


class TTSResponse(BaseModel):
    """Response model for text-to-speech generation."""
    audio_content: bytes = Field(..., description="Generated audio content")
    audio_format: str = Field(..., description="Audio format (mp3, wav, ogg)")
    sample_rate: int = Field(..., description="Audio sample rate in Hz")
    duration_seconds: Optional[float] = Field(None, description="Audio duration in seconds")
    voice_used: str = Field(..., description="Voice that was used for synthesis")
    language_code: str = Field(..., description="Language code used")
    text_length: int = Field(..., description="Length of input text")

    class Config:
        arbitrary_types_allowed = True


# Context Caching Models
class CacheContent(BaseModel):
    """Content to be cached."""
    role: str = Field(..., description="Role of the content (user, model, system)")
    parts: List[Dict[str, Any]] = Field(..., description="Content parts")


class CacheCreateRequest(BaseModel):
    """Request model for creating a context cache."""
    contents: List[CacheContent] = Field(..., description="Contents to cache")
    model: str = Field(..., description="Model to use for caching")
    system_instruction: Optional[str] = Field(None, description="System instruction")
    ttl: Optional[int] = Field(3600, description="Time to live in seconds (default: 1 hour)")
    display_name: Optional[str] = Field(None, description="Display name for the cache")


class CacheUpdateRequest(BaseModel):
    """Request model for updating a context cache."""
    ttl: Optional[int] = Field(None, description="New time to live in seconds")
    display_name: Optional[str] = Field(None, description="New display name")


class CacheInfo(BaseModel):
    """Context cache information."""
    name: str = Field(..., description="Cache name/ID")
    model: str = Field(..., description="Model used for caching")
    display_name: Optional[str] = Field(None, description="Display name")
    usage_metadata: Dict[str, Any] = Field(..., description="Usage metadata")
    create_time: datetime = Field(..., description="Cache creation time")
    update_time: datetime = Field(..., description="Cache last update time")
    expire_time: datetime = Field(..., description="Cache expiration time")


class CacheResponse(BaseModel):
    """Response model for cache operations."""
    cache: CacheInfo = Field(..., description="Cache information")
    success: bool = Field(..., description="Operation success status")
    message: Optional[str] = Field(None, description="Operation message")


class CacheListResponse(BaseModel):
    """Response model for listing caches."""
    caches: List[CacheInfo] = Field(..., description="List of caches")
    total_count: int = Field(..., description="Total number of caches")
    next_page_token: Optional[str] = Field(None, description="Token for next page")


class CachedGenerationRequest(BaseModel):
    """Request model for generation using cached content."""
    cache_name: str = Field(..., description="Name of the cache to use")
    prompt: str = Field(..., description="Additional prompt text")
    model: Optional[str] = Field(None, description="Model to use (overrides cache model)")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(0.7, description="Temperature for generation")
    top_p: Optional[float] = Field(None, description="Top-p sampling parameter")
    top_k: Optional[int] = Field(None, description="Top-k sampling parameter")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Error details")


# Image Generation Models
class AspectRatio(str, Enum):
    """Supported aspect ratios for image generation."""
    SQUARE = "1:1"
    FULLSCREEN = "4:3"
    PORTRAIT_FULLSCREEN = "3:4"
    WIDESCREEN = "16:9"
    PORTRAIT = "9:16"


class PersonGeneration(str, Enum):
    """Person generation settings for Imagen."""
    DONT_ALLOW = "dont_allow"
    ALLOW_ADULT = "allow_adult"
    ALLOW_ALL = "allow_all"


class ImageGenerationModel(str, Enum):
    """Available image generation models."""
    GEMINI_NATIVE = "gemini-2.0-flash-preview-image-generation"
    IMAGEN_3 = "imagen-3.0-generate-002"


class ImageGenerationRequest(BaseModel):
    """Request model for image generation."""
    prompt: str = Field(..., description="Text prompt for image generation", max_length=480)
    model: ImageGenerationModel = Field(
        ImageGenerationModel.GEMINI_NATIVE,
        description="Image generation model to use"
    )

    # Common parameters
    number_of_images: Optional[int] = Field(1, description="Number of images to generate (1-4)", ge=1, le=4)
    aspect_ratio: Optional[AspectRatio] = Field(AspectRatio.SQUARE, description="Aspect ratio of generated images")

    # Imagen 3 specific parameters
    person_generation: Optional[PersonGeneration] = Field(
        PersonGeneration.ALLOW_ADULT,
        description="Person generation setting (Imagen 3 only)"
    )

    # Gemini Native specific parameters
    input_images: Optional[List[ImagePart]] = Field(None, description="Input images for editing (Gemini Native only)")
    response_modalities: Optional[List[str]] = Field(
        ["TEXT", "IMAGE"],
        description="Response modalities for Gemini Native"
    )

    # Generation config
    temperature: Optional[float] = Field(0.7, description="Temperature for generation", ge=0.0, le=2.0)
    top_p: Optional[float] = Field(0.95, description="Top-p for nucleus sampling", ge=0.0, le=1.0)
    top_k: Optional[int] = Field(40, description="Top-k for sampling", ge=1, le=100)

    # Safety settings
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")

    # Advanced options
    style_prompt: Optional[str] = Field(None, description="Style guidance for image generation")
    negative_prompt: Optional[str] = Field(None, description="What to avoid in the image")
    seed: Optional[int] = Field(None, description="Seed for reproducible generation")


class GeneratedImage(BaseModel):
    """A single generated image."""
    image_data: bytes = Field(..., description="Image data as bytes")
    format: str = Field("png", description="Image format")
    width: Optional[int] = Field(None, description="Image width in pixels")
    height: Optional[int] = Field(None, description="Image height in pixels")
    size_bytes: Optional[int] = Field(None, description="Image size in bytes")

    class Config:
        arbitrary_types_allowed = True


class ImageGenerationResponse(BaseModel):
    """Response model for image generation."""
    images: List[GeneratedImage] = Field(..., description="Generated images")
    model: str = Field(..., description="Model used for generation")
    prompt_used: str = Field(..., description="Actual prompt used for generation")

    # Generation metadata
    generation_time: Optional[float] = Field(None, description="Generation time in seconds")
    aspect_ratio: Optional[str] = Field(None, description="Aspect ratio used")
    number_of_images: int = Field(..., description="Number of images generated")

    # Text response (for Gemini Native)
    text_response: Optional[str] = Field(None, description="Text response from model")

    # Safety and filtering
    safety_ratings: Optional[List[Dict[str, Any]]] = Field(None, description="Safety ratings")
    content_filtered: Optional[bool] = Field(False, description="Whether content was filtered")

    # Usage information
    usage: Optional[UsageInfo] = Field(None, description="Token usage information")

    class Config:
        arbitrary_types_allowed = True


class ImageEditingRequest(BaseModel):
    """Request model for image editing with Gemini Native."""
    prompt: str = Field(..., description="Text prompt for image editing")
    input_images: List[ImagePart] = Field(..., description="Input images to edit")
    model: str = Field(
        ImageGenerationModel.GEMINI_NATIVE.value,
        description="Model to use (only Gemini Native supports editing)"
    )

    # Generation parameters
    temperature: Optional[float] = Field(0.7, description="Temperature for generation", ge=0.0, le=2.0)
    top_p: Optional[float] = Field(0.95, description="Top-p for nucleus sampling", ge=0.0, le=1.0)
    top_k: Optional[int] = Field(40, description="Top-k for sampling", ge=1, le=100)

    # Safety settings
    safety_settings: Optional[List[SafetySetting]] = Field(None, description="Safety settings")

    # Advanced options
    style_prompt: Optional[str] = Field(None, description="Style guidance for editing")
    preserve_style: Optional[bool] = Field(True, description="Whether to preserve original image style")


class BatchImageGenerationRequest(BaseModel):
    """Request model for batch image generation."""
    requests: List[ImageGenerationRequest] = Field(
        ...,
        description="List of image generation requests",
        max_items=10
    )
    parallel_processing: Optional[bool] = Field(True, description="Whether to process requests in parallel")
    max_concurrent: Optional[int] = Field(3, description="Maximum concurrent generations", ge=1, le=5)


class BatchImageGenerationResponse(BaseModel):
    """Response model for batch image generation."""
    results: List[ImageGenerationResponse] = Field(..., description="Generation results")
    total_requests: int = Field(..., description="Total number of requests")
    successful_requests: int = Field(..., description="Number of successful requests")
    failed_requests: int = Field(..., description="Number of failed requests")
    total_generation_time: Optional[float] = Field(None, description="Total generation time in seconds")
    errors: Optional[List[Dict[str, Any]]] = Field(None, description="Errors for failed requests")
