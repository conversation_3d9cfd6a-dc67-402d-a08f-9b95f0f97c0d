# Image Generation Feature

## Overview

The AI Service now supports comprehensive image generation capabilities through both **Gemini Native Image Generation** and **Imagen 3**, providing a complete multimodal AI platform with text-to-image generation, image editing, and batch processing.

## Supported Models

### 1. Gemini Native Image Generation
- **Model**: `gemini-2.0-flash-preview-image-generation`
- **Capabilities**: 
  - Text-to-image generation
  - Image editing with input images
  - Multimodal responses (text + images)
  - Advanced generation controls

### 2. Imagen 3
- **Model**: `imagen-3.0-generate-002`
- **Capabilities**:
  - High-quality text-to-image generation
  - Person generation controls
  - Reproducible generation with seeds
  - Multiple aspect ratios

## API Endpoints

### 1. Image Generation
```http
POST /api/v1/ai/image-generation
```

Generate images from text prompts using either Gemini Native or Imagen 3.

**Request Body:**
```json
{
  "prompt": "A beautiful sunset over mountains",
  "model": "gemini-2.0-flash-preview-image-generation",
  "number_of_images": 1,
  "aspect_ratio": "16:9",
  "temperature": 0.7,
  "style_prompt": "Digital art style, vibrant colors"
}
```

**Response:**
```json
{
  "images": [
    {
      "image_data": "<base64_encoded_image>",
      "format": "png",
      "size_bytes": 1024000
    }
  ],
  "model": "gemini-2.0-flash-preview-image-generation",
  "prompt_used": "A beautiful sunset over mountains",
  "number_of_images": 1,
  "generation_time": 3.5
}
```

### 2. Image Editing
```http
POST /api/v1/ai/image-editing
```

Edit existing images using Gemini Native Image Generation.

**Request Body:**
```json
{
  "prompt": "Add a rainbow in the sky",
  "input_images": [
    {
      "type": "base64",
      "data": "<base64_encoded_image>",
      "mime_type": "image/png"
    }
  ],
  "temperature": 0.7,
  "style_prompt": "Keep original style"
}
```

### 3. Batch Image Generation
```http
POST /api/v1/ai/batch/image-generation
```

Generate multiple sets of images in parallel or sequentially.

**Request Body:**
```json
{
  "requests": [
    {
      "prompt": "A cat sitting on a windowsill",
      "model": "gemini-2.0-flash-preview-image-generation"
    },
    {
      "prompt": "A dog playing in a park",
      "model": "imagen-3.0-generate-002"
    }
  ],
  "parallel_processing": true,
  "max_concurrent": 3
}
```

## Parameters

### Common Parameters
- **prompt** (required): Text description of the image to generate (max 480 characters)
- **model**: Image generation model to use
- **number_of_images**: Number of images to generate (1-4)
- **aspect_ratio**: Image aspect ratio (`1:1`, `4:3`, `3:4`, `16:9`, `9:16`)

### Gemini Native Specific
- **input_images**: Input images for editing
- **temperature**: Generation creativity (0.0-2.0)
- **top_p**: Nucleus sampling parameter
- **top_k**: Top-k sampling parameter
- **style_prompt**: Style guidance
- **response_modalities**: Output types (`["TEXT", "IMAGE"]`)

### Imagen 3 Specific
- **person_generation**: Person generation setting (`dont_allow`, `allow_adult`, `allow_all`)
- **seed**: Seed for reproducible generation

## Usage Examples

### Python Client Example

```python
import asyncio
from ai_service.models.schemas import ImageGenerationRequest, ImageGenerationModel
from ai_service.services.ai_service import AIService

async def generate_image():
    ai_service = AIService()
    
    request = ImageGenerationRequest(
        prompt="A futuristic city with flying cars",
        model=ImageGenerationModel.GEMINI_NATIVE,
        number_of_images=1,
        aspect_ratio="16:9",
        temperature=0.8,
        style_prompt="Cyberpunk style, neon colors"
    )
    
    response = await ai_service.generate_images(request)
    
    # Save the generated image
    with open("generated_image.png", "wb") as f:
        f.write(response.images[0].image_data)
    
    print(f"Generated {len(response.images)} images")

asyncio.run(generate_image())
```

### cURL Example

```bash
curl -X POST "http://localhost:8000/api/v1/ai/image-generation" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A serene lake surrounded by mountains",
    "model": "gemini-2.0-flash-preview-image-generation",
    "number_of_images": 1,
    "aspect_ratio": "16:9",
    "temperature": 0.7
  }'
```

## Best Practices

### 1. Prompt Engineering
- **Be specific**: Include details about style, composition, and mood
- **Use descriptive language**: "vibrant sunset" vs "sunset"
- **Specify art style**: "digital art", "oil painting", "photograph"
- **Include lighting**: "soft lighting", "dramatic shadows"

### 2. Model Selection
- **Gemini Native**: Best for image editing and multimodal responses
- **Imagen 3**: Best for high-quality standalone image generation

### 3. Performance Optimization
- Use batch processing for multiple images
- Set appropriate concurrency limits
- Consider caching for repeated requests

### 4. Error Handling
```python
try:
    response = await ai_service.generate_images(request)
except ValidationError as e:
    print(f"Invalid request: {e.message}")
except APIError as e:
    print(f"API error: {e.message}")
```

## Limitations

### Gemini Native
- Requires specific model availability
- Image editing limited to supported formats
- Generation time varies with complexity

### Imagen 3
- 480 character prompt limit
- Limited to text-to-image (no editing)
- Person generation controls required

## Testing

Run the comprehensive test suite:

```bash
python test_image_generation.py
```

This will test:
- Gemini Native image generation
- Imagen 3 image generation  
- Image editing capabilities
- Batch processing

## Architecture

The image generation feature follows the established clean architecture:

```
API Layer (ai_routes.py)
    ↓
Service Layer (ai_service.py)
    ↓
Core Layer (ai_client.py)
    ↓
Google Gemini API / Imagen 3 API
```

### Key Components

1. **Schemas** (`models/schemas.py`): Request/response models
2. **Validators** (`core/validators.py`): Input validation
3. **AI Client** (`core/ai_client.py`): API integration
4. **Service** (`services/ai_service.py`): Business logic
5. **Routes** (`api/ai_routes.py`): HTTP endpoints

## Security Considerations

- Input validation for all parameters
- File size limits for uploaded images
- Rate limiting for batch requests
- Content filtering through safety settings
- Secure handling of image data

## Future Enhancements

- [ ] Streaming image generation
- [ ] Image-to-image translation
- [ ] Style transfer capabilities
- [ ] Advanced composition controls
- [ ] Integration with image storage services
- [ ] Real-time image editing
- [ ] Custom model fine-tuning support
